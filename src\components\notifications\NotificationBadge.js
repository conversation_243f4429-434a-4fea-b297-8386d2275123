import React, { useState, useEffect } from 'react';

/**
 * NotificationBadge Component
 * Displays a badge with unread notification count
 * Follows the existing cyber theme styling
 */
const NotificationBadge = ({ userId, className = '' }) => {
    const [unreadCount, setUnreadCount] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (!userId) {
            setUnreadCount(0);
            setIsLoading(false);
            return;
        }

        // Initial load of unread count
        const loadUnreadCount = async () => {
            try {
                if (window.notificationService) {
                    const notifications = await window.notificationService.getUserNotifications(userId, 100, true);
                    setUnreadCount(notifications.length);
                } else {
                    setUnreadCount(0);
                }
            } catch (error) {
                console.error('Error loading unread notification count:', error);
                setUnreadCount(0);
            } finally {
                setIsLoading(false);
            }
        };

        loadUnreadCount();

        // For now, we'll poll for updates every 30 seconds
        // In a full implementation, you'd want real-time subscriptions
        const interval = setInterval(loadUnreadCount, 30000);

        const unsubscribe = () => {
            clearInterval(interval);
        };

        return () => {
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, [userId]);

    // Don't show badge if no unread notifications or still loading
    if (isLoading || unreadCount === 0) {
        return null;
    }

    // Limit display to 99+ for large numbers
    const displayCount = unreadCount > 99 ? '99+' : unreadCount.toString();

    return (
        <span 
            className={`absolute top-0.5 right-0.5 block h-2.5 w-2.5 rounded-full ring-2 ring-offset-1 ring-offset-[var(--bg-deep-space)] ring-red-500 bg-red-400 ${className}`}
            style={{ 
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: unreadCount > 9 ? '20px' : '10px',
                height: unreadCount > 9 ? '20px' : '10px',
                fontSize: unreadCount > 9 ? '8px' : '0px',
                fontWeight: 'bold',
                color: 'white'
            }}
            title={`${unreadCount} unread notification${unreadCount !== 1 ? 's' : ''}`}
        >
            {unreadCount > 9 ? displayCount : ''}
        </span>
    );
};

export default NotificationBadge;
