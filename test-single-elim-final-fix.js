const { generateBracket } = require('./functions/bracketGenerator');

// Test single elimination bracket with 4 participants
const participants = [
    { id: '0x1234567890abcdef1234567890abcdef12345678', name: '<PERSON>', seed: 1 },
    { id: '0x2345678901bcdef12345678901bcdef123456789', name: 'Bravo', seed: 2 },
    { id: '0x3456789012cdef123456789012cdef12345678ab', name: '<PERSON>', seed: 3 },
    { id: '0x456789013def123456789013def12345678abcd', name: '<PERSON>', seed: 4 }
];

console.log('Testing Single Elimination Bracket Generation...');
console.log('Participants:', participants.map(p => p.name).join(', '));

try {
    const bracket = generateBracket(participants, 'single-elim', 'bo1');
    
    console.log('\n=== BRACKET STRUCTURE ===');
    bracket.rounds.forEach((round, index) => {
        console.log(`\nRound ${index + 1}: ${round.name}`);
        round.matches.forEach(match => {
            const p1 = bracket.participants.find(p => p.id === match.participant1Id);
            const p2 = bracket.participants.find(p => p.id === match.participant2Id);
            
            console.log(`  ${match.identifier || match.id}:`);
            console.log(`    Participant 1: ${p1 ? p1.name : match.participant1Id || 'null'}`);
            console.log(`    Participant 2: ${p2 ? p2.name : match.participant2Id || 'null'}`);
            console.log(`    Status: ${match.status}`);
            console.log(`    Next Match: ${match.nextMatchId || 'none'}`);
        });
    });
    
    // Check final match specifically
    const finalRound = bracket.rounds[bracket.rounds.length - 1];
    const finalMatch = finalRound.matches[0];
    
    console.log('\n=== FINAL MATCH ANALYSIS ===');
    console.log(`Final Match ID: ${finalMatch.id}`);
    console.log(`Participant 1 ID: ${finalMatch.participant1Id}`);
    console.log(`Participant 2 ID: ${finalMatch.participant2Id}`);
    console.log(`Status: ${finalMatch.status}`);
    
    // Verify both participants are TBD placeholders
    const isTBD1 = finalMatch.participant1Id && finalMatch.participant1Id.startsWith('TBD_');
    const isTBD2 = finalMatch.participant2Id && finalMatch.participant2Id.startsWith('TBD_');
    
    console.log(`Participant 1 is TBD: ${isTBD1}`);
    console.log(`Participant 2 is TBD: ${isTBD2}`);
    
    if (isTBD1 && isTBD2) {
        console.log('\n✅ SUCCESS: Final match correctly has both participants as TBD placeholders');
    } else {
        console.log('\n❌ ISSUE: Final match should have both participants as TBD placeholders');
    }
    
} catch (error) {
    console.error('Error generating bracket:', error);
}
