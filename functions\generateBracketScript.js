const admin = require('firebase-admin');
const { generateBracket } = require('./bracketGenerator');

// Initialize Firebase Admin with service account credentials
const serviceAccount = require('../cyberfield-2e321-firebase-adminsdk-fbsvc-58fd78d24e.json');
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'cyberfield-2e321'
});

async function generateBracketForTournament(tournamentId) {
  try {
    // Get the tournament document
    const tournamentDoc = await admin.firestore()
      .collection('tournaments')
      .doc(tournamentId)
      .get();

    if (!tournamentDoc.exists) {
      throw new Error('Tournament not found');
    }

    const tournamentData = tournamentDoc.data();
    console.log('Tournament data:', tournamentData);

    // Check if tournament has participants
    if (!tournamentData.participants || tournamentData.participants.length === 0) {
      throw new Error('Tournament has no participants');
    }

    // Validate that all participants are wallet addresses (not Firebase UIDs)
    const validParticipants = tournamentData.participants.filter(participant => {
      // Wallet addresses are 42 characters long and start with 0x
      return typeof participant === 'string' &&
             participant.length === 42 &&
             participant.startsWith('0x');
    });

    if (validParticipants.length !== tournamentData.participants.length) {
      console.warn(`Tournament ${tournamentId}: Found ${tournamentData.participants.length - validParticipants.length} invalid participant IDs (likely Firebase UIDs). Only using ${validParticipants.length} valid wallet addresses.`);
    }

    if (validParticipants.length === 0) {
      throw new Error('No valid wallet addresses found in participants');
    }

    // Prepare participants for bracket generation
    const participants = validParticipants.map((address, index) => ({
      id: address.toLowerCase(), // Ensure consistent lowercase
      name: address,
      seed: index + 1
    }));

    console.log('Participants:', participants);

    // Determine the bracket format for team tournaments
    let bracketFormat = tournamentData.tournamentFormat || 'single-elim';

    // For team tournaments (2v2, 3v3), use the bracket format or default to single-elimination
    if (tournamentData.tournamentFormat === '2v2' || tournamentData.tournamentFormat === '3v3') {
        // Use bracketFormat field if specified, otherwise default to single-elimination
        bracketFormat = tournamentData.bracketFormat || 'single-elim';
    }

    // Generate bracket data using the determined bracket format
    const bracketData = generateBracket(participants, bracketFormat);
    console.log('Generated bracket data:', bracketData);

    // Update tournament document with bracket data
    await tournamentDoc.ref.update({
      bracketData: bracketData
    });

    console.log('Successfully updated tournament with bracket data');
    return bracketData;
  } catch (error) {
    console.error('Error generating bracket:', error);
    throw error;
  }
}

// Get tournament ID from command line argument
const tournamentId = process.argv[2];
if (!tournamentId) {
  console.error('Please provide a tournament ID as a command line argument');
  process.exit(1);
}

// Run the function
generateBracketForTournament(tournamentId)
  .then(() => {
    console.log('Bracket generation completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Failed to generate bracket:', error);
    process.exit(1);
  });