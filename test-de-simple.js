// Simple test for double elimination bracket
const { generateBracket } = require('./functions/bracketGenerator');

console.log('Testing Double Elimination Bracket...\n');

const testParticipants = [
    { id: 'player1', name: 'Player 1', seed: 1 },
    { id: 'player2', name: 'Player 2', seed: 2 },
    { id: 'player3', name: 'Player 3', seed: 3 },
    { id: 'player4', name: 'Player 4', seed: 4 }
];

try {
    const bracket = generateBracket(testParticipants, 'double-elimination');
    
    console.log('✅ Double elimination bracket generated successfully!');
    console.log('Format:', bracket.format);
    console.log('Total Rounds:', bracket.rounds.length);
    console.log('Total Matches:', Object.keys(bracket.matchesById).length);
    console.log('Upper Bracket Rounds:', bracket.metadata.numUpperRounds);
    console.log('Lower Bracket Rounds:', bracket.metadata.numLowerRounds);
    
    // Check for proper linking
    let linkErrors = 0;
    let hasLoserLinks = 0;
    
    Object.values(bracket.matchesById).forEach(match => {
        if (match.nextLoserMatchId) {
            hasLoserLinks++;
            if (!bracket.matchesById[match.nextLoserMatchId]) {
                linkErrors++;
                console.error(`❌ Invalid loser link: ${match.id} -> ${match.nextLoserMatchId}`);
            }
        }
    });
    
    console.log(`\nLinking Analysis:`);
    console.log(`- Matches with loser links: ${hasLoserLinks}`);
    console.log(`- Link errors: ${linkErrors}`);
    
    if (linkErrors === 0 && hasLoserLinks > 0) {
        console.log('✅ Double elimination linking appears to be working!');
    } else if (hasLoserLinks === 0) {
        console.log('⚠️  No loser links found - this might be an issue');
    }
    
} catch (error) {
    console.error('❌ Error:', error.message);
}
