/*! For license information please see 709.admin-bundle.js.LICENSE.txt */
"use strict";(this.webpackChunkAdminComponents=this.webpackChunkAdminComponents||[]).push([[709],{709:(t,r,e)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,r){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);r&&(n=n.filter((function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),e.push.apply(e,n)}return e}function i(t){for(var r=1;r<arguments.length;r++){var e=null!=arguments[r]?arguments[r]:{};r%2?o(Object(e),!0).forEach((function(r){a(t,r,e[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):o(Object(e)).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}))}return t}function a(t,r,e){return(r=function(t){var r=function(t){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var e=r.call(t,"string");if("object"!=n(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==n(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[r]=e,t}function c(){c=function(){return r};var t,r={},e=Object.prototype,o=e.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(t,r,e,n){return Object.defineProperty(t,r,{value:e,enumerable:!n,configurable:!n,writable:!n})}try{f({},"")}catch(t){f=function(t,r,e){return t[r]=e}}function p(r,e,n,o){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype);return f(a,"_invoke",function(r,e,n){var o=1;return function(i,a){if(3===o)throw Error("Generator is already running");if(4===o){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var c=n.delegate;if(c){var u=E(c,n);if(u){if(u===l)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(1===o)throw o=4,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=3;var s=h(r,e,n);if("normal"===s.type){if(o=n.done?4:2,s.arg===l)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=4,n.method="throw",n.arg=s.arg)}}}(r,n,new j(o||[])),!0),a}function h(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}r.wrap=p;var l={};function d(){}function m(){}function v(){}var y={};f(y,a,(function(){return this}));var g=Object.getPrototypeOf,w=g&&g(g(A([])));w&&w!==e&&o.call(w,a)&&(y=w);var b=v.prototype=d.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(r){f(t,r,(function(t){return this._invoke(r,t)}))}))}function N(t,r){function e(i,a,c,u){var s=h(t[i],t,a);if("throw"!==s.type){var f=s.arg,p=f.value;return p&&"object"==n(p)&&o.call(p,"__await")?r.resolve(p.__await).then((function(t){e("next",t,c,u)}),(function(t){e("throw",t,c,u)})):r.resolve(p).then((function(t){f.value=t,c(f)}),(function(t){return e("throw",t,c,u)}))}u(s.arg)}var i;f(this,"_invoke",(function(t,n){function o(){return new r((function(r,o){e(t,n,r,o)}))}return i=i?i.then(o,o):o()}),!0)}function E(r,e){var n=e.method,o=r.i[n];if(o===t)return e.delegate=null,"throw"===n&&r.i.return&&(e.method="return",e.arg=t,E(r,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),l;var i=h(o,r.i,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,l;var a=i.arg;return a?a.done?(e[r.r]=a.value,e.next=r.n,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,l):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function k(t){this.tryEntries.push(t)}function O(r){var e=r[4]||{};e.type="normal",e.arg=t,r[4]=e}function j(t){this.tryEntries=[[-1]],t.forEach(k,this),this.reset(!0)}function A(r){if(null!=r){var e=r[a];if(e)return e.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var i=-1,c=function e(){for(;++i<r.length;)if(o.call(r,i))return e.value=r[i],e.done=!1,e;return e.value=t,e.done=!0,e};return c.next=c}}throw new TypeError(n(r)+" is not iterable")}return m.prototype=v,f(b,"constructor",v),f(v,"constructor",m),m.displayName=f(v,s,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===m||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,f(t,s,"GeneratorFunction")),t.prototype=Object.create(b),t},r.awrap=function(t){return{__await:t}},x(N.prototype),f(N.prototype,u,(function(){return this})),r.AsyncIterator=N,r.async=function(t,e,n,o,i){void 0===i&&(i=Promise);var a=new N(p(t,e,n,o),i);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},x(b),f(b,s,"Generator"),f(b,a,(function(){return this})),f(b,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=Object(t),e=[];for(var n in r)e.unshift(n);return function t(){for(;e.length;)if((n=e.pop())in r)return t.value=n,t.done=!1,t;return t.done=!0,t}},r.values=A,j.prototype={constructor:j,reset:function(r){if(this.prev=this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!r)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var e=this;function n(t){a.type="throw",a.arg=r,e.next=t}for(var o=e.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i[4],c=this.prev,u=i[1],s=i[2];if(-1===i[0])return n("end"),!1;if(!u&&!s)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=c){if(c<u)return this.method="next",this.arg=t,n(u),!0;if(c<s)return n(s),!1}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[0]>-1&&n[0]<=this.prev&&this.prev<n[2]){var o=n;break}}o&&("break"===t||"continue"===t)&&o[0]<=r&&r<=o[2]&&(o=null);var i=o?o[4]:{};return i.type=t,i.arg=r,o?(this.method="next",this.next=o[2],l):this.complete(i)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),l},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e[2]===t)return this.complete(e[4],e[3]),O(e),l}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e[0]===t){var n=e[4];if("throw"===n.type){var o=n.arg;O(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(r,e,n){return this.delegate={i:A(r),r:e,n},"next"===this.method&&(this.arg=t),l}},r}function u(t,r,e,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void e(t)}c.done?r(u):Promise.resolve(u).then(n,o)}function s(t){return function(){var r=this,e=arguments;return new Promise((function(n,o){var i=t.apply(r,e);function a(t){u(i,n,o,a,c,"next",t)}function c(t){u(i,n,o,a,c,"throw",t)}a(void 0)}))}}e.r(r),e.d(r,{createMatchDisputeNotification:()=>g,createNotification:()=>f,createTournamentLiveNotification:()=>y,createTournamentRegistrationClosedNotification:()=>v,createUpcomingMatchNotification:()=>w,getUnreadNotificationCount:()=>d,getUserNotifications:()=>p,markAllNotificationsAsRead:()=>l,markNotificationAsRead:()=>h,subscribeToNotifications:()=>m});var f=function(){var t=s(c().mark((function t(r,e,n,o){var i,a,u,s,f=arguments;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=f.length>4&&void 0!==f[4]?f[4]:{},t.prev=1,a=getDb(),u={userId:r.toLowerCase(),type:e,title:n,message:o,data:i,isRead:!1,createdAt:window.Timestamp.now(),expiresAt:window.Timestamp.fromDate(new Date(Date.now()+2592e6))},t.next=6,window.addDoc(window.collection(a,"notifications"),u);case 6:return s=t.sent,console.log("Notification created:",s.id),t.abrupt("return",s.id);case 11:throw t.prev=11,t.t0=t.catch(1),console.error("Error creating notification:",t.t0),t.t0;case 15:case"end":return t.stop()}}),t,null,[[1,11]])})));return function(r,e,n,o){return t.apply(this,arguments)}}(),p=function(){var t=s(c().mark((function t(r){var e,n,o,a,u,s,f,p=arguments;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=p.length>1&&void 0!==p[1]?p[1]:50,n=p.length>2&&void 0!==p[2]&&p[2],t.prev=2,o=getDb(),a=collection(o,"notifications"),u=query(a,where("userId","==",r.toLowerCase()),orderBy("createdAt","desc"),limit(e)),n&&(u=query(a,where("userId","==",r.toLowerCase()),where("isRead","==",!1),orderBy("createdAt","desc"),limit(e))),t.next=9,getDocs(u);case 9:return s=t.sent,f=[],s.forEach((function(t){var r;f.push(i(i({id:t.id},t.data()),{},{createdAt:(null===(r=t.data().createdAt)||void 0===r?void 0:r.toDate())||new Date}))})),t.abrupt("return",f);case 15:throw t.prev=15,t.t0=t.catch(2),console.error("Error fetching notifications:",t.t0),t.t0;case 19:case"end":return t.stop()}}),t,null,[[2,15]])})));return function(r){return t.apply(this,arguments)}}(),h=function(){var t=s(c().mark((function t(r){var e,n;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e=getDb(),n=doc(e,"notifications",r),t.next=5,updateDoc(n,{isRead:!0,readAt:Timestamp.now()});case 5:console.log("Notification marked as read:",r),t.next=12;break;case 8:throw t.prev=8,t.t0=t.catch(0),console.error("Error marking notification as read:",t.t0),t.t0;case 12:case"end":return t.stop()}}),t,null,[[0,8]])})));return function(r){return t.apply(this,arguments)}}(),l=function(){var t=s(c().mark((function t(r){var e,n;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,p(r,100,!0);case 3:return e=t.sent,n=e.map((function(t){return h(t.id)})),t.next=7,Promise.all(n);case 7:console.log("All notifications marked as read for user:",r),t.next=14;break;case 10:throw t.prev=10,t.t0=t.catch(0),console.error("Error marking all notifications as read:",t.t0),t.t0;case 14:case"end":return t.stop()}}),t,null,[[0,10]])})));return function(r){return t.apply(this,arguments)}}(),d=function(){var t=s(c().mark((function t(r){var e;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,p(r,100,!0);case 3:return e=t.sent,t.abrupt("return",e.length);case 7:return t.prev=7,t.t0=t.catch(0),console.error("Error getting unread notification count:",t.t0),t.abrupt("return",0);case 11:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(r){return t.apply(this,arguments)}}(),m=function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:50;try{var n=getDb(),o=collection(n,"notifications"),a=query(o,where("userId","==",t.toLowerCase()),orderBy("createdAt","desc"),limit(e));return onSnapshot(a,(function(t){var e=[];t.forEach((function(t){var r;e.push(i(i({id:t.id},t.data()),{},{createdAt:(null===(r=t.data().createdAt)||void 0===r?void 0:r.toDate())||new Date}))})),r(e)}),(function(t){console.error("Error in notification subscription:",t)}))}catch(t){return console.error("Error setting up notification subscription:",t),function(){}}},v=function(){var t=s(c().mark((function t(r,e){return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",f(r,"tournament_registration_closed","Tournament Registration Closed",'Registration has closed for "'.concat(e.tournamentName,'"'),{tournamentId:e.id,tournamentName:e.tournamentName,tournamentFormat:e.tournamentFormat||e.bracketFormat,gameName:e.gameName}));case 1:case"end":return t.stop()}}),t)})));return function(r,e){return t.apply(this,arguments)}}(),y=function(){var t=s(c().mark((function t(r,e){return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",f(r,"tournament_live","Tournament Started",'"'.concat(e.tournamentName,'" is now live!'),{tournamentId:e.id,tournamentName:e.tournamentName,tournamentFormat:e.tournamentFormat||e.bracketFormat,gameName:e.gameName}));case 1:case"end":return t.stop()}}),t)})));return function(r,e){return t.apply(this,arguments)}}(),g=function(){var t=s(c().mark((function t(r,e,n){return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",f(r,"match_dispute","Match Dispute Requires Attention",'A match in "'.concat(e.tournamentName,'" has been disputed and needs admin review'),{tournamentId:e.id,tournamentName:e.tournamentName,matchId:n.id,matchIdentifier:n.identifier||n.id,participant1:n.participant1Name||n.participant1Id,participant2:n.participant2Name||n.participant2Id}));case 1:case"end":return t.stop()}}),t)})));return function(r,e,n){return t.apply(this,arguments)}}(),w=function(){var t=s(c().mark((function t(r,e,n){var o;return c().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=n.participant1Id===r?n.participant2Name||n.participant2Id:n.participant1Name||n.participant1Id,t.abrupt("return",f(r,"upcoming_match","Upcoming Match",'You have an upcoming match in "'.concat(e.tournamentName,'" against ').concat(o),{tournamentId:e.id,tournamentName:e.tournamentName,matchId:n.id,matchIdentifier:n.identifier||n.id,opponent:o,matchFormat:n.matchFormat}));case 2:case"end":return t.stop()}}),t)})));return function(r,e,n){return t.apply(this,arguments)}}()}}]);