const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}

/**
 * <PERSON>ript to clean up tournaments with mixed participant types
 * This will identify tournaments that have Firebase UIDs mixed with wallet addresses
 * and provide options to fix them
 */

async function analyzeParticipantTypes() {
    const db = admin.firestore();
    const tournamentsRef = db.collection('tournaments');
    
    try {
        const snapshot = await tournamentsRef.get();
        const results = [];
        
        for (const doc of snapshot.docs) {
            const tournament = doc.data();
            
            if (!tournament.participants || tournament.participants.length === 0) {
                continue;
            }
            
            const analysis = {
                tournamentId: doc.id,
                tournamentName: tournament.tournamentName,
                status: tournament.status,
                totalParticipants: tournament.participants.length,
                walletAddresses: [],
                firebaseUIDs: [],
                invalidEntries: []
            };
            
            tournament.participants.forEach(participant => {
                if (typeof participant === 'string') {
                    if (participant.length === 42 && participant.startsWith('0x')) {
                        analysis.walletAddresses.push(participant);
                    } else if (participant.length === 28) {
                        // Firebase UIDs are typically 28 characters
                        analysis.firebaseUIDs.push(participant);
                    } else {
                        analysis.invalidEntries.push(participant);
                    }
                } else {
                    analysis.invalidEntries.push(participant);
                }
            });
            
            // Only include tournaments with issues
            if (analysis.firebaseUIDs.length > 0 || analysis.invalidEntries.length > 0) {
                results.push(analysis);
            }
        }
        
        console.log('\n=== TOURNAMENT PARTICIPANT ANALYSIS ===');
        console.log(`Total tournaments analyzed: ${snapshot.size}`);
        console.log(`Tournaments with issues: ${results.length}`);
        
        results.forEach(result => {
            console.log(`\n--- Tournament: ${result.tournamentName} (${result.tournamentId}) ---`);
            console.log(`Status: ${result.status}`);
            console.log(`Total participants: ${result.totalParticipants}`);
            console.log(`Valid wallet addresses: ${result.walletAddresses.length}`);
            console.log(`Firebase UIDs: ${result.firebaseUIDs.length}`);
            console.log(`Invalid entries: ${result.invalidEntries.length}`);
            
            if (result.firebaseUIDs.length > 0) {
                console.log(`Firebase UIDs found: ${result.firebaseUIDs.join(', ')}`);
            }
            
            if (result.invalidEntries.length > 0) {
                console.log(`Invalid entries: ${result.invalidEntries.join(', ')}`);
            }
        });
        
        return results;
        
    } catch (error) {
        console.error('Error analyzing participant types:', error);
        throw error;
    }
}

/**
 * Clean up a specific tournament by removing Firebase UIDs and invalid entries
 * WARNING: This will permanently remove Firebase UID participants
 */
async function cleanupTournament(tournamentId, removeFirebaseUIDs = false) {
    const db = admin.firestore();
    const tournamentRef = db.collection('tournaments').doc(tournamentId);
    
    try {
        const doc = await tournamentRef.get();
        if (!doc.exists) {
            throw new Error('Tournament not found');
        }
        
        const tournament = doc.data();
        const originalParticipants = tournament.participants || [];
        
        // Filter to only keep valid wallet addresses
        const validParticipants = originalParticipants.filter(participant => {
            return typeof participant === 'string' && 
                   participant.length === 42 && 
                   participant.startsWith('0x');
        });
        
        if (removeFirebaseUIDs && validParticipants.length !== originalParticipants.length) {
            await tournamentRef.update({
                participants: validParticipants,
                participantCount: validParticipants.length
            });
            
            console.log(`Tournament ${tournamentId} cleaned up:`);
            console.log(`  Original participants: ${originalParticipants.length}`);
            console.log(`  Valid participants remaining: ${validParticipants.length}`);
            console.log(`  Removed: ${originalParticipants.length - validParticipants.length}`);
            
            return {
                success: true,
                originalCount: originalParticipants.length,
                cleanedCount: validParticipants.length,
                removedCount: originalParticipants.length - validParticipants.length
            };
        } else {
            console.log(`Tournament ${tournamentId} - no cleanup needed or removeFirebaseUIDs=false`);
            return {
                success: false,
                message: 'No cleanup needed or removeFirebaseUIDs flag not set'
            };
        }
        
    } catch (error) {
        console.error(`Error cleaning up tournament ${tournamentId}:`, error);
        throw error;
    }
}

/**
 * Regenerate brackets for tournaments with valid participants
 */
async function regenerateBracketsForCleanedTournaments() {
    const { generateBracket } = require('./bracketGenerator');
    const db = admin.firestore();
    
    try {
        const tournamentsRef = db.collection('tournaments');
        const snapshot = await tournamentsRef
            .where('status', 'in', ['registrationClosed', 'live'])
            .get();
        
        const batch = db.batch();
        const results = [];
        
        for (const doc of snapshot.docs) {
            const tournament = doc.data();
            
            if (!tournament.participants || tournament.participants.length === 0) {
                continue;
            }
            
            // Validate participants are wallet addresses
            const validParticipants = tournament.participants.filter(participant => {
                return typeof participant === 'string' && 
                       participant.length === 42 && 
                       participant.startsWith('0x');
            });
            
            if (validParticipants.length >= 2) {
                try {
                    const participants = validParticipants.map((address, index) => ({
                        id: address.toLowerCase(),
                        name: `Player ${index + 1}`,
                        seed: index + 1
                    }));
                    
                    const bracketData = generateBracket(
                        participants,
                        tournament.tournamentFormat || 'single-elim'
                    );
                    
                    batch.update(doc.ref, {
                        bracketData: bracketData,
                        participants: validParticipants,
                        participantCount: validParticipants.length
                    });
                    
                    results.push({
                        tournamentId: doc.id,
                        participantCount: validParticipants.length,
                        status: 'bracket_regenerated'
                    });
                    
                } catch (error) {
                    console.error(`Error generating bracket for tournament ${doc.id}:`, error);
                    results.push({
                        tournamentId: doc.id,
                        status: 'bracket_generation_failed',
                        error: error.message
                    });
                }
            } else {
                results.push({
                    tournamentId: doc.id,
                    status: 'insufficient_participants',
                    participantCount: validParticipants.length
                });
            }
        }
        
        await batch.commit();
        
        console.log('\n=== BRACKET REGENERATION RESULTS ===');
        results.forEach(result => {
            console.log(`Tournament ${result.tournamentId}: ${result.status} (${result.participantCount || 0} participants)`);
        });
        
        return results;
        
    } catch (error) {
        console.error('Error regenerating brackets:', error);
        throw error;
    }
}

// Export functions for use in other scripts
module.exports = {
    analyzeParticipantTypes,
    cleanupTournament,
    regenerateBracketsForCleanedTournaments
};

// If run directly, perform analysis
if (require.main === module) {
    analyzeParticipantTypes()
        .then(() => {
            console.log('\nAnalysis complete. To clean up tournaments, use:');
            console.log('  cleanupTournament(tournamentId, true)');
            console.log('To regenerate brackets after cleanup, use:');
            console.log('  regenerateBracketsForCleanedTournaments()');
            process.exit(0);
        })
        .catch(error => {
            console.error('Script failed:', error);
            process.exit(1);
        });
}
