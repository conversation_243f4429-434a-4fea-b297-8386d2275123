const functions = require('firebase-functions/v1');
const admin = require('firebase-admin');
const { generateBracket } = require('./bracketGenerator');

exports.scheduledTournamentStatusUpdate = functions.pubsub.schedule('every 5 minutes').onRun(async (context) => {
  const now = admin.firestore.Timestamp.now();
  const tournamentsRef = admin.firestore().collection('tournaments');

  // First, handle tournaments that need to close registration
  const registrationSnapshot = await tournamentsRef
    .where('status', '==', 'upcoming')
    .where('startDate', '<=', now)
    .get();

  if (!registrationSnapshot.empty) {
    const registrationBatch = admin.firestore().batch();

    for (const doc of registrationSnapshot.docs) {
      const tournament = doc.data();

      // Generate bracket if there are participants
      if (tournament.participants && tournament.participants.length > 0) {
        try {
          // Validate that all participants are wallet addresses (not Firebase UIDs)
          const validParticipants = tournament.participants.filter(participant => {
            // Wallet addresses are 42 characters long and start with 0x
            return typeof participant === 'string' &&
                   participant.length === 42 &&
                   participant.startsWith('0x');
          });

          if (validParticipants.length !== tournament.participants.length) {
            console.warn(`Tournament ${doc.id}: Found ${tournament.participants.length - validParticipants.length} invalid participant IDs (likely Firebase UIDs). Only using ${validParticipants.length} valid wallet addresses.`);
          }

          if (validParticipants.length === 0) {
            console.error(`Tournament ${doc.id}: No valid wallet addresses found in participants`);
            // Still update status even if bracket generation fails
            registrationBatch.update(doc.ref, {
              status: 'registrationClosed',
              registrationClosedAt: now
            });
            continue;
          }

          const participants = validParticipants.map((address, index) => ({
            id: address.toLowerCase(), // Ensure consistent lowercase
            name: `Player ${index + 1}`,
            seed: index + 1
          }));

          // Determine the bracket format for team tournaments
          let bracketFormat = tournament.tournamentFormat || 'single-elim';

          // For team tournaments (2v2, 3v3), use the bracket format or default to single-elimination
          if (tournament.tournamentFormat === '2v2' || tournament.tournamentFormat === '3v3') {
              // Use bracketFormat field if specified, otherwise default to single-elimination
              bracketFormat = tournament.bracketFormat || 'single-elim';
          }

          const bracketData = generateBracket(
            participants,
            bracketFormat
          );

          registrationBatch.update(doc.ref, {
            status: 'registrationClosed',
            registrationClosedAt: now,
            bracketData: bracketData
          });
        } catch (error) {
          console.error(`Error generating bracket for tournament ${doc.id}:`, error);
          // Still update status even if bracket generation fails
          registrationBatch.update(doc.ref, {
            status: 'registrationClosed',
            registrationClosedAt: now
          });
        }
      } else {
        // No participants, just update status
        registrationBatch.update(doc.ref, {
          status: 'registrationClosed',
          registrationClosedAt: now
        });
      }

      console.log(`Tournament ${doc.id} status set to registrationClosed`);
    }

    await registrationBatch.commit();
  }

  // Then, handle tournaments that should start (30 minutes after registration closed)
  const thirtyMinutesAgo = new admin.firestore.Timestamp(
    now.seconds - (30 * 60), // 30 minutes in seconds
    now.nanoseconds
  );

  const startSnapshot = await tournamentsRef
    .where('status', '==', 'registrationClosed')
    .where('registrationClosedAt', '<=', thirtyMinutesAgo)
    .get();

  if (!startSnapshot.empty) {
    const startBatch = admin.firestore().batch();
    startSnapshot.forEach(doc => {
      startBatch.update(doc.ref, {
        status: 'live',
        startedAt: now
      });
      console.log(`Tournament ${doc.id} status set to live`);
    });
    await startBatch.commit();
  }

  return null;
});