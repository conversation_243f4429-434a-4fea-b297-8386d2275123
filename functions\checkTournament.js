const admin = require('firebase-admin');

// Initialize Firebase Admin with default credentials
admin.initializeApp();

async function checkTournamentData() {
    const db = admin.firestore();
    const tournamentsRef = db.collection('tournaments');
    
    try {
        // Get tournaments that are in 'live' status
        const snapshot = await tournamentsRef
            .where('status', '==', 'live')
            .get();
            
        console.log('Live tournaments found:', snapshot.size);
        
        snapshot.forEach(doc => {
            const data = doc.data();
            console.log('\nTournament ID:', doc.id);
            console.log('Status:', data.status);
            console.log('Has bracket data:', !!data.bracketData);
            if (data.bracketData) {
                console.log('Bracket format:', data.bracketData.format);
                console.log('Number of rounds:', data.bracketData.rounds?.length);
            }
            console.log('Participants:', data.participants?.length || 0);
            console.log('Registration closed at:', data.registrationClosedAt?.toDate());
            console.log('Started at:', data.startedAt?.toDate());
        });
    } catch (error) {
        console.error('Error fetching tournaments:', error);
    } finally {
        process.exit();
    }
}

checkTournamentData(); 