/*! For license information please see admin-bundle.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("React"),require("ReactDOM")):"function"==typeof define&&define.amd?define(["React","ReactDOM"],e):"object"==typeof exports?exports.AdminComponents=e(require("React"),require("ReactDOM")):t.AdminComponents=e(t.React,t.ReactDOM)}(this,((t,e)=>(()=>{"use strict";var n={56:(t,e,n)=>{t.exports=function(t){var e=n.nc;e&&t.setAttribute("nonce",e)}},72:t=>{var e=[];function n(t){for(var n=-1,r=0;r<e.length;r++)if(e[r].identifier===t){n=r;break}return n}function r(t,r){for(var s={},o=[],a=0;a<t.length;a++){var c=t[a],u=r.base?c[0]+r.base:c[0],h=s[u]||0,l="".concat(u," ").concat(h);s[u]=h+1;var d=n(l),f={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==d)e[d].references++,e[d].updater(f);else{var p=i(f,r);r.byIndex=a,e.splice(a,0,{identifier:l,updater:p,references:1})}o.push(l)}return o}function i(t,e){var n=e.domAPI(e);return n.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;n.update(t=e)}else n.remove()}}t.exports=function(t,i){var s=r(t=t||[],i=i||{});return function(t){t=t||[];for(var o=0;o<s.length;o++){var a=n(s[o]);e[a].references--}for(var c=r(t,i),u=0;u<s.length;u++){var h=n(s[u]);0===e[h].references&&(e[h].updater(),e.splice(h,1))}s=c}}},113:t=>{t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},314:t=>{t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",r=void 0!==e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),r&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),r&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,r,i,s){"string"==typeof t&&(t=[[null,t,void 0]]);var o={};if(r)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(o[c]=!0)}for(var u=0;u<t.length;u++){var h=[].concat(t[u]);r&&o[h[0]]||(void 0!==s&&(void 0===h[5]||(h[1]="@layer".concat(h[5].length>0?" ".concat(h[5]):""," {").concat(h[1],"}")),h[5]=s),n&&(h[2]?(h[1]="@media ".concat(h[2]," {").concat(h[1],"}"),h[2]=n):h[2]=n),i&&(h[4]?(h[1]="@supports (".concat(h[4],") {").concat(h[1],"}"),h[4]=i):h[4]="".concat(i)),e.push(h))}},e}},516:(t,e,n)=>{n.d(e,{A:()=>a});var r=n(601),i=n.n(r),s=n(314),o=n.n(s)()(i());o.push([t.id,".admin-control-panel {\n    background: rgba(0, 0, 0, 0.8);\n    border: 1px solid #00ff00;\n    border-radius: 8px;\n    padding: 20px;\n    margin: 20px 0;\n    color: #fff;\n}\n\n.admin-control-panel h2 {\n    color: #00ff00;\n    font-family: 'Orbitron', sans-serif;\n    margin-bottom: 20px;\n    text-align: center;\n}\n\n.admin-control-panel h3 {\n    color: #00ff00;\n    font-family: 'Orbitron', sans-serif;\n    margin: 15px 0;\n    font-size: 1.2em;\n}\n\n.error-message {\n    background: rgba(255, 0, 0, 0.2);\n    border: 1px solid #ff0000;\n    color: #ff0000;\n    padding: 10px;\n    margin: 10px 0;\n    border-radius: 4px;\n}\n\n.success-message {\n    background: rgba(0, 255, 0, 0.2);\n    border: 1px solid #00ff00;\n    color: #00ff00;\n    padding: 10px;\n    margin: 10px 0;\n    border-radius: 4px;\n}\n\n.control-buttons {\n    display: flex;\n    gap: 10px;\n    flex-wrap: wrap;\n    margin: 10px 0;\n}\n\n.btn {\n    padding: 8px 16px;\n    border: 1px solid #00ff00;\n    background: transparent;\n    color: #00ff00;\n    font-family: 'Orbitron', sans-serif;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    border-radius: 4px;\n}\n\n.btn:hover {\n    background: rgba(0, 255, 0, 0.2);\n}\n\n.btn-cyber-primary {\n    background: rgba(0, 255, 0, 0.1);\n}\n\n.btn-cyber-secondary {\n    background: transparent;\n}\n\n.btn-cyber-danger {\n    border-color: #ff0000;\n    color: #ff0000;\n}\n\n.btn-cyber-danger:hover {\n    background: rgba(255, 0, 0, 0.2);\n}\n\n.participant-controls {\n    margin: 20px 0;\n}\n\n.add-participant,\n.replace-participant {\n    display: flex;\n    gap: 10px;\n    margin: 10px 0;\n    align-items: center;\n}\n\ninput {\n    background: rgba(0, 0, 0, 0.5);\n    border: 1px solid #00ff00;\n    color: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    font-family: 'Orbitron', sans-serif;\n}\n\ninput:focus {\n    outline: none;\n    border-color: #00ff00;\n    box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);\n}\n\n.selected-match-controls {\n    background: rgba(0, 255, 0, 0.1);\n    border: 1px solid #00ff00;\n    padding: 15px;\n    border-radius: 4px;\n    margin: 10px 0;\n}\n\n.selected-match-controls h4 {\n    color: #00ff00;\n    margin-bottom: 10px;\n    font-family: 'Orbitron', sans-serif;\n}\n\n@media (max-width: 768px) {\n    .control-buttons {\n        flex-direction: column;\n    }\n\n    .btn {\n        width: 100%;\n    }\n\n    .add-participant,\n    .replace-participant {\n        flex-direction: column;\n    }\n\n    input {\n        width: 100%;\n    }\n} ",""]);const a=o},540:t=>{t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},601:t=>{t.exports=function(t){return t[1]}},659:t=>{var e={};t.exports=function(t,n){var r=function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}(t);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},825:t=>{t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(n){!function(t,e,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var i=void 0!==n.layer;i&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,i&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var s=n.sourceMap;s&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(s))))," */")),e.styleTagTransform(r,t,e.options)}(e,t,n)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},845:t=>{t.exports=e},883:e=>{e.exports=t}},r={};function i(t){var e=r[t];if(void 0!==e)return e.exports;var s=r[t]={id:t,exports:{}};return n[t](s,s.exports,i),s.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.nc=void 0;var s=i(883),o=i.n(s),a=i(845),c=i.n(a);const u=function(t){const e=[];let n=0;for(let r=0;r<t.length;r++){let i=t.charCodeAt(r);i<128?e[n++]=i:i<2048?(e[n++]=i>>6|192,e[n++]=63&i|128):55296==(64512&i)&&r+1<t.length&&56320==(64512&t.charCodeAt(r+1))?(i=65536+((1023&i)<<10)+(1023&t.charCodeAt(++r)),e[n++]=i>>18|240,e[n++]=i>>12&63|128,e[n++]=i>>6&63|128,e[n++]=63&i|128):(e[n++]=i>>12|224,e[n++]=i>>6&63|128,e[n++]=63&i|128)}return e},h={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(t,e){if(!Array.isArray(t))throw Error("encodeByteArray takes an array as a parameter");this.init_();const n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let e=0;e<t.length;e+=3){const i=t[e],s=e+1<t.length,o=s?t[e+1]:0,a=e+2<t.length,c=a?t[e+2]:0,u=i>>2,h=(3&i)<<4|o>>4;let l=(15&o)<<2|c>>6,d=63&c;a||(d=64,s||(l=64)),r.push(n[u],n[h],n[l],n[d])}return r.join("")},encodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(t):this.encodeByteArray(u(t),e)},decodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(t):function(t){const e=[];let n=0,r=0;for(;n<t.length;){const i=t[n++];if(i<128)e[r++]=String.fromCharCode(i);else if(i>191&&i<224){const s=t[n++];e[r++]=String.fromCharCode((31&i)<<6|63&s)}else if(i>239&&i<365){const s=((7&i)<<18|(63&t[n++])<<12|(63&t[n++])<<6|63&t[n++])-65536;e[r++]=String.fromCharCode(55296+(s>>10)),e[r++]=String.fromCharCode(56320+(1023&s))}else{const s=t[n++],o=t[n++];e[r++]=String.fromCharCode((15&i)<<12|(63&s)<<6|63&o)}}return e.join("")}(this.decodeStringToByteArray(t,e))},decodeStringToByteArray(t,e){this.init_();const n=e?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let e=0;e<t.length;){const i=n[t.charAt(e++)],s=e<t.length?n[t.charAt(e)]:0;++e;const o=e<t.length?n[t.charAt(e)]:64;++e;const a=e<t.length?n[t.charAt(e)]:64;if(++e,null==i||null==s||null==o||null==a)throw new l;const c=i<<2|s>>4;if(r.push(c),64!==o){const t=s<<4&240|o>>2;if(r.push(t),64!==a){const t=o<<6&192|a;r.push(t)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let t=0;t<this.ENCODED_VALS.length;t++)this.byteToCharMap_[t]=this.ENCODED_VALS.charAt(t),this.charToByteMap_[this.byteToCharMap_[t]]=t,this.byteToCharMapWebSafe_[t]=this.ENCODED_VALS_WEBSAFE.charAt(t),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[t]]=t,t>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(t)]=t,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(t)]=t)}}};class l extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const d=function(t){return function(t){const e=u(t);return h.encodeByteArray(e,!0)}(t).replace(/\./g,"")};class f extends Error{constructor(t,e,n){super(e),this.code=t,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,f.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,p.prototype.create)}}class p{constructor(t,e,n){this.service=t,this.serviceName=e,this.errors=n}create(t,...e){const n=e[0]||{},r=`${this.service}/${t}`,i=this.errors[t],s=i?function(t,e){return t.replace(g,((t,n)=>{const r=e[n];return null!=r?String(r):`<${n}?>`}))}(i,n):"Error",o=`${this.serviceName}: ${s} (${r}).`;return new f(r,o,n)}}const g=/\{\$([^}]+)}/g;function m(t){return t&&t._delegate?t._delegate:t}class y{constructor(t,e,n){this.name=t,this.instanceFactory=e,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(t){return this.instantiationMode=t,this}setMultipleInstances(t){return this.multipleInstances=t,this}setServiceProps(t){return this.serviceProps=t,this}setInstanceCreatedCallback(t){return this.onInstanceCreated=t,this}}const v=[];var w;!function(t){t[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT"}(w||(w={}));const b={debug:w.DEBUG,verbose:w.VERBOSE,info:w.INFO,warn:w.WARN,error:w.ERROR,silent:w.SILENT},E=w.INFO,T={[w.DEBUG]:"log",[w.VERBOSE]:"log",[w.INFO]:"info",[w.WARN]:"warn",[w.ERROR]:"error"},I=(t,e,...n)=>{if(e<t.logLevel)return;const r=(new Date).toISOString(),i=T[e];if(!i)throw new Error(`Attempted to log a message with an invalid logType (value: ${e})`);console[i](`[${r}]  ${t.name}:`,...n)};class S{constructor(t){this.name=t,this._logLevel=E,this._logHandler=I,this._userLogHandler=null,v.push(this)}get logLevel(){return this._logLevel}set logLevel(t){if(!(t in w))throw new TypeError(`Invalid value "${t}" assigned to \`logLevel\``);this._logLevel=t}setLogLevel(t){this._logLevel="string"==typeof t?b[t]:t}get logHandler(){return this._logHandler}set logHandler(t){if("function"!=typeof t)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=t}get userLogHandler(){return this._userLogHandler}set userLogHandler(t){this._userLogHandler=t}debug(...t){this._userLogHandler&&this._userLogHandler(this,w.DEBUG,...t),this._logHandler(this,w.DEBUG,...t)}log(...t){this._userLogHandler&&this._userLogHandler(this,w.VERBOSE,...t),this._logHandler(this,w.VERBOSE,...t)}info(...t){this._userLogHandler&&this._userLogHandler(this,w.INFO,...t),this._logHandler(this,w.INFO,...t)}warn(...t){this._userLogHandler&&this._userLogHandler(this,w.WARN,...t),this._logHandler(this,w.WARN,...t)}error(...t){this._userLogHandler&&this._userLogHandler(this,w.ERROR,...t),this._logHandler(this,w.ERROR,...t)}}let C,A;const _=new WeakMap,x=new WeakMap,N=new WeakMap,D=new WeakMap,k=new WeakMap;let R={get(t,e,n){if(t instanceof IDBTransaction){if("done"===e)return x.get(t);if("objectStoreNames"===e)return t.objectStoreNames||N.get(t);if("store"===e)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return M(t[e])},set:(t,e,n)=>(t[e]=n,!0),has:(t,e)=>t instanceof IDBTransaction&&("done"===e||"store"===e)||e in t};function O(t){return"function"==typeof t?(e=t)!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(A||(A=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(e)?function(...t){return e.apply(L(this),t),M(_.get(this))}:function(...t){return M(e.apply(L(this),t))}:function(t,...n){const r=e.call(L(this),t,...n);return N.set(r,t.sort?t.sort():[t]),M(r)}:(t instanceof IDBTransaction&&function(t){if(x.has(t))return;const e=new Promise(((e,n)=>{const r=()=>{t.removeEventListener("complete",i),t.removeEventListener("error",s),t.removeEventListener("abort",s)},i=()=>{e(),r()},s=()=>{n(t.error||new DOMException("AbortError","AbortError")),r()};t.addEventListener("complete",i),t.addEventListener("error",s),t.addEventListener("abort",s)}));x.set(t,e)}(t),n=t,(C||(C=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some((t=>n instanceof t))?new Proxy(t,R):t);var e,n}function M(t){if(t instanceof IDBRequest)return function(t){const e=new Promise(((e,n)=>{const r=()=>{t.removeEventListener("success",i),t.removeEventListener("error",s)},i=()=>{e(M(t.result)),r()},s=()=>{n(t.error),r()};t.addEventListener("success",i),t.addEventListener("error",s)}));return e.then((e=>{e instanceof IDBCursor&&_.set(e,t)})).catch((()=>{})),k.set(e,t),e}(t);if(D.has(t))return D.get(t);const e=O(t);return e!==t&&(D.set(t,e),k.set(e,t)),e}const L=t=>k.get(t),P=["get","getKey","getAll","getAllKeys","count"],F=["put","add","delete","clear"],V=new Map;function U(t,e){if(!(t instanceof IDBDatabase)||e in t||"string"!=typeof e)return;if(V.get(e))return V.get(e);const n=e.replace(/FromIndex$/,""),r=e!==n,i=F.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!i&&!P.includes(n))return;const s=async function(t,...e){const s=this.transaction(t,i?"readwrite":"readonly");let o=s.store;return r&&(o=o.index(e.shift())),(await Promise.all([o[n](...e),i&&s.done]))[0]};return V.set(e,s),s}var B;B=R,R={...B,get:(t,e,n)=>U(t,e)||B.get(t,e,n),has:(t,e)=>!!U(t,e)||B.has(t,e)};class j{constructor(t){this.container=t}getPlatformInfoString(){return this.container.getProviders().map((t=>{if(function(t){const e=t.getComponent();return"VERSION"===(null==e?void 0:e.type)}(t)){const e=t.getImmediate();return`${e.library}/${e.version}`}return null})).filter((t=>t)).join(" ")}}const q="@firebase/app",$="0.9.13",z=new S("@firebase/app"),G="@firebase/app-compat",K="@firebase/analytics-compat",H="@firebase/analytics",Q="@firebase/app-check-compat",W="@firebase/app-check",Y="@firebase/auth",X="@firebase/auth-compat",J="@firebase/database",Z="@firebase/database-compat",tt="@firebase/functions",et="@firebase/functions-compat",nt="@firebase/installations",rt="@firebase/installations-compat",it="@firebase/messaging",st="@firebase/messaging-compat",ot="@firebase/performance",at="@firebase/performance-compat",ct="@firebase/remote-config",ut="@firebase/remote-config-compat",ht="@firebase/storage",lt="@firebase/storage-compat",dt="@firebase/firestore",ft="@firebase/firestore-compat",pt="firebase",gt={[q]:"fire-core",[G]:"fire-core-compat",[H]:"fire-analytics",[K]:"fire-analytics-compat",[W]:"fire-app-check",[Q]:"fire-app-check-compat",[Y]:"fire-auth",[X]:"fire-auth-compat",[J]:"fire-rtdb",[Z]:"fire-rtdb-compat",[tt]:"fire-fn",[et]:"fire-fn-compat",[nt]:"fire-iid",[rt]:"fire-iid-compat",[it]:"fire-fcm",[st]:"fire-fcm-compat",[ot]:"fire-perf",[at]:"fire-perf-compat",[ct]:"fire-rc",[ut]:"fire-rc-compat",[ht]:"fire-gcs",[lt]:"fire-gcs-compat",[dt]:"fire-fst",[ft]:"fire-fst-compat","fire-js":"fire-js",[pt]:"fire-js-all"},mt=new Map,yt=new Map;function vt(t,e){try{t.container.addComponent(e)}catch(n){z.debug(`Component ${e.name} failed to register with FirebaseApp ${t.name}`,n)}}function wt(t){const e=t.name;if(yt.has(e))return z.debug(`There were multiple attempts to register component ${e}.`),!1;yt.set(e,t);for(const e of mt.values())vt(e,t);return!0}const bt=new p("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}."});function Et(t,e,n){var r;let i=null!==(r=gt[t])&&void 0!==r?r:t;n&&(i+=`-${n}`);const s=i.match(/\s|\//),o=e.match(/\s|\//);if(s||o){const t=[`Unable to register library "${i}" with version "${e}":`];return s&&t.push(`library name "${i}" contains illegal characters (whitespace or "/")`),s&&o&&t.push("and"),o&&t.push(`version name "${e}" contains illegal characters (whitespace or "/")`),void z.warn(t.join(" "))}wt(new y(`${i}-version`,(()=>({library:i,version:e})),"VERSION"))}const Tt="firebase-heartbeat-store";let It=null;function St(){return It||(It=function(t,e,{blocked:n,upgrade:r,blocking:i,terminated:s}={}){const o=indexedDB.open(t,e),a=M(o);return r&&o.addEventListener("upgradeneeded",(t=>{r(M(o.result),t.oldVersion,t.newVersion,M(o.transaction),t)})),n&&o.addEventListener("blocked",(t=>n(t.oldVersion,t.newVersion,t))),a.then((t=>{s&&t.addEventListener("close",(()=>s())),i&&t.addEventListener("versionchange",(t=>i(t.oldVersion,t.newVersion,t)))})).catch((()=>{})),a}("firebase-heartbeat-database",1,{upgrade:(t,e)=>{0===e&&t.createObjectStore(Tt)}}).catch((t=>{throw bt.create("idb-open",{originalErrorMessage:t.message})}))),It}async function Ct(t,e){try{const n=(await St()).transaction(Tt,"readwrite"),r=n.objectStore(Tt);await r.put(e,At(t)),await n.done}catch(t){if(t instanceof f)z.warn(t.message);else{const e=bt.create("idb-set",{originalErrorMessage:null==t?void 0:t.message});z.warn(e.message)}}}function At(t){return`${t.name}!${t.options.appId}`}class _t{constructor(t){this.container=t,this._heartbeatsCache=null;const e=this.container.getProvider("app").getImmediate();this._storage=new Nt(e),this._heartbeatsCachePromise=this._storage.read().then((t=>(this._heartbeatsCache=t,t)))}async triggerHeartbeat(){const t=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),e=xt();if(null===this._heartbeatsCache&&(this._heartbeatsCache=await this._heartbeatsCachePromise),this._heartbeatsCache.lastSentHeartbeatDate!==e&&!this._heartbeatsCache.heartbeats.some((t=>t.date===e)))return this._heartbeatsCache.heartbeats.push({date:e,agent:t}),this._heartbeatsCache.heartbeats=this._heartbeatsCache.heartbeats.filter((t=>{const e=new Date(t.date).valueOf();return Date.now()-e<=2592e6})),this._storage.overwrite(this._heartbeatsCache)}async getHeartbeatsHeader(){if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,null===this._heartbeatsCache||0===this._heartbeatsCache.heartbeats.length)return"";const t=xt(),{heartbeatsToSend:e,unsentEntries:n}=function(t,e=1024){const n=[];let r=t.slice();for(const i of t){const t=n.find((t=>t.agent===i.agent));if(t){if(t.dates.push(i.date),Dt(n)>e){t.dates.pop();break}}else if(n.push({agent:i.agent,dates:[i.date]}),Dt(n)>e){n.pop();break}r=r.slice(1)}return{heartbeatsToSend:n,unsentEntries:r}}(this._heartbeatsCache.heartbeats),r=d(JSON.stringify({version:2,heartbeats:e}));return this._heartbeatsCache.lastSentHeartbeatDate=t,n.length>0?(this._heartbeatsCache.heartbeats=n,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),r}}function xt(){return(new Date).toISOString().substring(0,10)}class Nt{constructor(t){this.app=t,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!function(){try{return"object"==typeof indexedDB}catch(t){return!1}}()&&new Promise(((t,e)=>{try{let n=!0;const r="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(r);i.onsuccess=()=>{i.result.close(),n||self.indexedDB.deleteDatabase(r),t(!0)},i.onupgradeneeded=()=>{n=!1},i.onerror=()=>{var t;e((null===(t=i.error)||void 0===t?void 0:t.message)||"")}}catch(t){e(t)}})).then((()=>!0)).catch((()=>!1))}async read(){if(await this._canUseIndexedDBPromise){const t=await async function(t){try{const e=await St();return await e.transaction(Tt).objectStore(Tt).get(At(t))}catch(t){if(t instanceof f)z.warn(t.message);else{const e=bt.create("idb-get",{originalErrorMessage:null==t?void 0:t.message});z.warn(e.message)}}}(this.app);return t||{heartbeats:[]}}return{heartbeats:[]}}async overwrite(t){var e;if(await this._canUseIndexedDBPromise){const n=await this.read();return Ct(this.app,{lastSentHeartbeatDate:null!==(e=t.lastSentHeartbeatDate)&&void 0!==e?e:n.lastSentHeartbeatDate,heartbeats:t.heartbeats})}}async add(t){var e;if(await this._canUseIndexedDBPromise){const n=await this.read();return Ct(this.app,{lastSentHeartbeatDate:null!==(e=t.lastSentHeartbeatDate)&&void 0!==e?e:n.lastSentHeartbeatDate,heartbeats:[...n.heartbeats,...t.heartbeats]})}}}function Dt(t){return d(JSON.stringify({version:2,heartbeats:t})).length}wt(new y("platform-logger",(t=>new j(t)),"PRIVATE")),wt(new y("heartbeat",(t=>new _t(t)),"PRIVATE")),Et(q,$,""),Et(q,$,"esm2017"),Et("fire-js","");var kt,Rt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Ot={},Mt=Mt||{},Lt=Rt||self;function Pt(t){var e=typeof t;return"array"==(e="object"!=e?e:t?Array.isArray(t)?"array":e:"null")||"object"==e&&"number"==typeof t.length}function Ft(t){var e=typeof t;return"object"==e&&null!=t||"function"==e}var Vt="closure_uid_"+(1e9*Math.random()>>>0),Ut=0;function Bt(t,e,n){return t.call.apply(t.bind,arguments)}function jt(t,e,n){if(!t)throw Error();if(2<arguments.length){var r=Array.prototype.slice.call(arguments,2);return function(){var n=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(n,r),t.apply(e,n)}}return function(){return t.apply(e,arguments)}}function qt(t,e,n){return(qt=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?Bt:jt).apply(null,arguments)}function $t(t,e){var n=Array.prototype.slice.call(arguments,1);return function(){var e=n.slice();return e.push.apply(e,arguments),t.apply(this,e)}}function zt(t,e){function n(){}n.prototype=e.prototype,t.$=e.prototype,t.prototype=new n,t.prototype.constructor=t,t.ac=function(t,n,r){for(var i=Array(arguments.length-2),s=2;s<arguments.length;s++)i[s-2]=arguments[s];return e.prototype[n].apply(t,i)}}function Gt(){this.s=this.s,this.o=this.o}Gt.prototype.s=!1,Gt.prototype.sa=function(){var t;!this.s&&(this.s=!0,this.N(),0)&&(t=this,Object.prototype.hasOwnProperty.call(t,Vt)&&t[Vt]||(t[Vt]=++Ut))},Gt.prototype.N=function(){if(this.o)for(;this.o.length;)this.o.shift()()};const Kt=Array.prototype.indexOf?function(t,e){return Array.prototype.indexOf.call(t,e,void 0)}:function(t,e){if("string"==typeof t)return"string"!=typeof e||1!=e.length?-1:t.indexOf(e,0);for(let n=0;n<t.length;n++)if(n in t&&t[n]===e)return n;return-1};function Ht(t){const e=t.length;if(0<e){const n=Array(e);for(let r=0;r<e;r++)n[r]=t[r];return n}return[]}function Qt(t,e){for(let e=1;e<arguments.length;e++){const n=arguments[e];if(Pt(n)){const e=t.length||0,r=n.length||0;t.length=e+r;for(let i=0;i<r;i++)t[e+i]=n[i]}else t.push(n)}}function Wt(t,e){this.type=t,this.g=this.target=e,this.defaultPrevented=!1}Wt.prototype.h=function(){this.defaultPrevented=!0};var Yt=function(){if(!Lt.addEventListener||!Object.defineProperty)return!1;var t=!1,e=Object.defineProperty({},"passive",{get:function(){t=!0}});try{Lt.addEventListener("test",(()=>{}),e),Lt.removeEventListener("test",(()=>{}),e)}catch(t){}return t}();function Xt(t){return/^[\s\xa0]*$/.test(t)}function Jt(){var t=Lt.navigator;return t&&(t=t.userAgent)?t:""}function Zt(t){return-1!=Jt().indexOf(t)}function te(t){return te[" "](t),t}te[" "]=function(){};var ee,ne,re,ie=Zt("Opera"),se=Zt("Trident")||Zt("MSIE"),oe=Zt("Edge"),ae=oe||se,ce=Zt("Gecko")&&!(-1!=Jt().toLowerCase().indexOf("webkit")&&!Zt("Edge"))&&!(Zt("Trident")||Zt("MSIE"))&&!Zt("Edge"),ue=-1!=Jt().toLowerCase().indexOf("webkit")&&!Zt("Edge");function he(){var t=Lt.document;return t?t.documentMode:void 0}t:{var le="",de=(ne=Jt(),ce?/rv:([^\);]+)(\)|;)/.exec(ne):oe?/Edge\/([\d\.]+)/.exec(ne):se?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(ne):ue?/WebKit\/(\S+)/.exec(ne):ie?/(?:Version)[ \/]?(\S+)/.exec(ne):void 0);if(de&&(le=de?de[1]:""),se){var fe=he();if(null!=fe&&fe>parseFloat(le)){ee=String(fe);break t}}ee=le}Lt.document&&se?re=he()||parseInt(ee,10)||void 0:re=void 0;var pe=re;function ge(t,e){if(Wt.call(this,t?t.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,t){var n=this.type=t.type,r=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:null;if(this.target=t.target||t.srcElement,this.g=e,e=t.relatedTarget){if(ce){t:{try{te(e.nodeName);var i=!0;break t}catch(t){}i=!1}i||(e=null)}}else"mouseover"==n?e=t.fromElement:"mouseout"==n&&(e=t.toElement);this.relatedTarget=e,r?(this.clientX=void 0!==r.clientX?r.clientX:r.pageX,this.clientY=void 0!==r.clientY?r.clientY:r.pageY,this.screenX=r.screenX||0,this.screenY=r.screenY||0):(this.clientX=void 0!==t.clientX?t.clientX:t.pageX,this.clientY=void 0!==t.clientY?t.clientY:t.pageY,this.screenX=t.screenX||0,this.screenY=t.screenY||0),this.button=t.button,this.key=t.key||"",this.ctrlKey=t.ctrlKey,this.altKey=t.altKey,this.shiftKey=t.shiftKey,this.metaKey=t.metaKey,this.pointerId=t.pointerId||0,this.pointerType="string"==typeof t.pointerType?t.pointerType:me[t.pointerType]||"",this.state=t.state,this.i=t,t.defaultPrevented&&ge.$.h.call(this)}}zt(ge,Wt);var me={2:"touch",3:"pen",4:"mouse"};ge.prototype.h=function(){ge.$.h.call(this);var t=this.i;t.preventDefault?t.preventDefault():t.returnValue=!1};var ye="closure_listenable_"+(1e6*Math.random()|0),ve=0;function we(t,e,n,r,i){this.listener=t,this.proxy=null,this.src=e,this.type=n,this.capture=!!r,this.la=i,this.key=++ve,this.fa=this.ia=!1}function be(t){t.fa=!0,t.listener=null,t.proxy=null,t.src=null,t.la=null}function Ee(t,e,n){for(const r in t)e.call(n,t[r],r,t)}function Te(t){const e={};for(const n in t)e[n]=t[n];return e}const Ie="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Se(t,e){let n,r;for(let e=1;e<arguments.length;e++){for(n in r=arguments[e],r)t[n]=r[n];for(let e=0;e<Ie.length;e++)n=Ie[e],Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}}function Ce(t){this.src=t,this.g={},this.h=0}function Ae(t,e){var n=e.type;if(n in t.g){var r,i=t.g[n],s=Kt(i,e);(r=0<=s)&&Array.prototype.splice.call(i,s,1),r&&(be(e),0==t.g[n].length&&(delete t.g[n],t.h--))}}function _e(t,e,n,r){for(var i=0;i<t.length;++i){var s=t[i];if(!s.fa&&s.listener==e&&s.capture==!!n&&s.la==r)return i}return-1}Ce.prototype.add=function(t,e,n,r,i){var s=t.toString();(t=this.g[s])||(t=this.g[s]=[],this.h++);var o=_e(t,e,r,i);return-1<o?(e=t[o],n||(e.ia=!1)):((e=new we(e,this.src,s,!!r,i)).ia=n,t.push(e)),e};var xe="closure_lm_"+(1e6*Math.random()|0),Ne={};function De(t,e,n,r,i){if(r&&r.once)return Re(t,e,n,r,i);if(Array.isArray(e)){for(var s=0;s<e.length;s++)De(t,e[s],n,r,i);return null}return n=Ue(n),t&&t[ye]?t.O(e,n,Ft(r)?!!r.capture:!!r,i):ke(t,e,n,!1,r,i)}function ke(t,e,n,r,i,s){if(!e)throw Error("Invalid event type");var o=Ft(i)?!!i.capture:!!i,a=Fe(t);if(a||(t[xe]=a=new Ce(t)),(n=a.add(e,n,r,o,s)).proxy)return n;if(r=function(){const t=Pe;return function e(n){return t.call(e.src,e.listener,n)}}(),n.proxy=r,r.src=t,r.listener=n,t.addEventListener)Yt||(i=o),void 0===i&&(i=!1),t.addEventListener(e.toString(),r,i);else if(t.attachEvent)t.attachEvent(Le(e.toString()),r);else{if(!t.addListener||!t.removeListener)throw Error("addEventListener and attachEvent are unavailable.");t.addListener(r)}return n}function Re(t,e,n,r,i){if(Array.isArray(e)){for(var s=0;s<e.length;s++)Re(t,e[s],n,r,i);return null}return n=Ue(n),t&&t[ye]?t.P(e,n,Ft(r)?!!r.capture:!!r,i):ke(t,e,n,!0,r,i)}function Oe(t,e,n,r,i){if(Array.isArray(e))for(var s=0;s<e.length;s++)Oe(t,e[s],n,r,i);else r=Ft(r)?!!r.capture:!!r,n=Ue(n),t&&t[ye]?(t=t.i,(e=String(e).toString())in t.g&&-1<(n=_e(s=t.g[e],n,r,i))&&(be(s[n]),Array.prototype.splice.call(s,n,1),0==s.length&&(delete t.g[e],t.h--))):t&&(t=Fe(t))&&(e=t.g[e.toString()],t=-1,e&&(t=_e(e,n,r,i)),(n=-1<t?e[t]:null)&&Me(n))}function Me(t){if("number"!=typeof t&&t&&!t.fa){var e=t.src;if(e&&e[ye])Ae(e.i,t);else{var n=t.type,r=t.proxy;e.removeEventListener?e.removeEventListener(n,r,t.capture):e.detachEvent?e.detachEvent(Le(n),r):e.addListener&&e.removeListener&&e.removeListener(r),(n=Fe(e))?(Ae(n,t),0==n.h&&(n.src=null,e[xe]=null)):be(t)}}}function Le(t){return t in Ne?Ne[t]:Ne[t]="on"+t}function Pe(t,e){if(t.fa)t=!0;else{e=new ge(e,this);var n=t.listener,r=t.la||t.src;t.ia&&Me(t),t=n.call(r,e)}return t}function Fe(t){return(t=t[xe])instanceof Ce?t:null}var Ve="__closure_events_fn_"+(1e9*Math.random()>>>0);function Ue(t){return"function"==typeof t?t:(t[Ve]||(t[Ve]=function(e){return t.handleEvent(e)}),t[Ve])}function Be(){Gt.call(this),this.i=new Ce(this),this.S=this,this.J=null}function je(t,e){var n,r=t.J;if(r)for(n=[];r;r=r.J)n.push(r);if(t=t.S,r=e.type||e,"string"==typeof e)e=new Wt(e,t);else if(e instanceof Wt)e.target=e.target||t;else{var i=e;Se(e=new Wt(r,t),i)}if(i=!0,n)for(var s=n.length-1;0<=s;s--){var o=e.g=n[s];i=qe(o,r,!0,e)&&i}if(i=qe(o=e.g=t,r,!0,e)&&i,i=qe(o,r,!1,e)&&i,n)for(s=0;s<n.length;s++)i=qe(o=e.g=n[s],r,!1,e)&&i}function qe(t,e,n,r){if(!(e=t.i.g[String(e)]))return!0;e=e.concat();for(var i=!0,s=0;s<e.length;++s){var o=e[s];if(o&&!o.fa&&o.capture==n){var a=o.listener,c=o.la||o.src;o.ia&&Ae(t.i,o),i=!1!==a.call(c,r)&&i}}return i&&!r.defaultPrevented}zt(Be,Gt),Be.prototype[ye]=!0,Be.prototype.removeEventListener=function(t,e,n,r){Oe(this,t,e,n,r)},Be.prototype.N=function(){if(Be.$.N.call(this),this.i){var t,e=this.i;for(t in e.g){for(var n=e.g[t],r=0;r<n.length;r++)be(n[r]);delete e.g[t],e.h--}}this.J=null},Be.prototype.O=function(t,e,n,r){return this.i.add(String(t),e,!1,n,r)},Be.prototype.P=function(t,e,n,r){return this.i.add(String(t),e,!0,n,r)};var $e=Lt.JSON.stringify;function ze(){var t=Xe;let e=null;return t.g&&(e=t.g,t.g=t.g.next,t.g||(t.h=null),e.next=null),e}var Ge=new class{constructor(t,e){this.i=t,this.j=e,this.h=0,this.g=null}get(){let t;return 0<this.h?(this.h--,t=this.g,this.g=t.next,t.next=null):t=this.i(),t}}((()=>new Ke),(t=>t.reset()));class Ke{constructor(){this.next=this.g=this.h=null}set(t,e){this.h=t,this.g=e,this.next=null}reset(){this.next=this.g=this.h=null}}function He(t){var e=1;t=t.split(":");const n=[];for(;0<e&&t.length;)n.push(t.shift()),e--;return t.length&&n.push(t.join(":")),n}function Qe(t){Lt.setTimeout((()=>{throw t}),0)}let We,Ye=!1,Xe=new class{constructor(){this.h=this.g=null}add(t,e){const n=Ge.get();n.set(t,e),this.h?this.h.next=n:this.g=n,this.h=n}},Je=()=>{const t=Lt.Promise.resolve(void 0);We=()=>{t.then(Ze)}};var Ze=()=>{for(var t;t=ze();){try{t.h.call(t.g)}catch(t){Qe(t)}var e=Ge;e.j(t),100>e.h&&(e.h++,t.next=e.g,e.g=t)}Ye=!1};function tn(t,e){Be.call(this),this.h=t||1,this.g=e||Lt,this.j=qt(this.qb,this),this.l=Date.now()}function en(t){t.ga=!1,t.T&&(t.g.clearTimeout(t.T),t.T=null)}function nn(t,e,n){if("function"==typeof t)n&&(t=qt(t,n));else{if(!t||"function"!=typeof t.handleEvent)throw Error("Invalid listener argument");t=qt(t.handleEvent,t)}return 2147483647<Number(e)?-1:Lt.setTimeout(t,e||0)}function rn(t){t.g=nn((()=>{t.g=null,t.i&&(t.i=!1,rn(t))}),t.j);const e=t.h;t.h=null,t.m.apply(null,e)}zt(tn,Be),(kt=tn.prototype).ga=!1,kt.T=null,kt.qb=function(){if(this.ga){var t=Date.now()-this.l;0<t&&t<.8*this.h?this.T=this.g.setTimeout(this.j,this.h-t):(this.T&&(this.g.clearTimeout(this.T),this.T=null),je(this,"tick"),this.ga&&(en(this),this.start()))}},kt.start=function(){this.ga=!0,this.T||(this.T=this.g.setTimeout(this.j,this.h),this.l=Date.now())},kt.N=function(){tn.$.N.call(this),en(this),delete this.g};class sn extends Gt{constructor(t,e){super(),this.m=t,this.j=e,this.h=null,this.i=!1,this.g=null}l(t){this.h=arguments,this.g?this.i=!0:rn(this)}N(){super.N(),this.g&&(Lt.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function on(t){Gt.call(this),this.h=t,this.g={}}zt(on,Gt);var an=[];function cn(t,e,n,r){Array.isArray(n)||(n&&(an[0]=n.toString()),n=an);for(var i=0;i<n.length;i++){var s=De(e,n[i],r||t.handleEvent,!1,t.h||t);if(!s)break;t.g[s.key]=s}}function un(t){Ee(t.g,(function(t,e){this.g.hasOwnProperty(e)&&Me(t)}),t),t.g={}}function hn(){this.g=!0}function ln(t,e,n,r){t.info((function(){return"XMLHTTP TEXT ("+e+"): "+function(t,e){if(!t.g)return e;if(!e)return null;try{var n=JSON.parse(e);if(n)for(t=0;t<n.length;t++)if(Array.isArray(n[t])){var r=n[t];if(!(2>r.length)){var i=r[1];if(Array.isArray(i)&&!(1>i.length)){var s=i[0];if("noop"!=s&&"stop"!=s&&"close"!=s)for(var o=1;o<i.length;o++)i[o]=""}}}return $e(n)}catch(t){return e}}(t,n)+(r?" "+r:"")}))}on.prototype.N=function(){on.$.N.call(this),un(this)},on.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")},hn.prototype.Ea=function(){this.g=!1},hn.prototype.info=function(){};var dn={},fn=null;function pn(){return fn=fn||new Be}function gn(t){Wt.call(this,dn.Ta,t)}function mn(t){const e=pn();je(e,new gn(e))}function yn(t,e){Wt.call(this,dn.STAT_EVENT,t),this.stat=e}function vn(t){const e=pn();je(e,new yn(e,t))}function wn(t,e){Wt.call(this,dn.Ua,t),this.size=e}function bn(t,e){if("function"!=typeof t)throw Error("Fn must not be null and must be a function");return Lt.setTimeout((function(){t()}),e)}dn.Ta="serverreachability",zt(gn,Wt),dn.STAT_EVENT="statevent",zt(yn,Wt),dn.Ua="timingevent",zt(wn,Wt);var En={NO_ERROR:0,rb:1,Eb:2,Db:3,yb:4,Cb:5,Fb:6,Qa:7,TIMEOUT:8,Ib:9},Tn={wb:"complete",Sb:"success",Ra:"error",Qa:"abort",Kb:"ready",Lb:"readystatechange",TIMEOUT:"timeout",Gb:"incrementaldata",Jb:"progress",zb:"downloadprogress",$b:"uploadprogress"};function In(){}function Sn(t){return t.h||(t.h=t.i())}function Cn(){}In.prototype.h=null;var An,_n={OPEN:"a",vb:"b",Ra:"c",Hb:"d"};function xn(){Wt.call(this,"d")}function Nn(){Wt.call(this,"c")}function Dn(){}function kn(t,e,n,r){this.l=t,this.j=e,this.m=n,this.W=r||1,this.U=new on(this),this.P=On,t=ae?125:void 0,this.V=new tn(t),this.I=null,this.i=!1,this.s=this.A=this.v=this.L=this.G=this.Y=this.B=null,this.F=[],this.g=null,this.C=0,this.o=this.u=null,this.ca=-1,this.J=!1,this.O=0,this.M=null,this.ba=this.K=this.aa=this.S=!1,this.h=new Rn}function Rn(){this.i=null,this.g="",this.h=!1}zt(xn,Wt),zt(Nn,Wt),zt(Dn,In),Dn.prototype.g=function(){return new XMLHttpRequest},Dn.prototype.i=function(){return{}},An=new Dn;var On=45e3,Mn={},Ln={};function Pn(t,e,n){t.L=1,t.v=er(Yn(e)),t.s=n,t.S=!0,Fn(t,null)}function Fn(t,e){t.G=Date.now(),jn(t),t.A=Yn(t.v);var n=t.A,r=t.W;Array.isArray(r)||(r=[String(r)]),pr(n.i,"t",r),t.C=0,n=t.l.J,t.h=new Rn,t.g=pi(t.l,n?e:null,!t.s),0<t.O&&(t.M=new sn(qt(t.Pa,t,t.g),t.O)),cn(t.U,t.g,"readystatechange",t.nb),e=t.I?Te(t.I):{},t.s?(t.u||(t.u="POST"),e["Content-Type"]="application/x-www-form-urlencoded",t.g.ha(t.A,t.u,t.s,e)):(t.u="GET",t.g.ha(t.A,t.u,null,e)),mn(),function(t,e,n,r,i,s){t.info((function(){if(t.g)if(s)for(var o="",a=s.split("&"),c=0;c<a.length;c++){var u=a[c].split("=");if(1<u.length){var h=u[0];u=u[1];var l=h.split("_");o=2<=l.length&&"type"==l[1]?o+(h+"=")+u+"&":o+(h+"=redacted&")}}else o=null;else o=s;return"XMLHTTP REQ ("+r+") [attempt "+i+"]: "+e+"\n"+n+"\n"+o}))}(t.j,t.u,t.A,t.m,t.W,t.s)}function Vn(t){return!!t.g&&"GET"==t.u&&2!=t.L&&t.l.Ha}function Un(t,e,n){let r,i=!0;for(;!t.J&&t.C<n.length;){if(r=Bn(t,n),r==Ln){4==e&&(t.o=4,vn(14),i=!1),ln(t.j,t.m,null,"[Incomplete Response]");break}if(r==Mn){t.o=4,vn(15),ln(t.j,t.m,n,"[Invalid Chunk]"),i=!1;break}ln(t.j,t.m,r,null),Kn(t,r)}Vn(t)&&r!=Ln&&r!=Mn&&(t.h.g="",t.C=0),4!=e||0!=n.length||t.h.h||(t.o=1,vn(16),i=!1),t.i=t.i&&i,i?0<n.length&&!t.ba&&(t.ba=!0,(e=t.l).g==t&&e.ca&&!e.M&&(e.l.info("Great, no buffering proxy detected. Bytes received: "+n.length),oi(e),e.M=!0,vn(11))):(ln(t.j,t.m,n,"[Invalid Chunked Response]"),Gn(t),zn(t))}function Bn(t,e){var n=t.C,r=e.indexOf("\n",n);return-1==r?Ln:(n=Number(e.substring(n,r)),isNaN(n)?Mn:(r+=1)+n>e.length?Ln:(e=e.slice(r,r+n),t.C=r+n,e))}function jn(t){t.Y=Date.now()+t.P,qn(t,t.P)}function qn(t,e){if(null!=t.B)throw Error("WatchDog timer not null");t.B=bn(qt(t.lb,t),e)}function $n(t){t.B&&(Lt.clearTimeout(t.B),t.B=null)}function zn(t){0==t.l.H||t.J||ui(t.l,t)}function Gn(t){$n(t);var e=t.M;e&&"function"==typeof e.sa&&e.sa(),t.M=null,en(t.V),un(t.U),t.g&&(e=t.g,t.g=null,e.abort(),e.sa())}function Kn(t,e){try{var n=t.l;if(0!=n.H&&(n.g==t||Er(n.i,t)))if(!t.K&&Er(n.i,t)&&3==n.H){try{var r=n.Ja.g.parse(e)}catch(t){r=null}if(Array.isArray(r)&&3==r.length){var i=r;if(0==i[0]){t:if(!n.u){if(n.g){if(!(n.g.G+3e3<t.G))break t;ci(n),Jr(n)}si(n),vn(18)}}else n.Fa=i[1],0<n.Fa-n.V&&37500>i[2]&&n.G&&0==n.A&&!n.v&&(n.v=bn(qt(n.ib,n),6e3));if(1>=br(n.i)&&n.oa){try{n.oa()}catch(t){}n.oa=void 0}}else li(n,11)}else if((t.K||n.g==t)&&ci(n),!Xt(e))for(i=n.Ja.g.parse(e),e=0;e<i.length;e++){let u=i[e];if(n.V=u[0],u=u[1],2==n.H)if("c"==u[0]){n.K=u[1],n.pa=u[2];const e=u[3];null!=e&&(n.ra=e,n.l.info("VER="+n.ra));const i=u[4];null!=i&&(n.Ga=i,n.l.info("SVER="+n.Ga));const h=u[5];null!=h&&"number"==typeof h&&0<h&&(r=1.5*h,n.L=r,n.l.info("backChannelRequestTimeoutMs_="+r)),r=n;const l=t.g;if(l){const t=l.g?l.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(t){var s=r.i;s.g||-1==t.indexOf("spdy")&&-1==t.indexOf("quic")&&-1==t.indexOf("h2")||(s.j=s.l,s.g=new Set,s.h&&(Tr(s,s.h),s.h=null))}if(r.F){const t=l.g?l.g.getResponseHeader("X-HTTP-Session-Id"):null;t&&(r.Da=t,tr(r.I,r.F,t))}}n.H=3,n.h&&n.h.Ba(),n.ca&&(n.S=Date.now()-t.G,n.l.info("Handshake RTT: "+n.S+"ms"));var o=t;if((r=n).wa=fi(r,r.J?r.pa:null,r.Y),o.K){Ir(r.i,o);var a=o,c=r.L;c&&a.setTimeout(c),a.B&&($n(a),jn(a)),r.g=o}else ii(r);0<n.j.length&&ti(n)}else"stop"!=u[0]&&"close"!=u[0]||li(n,7);else 3==n.H&&("stop"==u[0]||"close"==u[0]?"stop"==u[0]?li(n,7):Xr(n):"noop"!=u[0]&&n.h&&n.h.Aa(u),n.A=0)}mn()}catch(t){}}function Hn(t,e){if(t.forEach&&"function"==typeof t.forEach)t.forEach(e,void 0);else if(Pt(t)||"string"==typeof t)Array.prototype.forEach.call(t,e,void 0);else for(var n=function(t){if(t.ta&&"function"==typeof t.ta)return t.ta();if(!t.Z||"function"!=typeof t.Z){if("undefined"!=typeof Map&&t instanceof Map)return Array.from(t.keys());if(!("undefined"!=typeof Set&&t instanceof Set)){if(Pt(t)||"string"==typeof t){var e=[];t=t.length;for(var n=0;n<t;n++)e.push(n);return e}e=[],n=0;for(const r in t)e[n++]=r;return e}}}(t),r=function(t){if(t.Z&&"function"==typeof t.Z)return t.Z();if("undefined"!=typeof Map&&t instanceof Map||"undefined"!=typeof Set&&t instanceof Set)return Array.from(t.values());if("string"==typeof t)return t.split("");if(Pt(t)){for(var e=[],n=t.length,r=0;r<n;r++)e.push(t[r]);return e}for(r in e=[],n=0,t)e[n++]=t[r];return e}(t),i=r.length,s=0;s<i;s++)e.call(void 0,r[s],n&&n[s],t)}(kt=kn.prototype).setTimeout=function(t){this.P=t},kt.nb=function(t){t=t.target;const e=this.M;e&&3==Gr(t)?e.l():this.Pa(t)},kt.Pa=function(t){try{if(t==this.g)t:{const h=Gr(this.g);var e=this.g.Ia();if(this.g.da(),!(3>h)&&(3!=h||ae||this.g&&(this.h.h||this.g.ja()||Kr(this.g)))){this.J||4!=h||7==e||mn(),$n(this);var n=this.g.da();this.ca=n;e:if(Vn(this)){var r=Kr(this.g);t="";var i=r.length,s=4==Gr(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){Gn(this),zn(this);var o="";break e}this.h.i=new Lt.TextDecoder}for(e=0;e<i;e++)this.h.h=!0,t+=this.h.i.decode(r[e],{stream:s&&e==i-1});r.splice(0,i),this.h.g+=t,this.C=0,o=this.h.g}else o=this.g.ja();if(this.i=200==n,function(t,e,n,r,i,s,o){t.info((function(){return"XMLHTTP RESP ("+r+") [ attempt "+i+"]: "+e+"\n"+n+"\n"+s+" "+o}))}(this.j,this.u,this.A,this.m,this.W,h,n),this.i){if(this.aa&&!this.K){e:{if(this.g){var a,c=this.g;if((a=c.g?c.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!Xt(a)){var u=a;break e}}u=null}if(!(n=u)){this.i=!1,this.o=3,vn(12),Gn(this),zn(this);break t}ln(this.j,this.m,n,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,Kn(this,n)}this.S?(Un(this,h,o),ae&&this.i&&3==h&&(cn(this.U,this.V,"tick",this.mb),this.V.start())):(ln(this.j,this.m,o,null),Kn(this,o)),4==h&&Gn(this),this.i&&!this.J&&(4==h?ui(this.l,this):(this.i=!1,jn(this)))}else(function(t){const e={};t=(t.g&&2<=Gr(t)&&t.g.getAllResponseHeaders()||"").split("\r\n");for(let r=0;r<t.length;r++){if(Xt(t[r]))continue;var n=He(t[r]);const i=n[0];if("string"!=typeof(n=n[1]))continue;n=n.trim();const s=e[i]||[];e[i]=s,s.push(n)}!function(t,e){for(const n in t)e.call(void 0,t[n],n,t)}(e,(function(t){return t.join(", ")}))})(this.g),400==n&&0<o.indexOf("Unknown SID")?(this.o=3,vn(12)):(this.o=0,vn(13)),Gn(this),zn(this)}}}catch(t){}},kt.mb=function(){if(this.g){var t=Gr(this.g),e=this.g.ja();this.C<e.length&&($n(this),Un(this,t,e),this.i&&4!=t&&jn(this))}},kt.cancel=function(){this.J=!0,Gn(this)},kt.lb=function(){this.B=null;const t=Date.now();0<=t-this.Y?(function(t,e){t.info((function(){return"TIMEOUT: "+e}))}(this.j,this.A),2!=this.L&&(mn(),vn(17)),Gn(this),this.o=2,zn(this)):qn(this,this.Y-t)};var Qn=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Wn(t){if(this.g=this.s=this.j="",this.m=null,this.o=this.l="",this.h=!1,t instanceof Wn){this.h=t.h,Xn(this,t.j),this.s=t.s,this.g=t.g,Jn(this,t.m),this.l=t.l;var e=t.i,n=new hr;n.i=e.i,e.g&&(n.g=new Map(e.g),n.h=e.h),Zn(this,n),this.o=t.o}else t&&(e=String(t).match(Qn))?(this.h=!1,Xn(this,e[1]||"",!0),this.s=nr(e[2]||""),this.g=nr(e[3]||"",!0),Jn(this,e[4]),this.l=nr(e[5]||"",!0),Zn(this,e[6]||"",!0),this.o=nr(e[7]||"")):(this.h=!1,this.i=new hr(null,this.h))}function Yn(t){return new Wn(t)}function Xn(t,e,n){t.j=n?nr(e,!0):e,t.j&&(t.j=t.j.replace(/:$/,""))}function Jn(t,e){if(e){if(e=Number(e),isNaN(e)||0>e)throw Error("Bad port number "+e);t.m=e}else t.m=null}function Zn(t,e,n){e instanceof hr?(t.i=e,function(t,e){e&&!t.j&&(lr(t),t.i=null,t.g.forEach((function(t,e){var n=e.toLowerCase();e!=n&&(dr(this,e),pr(this,n,t))}),t)),t.j=e}(t.i,t.h)):(n||(e=rr(e,cr)),t.i=new hr(e,t.h))}function tr(t,e,n){t.i.set(e,n)}function er(t){return tr(t,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),t}function nr(t,e){return t?e?decodeURI(t.replace(/%25/g,"%2525")):decodeURIComponent(t):""}function rr(t,e,n){return"string"==typeof t?(t=encodeURI(t).replace(e,ir),n&&(t=t.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),t):null}function ir(t){return"%"+((t=t.charCodeAt(0))>>4&15).toString(16)+(15&t).toString(16)}Wn.prototype.toString=function(){var t=[],e=this.j;e&&t.push(rr(e,sr,!0),":");var n=this.g;return(n||"file"==e)&&(t.push("//"),(e=this.s)&&t.push(rr(e,sr,!0),"@"),t.push(encodeURIComponent(String(n)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null!=(n=this.m)&&t.push(":",String(n))),(n=this.l)&&(this.g&&"/"!=n.charAt(0)&&t.push("/"),t.push(rr(n,"/"==n.charAt(0)?ar:or,!0))),(n=this.i.toString())&&t.push("?",n),(n=this.o)&&t.push("#",rr(n,ur)),t.join("")};var sr=/[#\/\?@]/g,or=/[#\?:]/g,ar=/[#\?]/g,cr=/[#\?@]/g,ur=/#/g;function hr(t,e){this.h=this.g=null,this.i=t||null,this.j=!!e}function lr(t){t.g||(t.g=new Map,t.h=0,t.i&&function(t,e){if(t){t=t.split("&");for(var n=0;n<t.length;n++){var r=t[n].indexOf("="),i=null;if(0<=r){var s=t[n].substring(0,r);i=t[n].substring(r+1)}else s=t[n];e(s,i?decodeURIComponent(i.replace(/\+/g," ")):"")}}}(t.i,(function(e,n){t.add(decodeURIComponent(e.replace(/\+/g," ")),n)})))}function dr(t,e){lr(t),e=gr(t,e),t.g.has(e)&&(t.i=null,t.h-=t.g.get(e).length,t.g.delete(e))}function fr(t,e){return lr(t),e=gr(t,e),t.g.has(e)}function pr(t,e,n){dr(t,e),0<n.length&&(t.i=null,t.g.set(gr(t,e),Ht(n)),t.h+=n.length)}function gr(t,e){return e=String(e),t.j&&(e=e.toLowerCase()),e}(kt=hr.prototype).add=function(t,e){lr(this),this.i=null,t=gr(this,t);var n=this.g.get(t);return n||this.g.set(t,n=[]),n.push(e),this.h+=1,this},kt.forEach=function(t,e){lr(this),this.g.forEach((function(n,r){n.forEach((function(n){t.call(e,n,r,this)}),this)}),this)},kt.ta=function(){lr(this);const t=Array.from(this.g.values()),e=Array.from(this.g.keys()),n=[];for(let r=0;r<e.length;r++){const i=t[r];for(let t=0;t<i.length;t++)n.push(e[r])}return n},kt.Z=function(t){lr(this);let e=[];if("string"==typeof t)fr(this,t)&&(e=e.concat(this.g.get(gr(this,t))));else{t=Array.from(this.g.values());for(let n=0;n<t.length;n++)e=e.concat(t[n])}return e},kt.set=function(t,e){return lr(this),this.i=null,fr(this,t=gr(this,t))&&(this.h-=this.g.get(t).length),this.g.set(t,[e]),this.h+=1,this},kt.get=function(t,e){return t&&0<(t=this.Z(t)).length?String(t[0]):e},kt.toString=function(){if(this.i)return this.i;if(!this.g)return"";const t=[],e=Array.from(this.g.keys());for(var n=0;n<e.length;n++){var r=e[n];const s=encodeURIComponent(String(r)),o=this.Z(r);for(r=0;r<o.length;r++){var i=s;""!==o[r]&&(i+="="+encodeURIComponent(String(o[r]))),t.push(i)}}return this.i=t.join("&")};var mr=class{constructor(t,e){this.g=t,this.map=e}};function yr(t){this.l=t||vr,t=Lt.PerformanceNavigationTiming?0<(t=Lt.performance.getEntriesByType("navigation")).length&&("hq"==t[0].nextHopProtocol||"h2"==t[0].nextHopProtocol):!!(Lt.g&&Lt.g.Ka&&Lt.g.Ka()&&Lt.g.Ka().ec),this.j=t?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}var vr=10;function wr(t){return!!t.h||!!t.g&&t.g.size>=t.j}function br(t){return t.h?1:t.g?t.g.size:0}function Er(t,e){return t.h?t.h==e:!!t.g&&t.g.has(e)}function Tr(t,e){t.g?t.g.add(e):t.h=e}function Ir(t,e){t.h&&t.h==e?t.h=null:t.g&&t.g.has(e)&&t.g.delete(e)}function Sr(t){if(null!=t.h)return t.i.concat(t.h.F);if(null!=t.g&&0!==t.g.size){let e=t.i;for(const n of t.g.values())e=e.concat(n.F);return e}return Ht(t.i)}yr.prototype.cancel=function(){if(this.i=Sr(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(const t of this.g.values())t.cancel();this.g.clear()}};var Cr=class{stringify(t){return Lt.JSON.stringify(t,void 0)}parse(t){return Lt.JSON.parse(t,void 0)}};function Ar(){this.g=new Cr}function _r(t,e,n){const r=n||"";try{Hn(t,(function(t,n){let i=t;Ft(t)&&(i=$e(t)),e.push(r+n+"="+encodeURIComponent(i))}))}catch(t){throw e.push(r+"type="+encodeURIComponent("_badmap")),t}}function xr(t,e,n,r,i){try{e.onload=null,e.onerror=null,e.onabort=null,e.ontimeout=null,i(r)}catch(t){}}function Nr(t){this.l=t.fc||null,this.j=t.ob||!1}function Dr(t,e){Be.call(this),this.F=t,this.u=e,this.m=void 0,this.readyState=kr,this.status=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.v=new Headers,this.h=null,this.C="GET",this.B="",this.g=!1,this.A=this.j=this.l=null}zt(Nr,In),Nr.prototype.g=function(){return new Dr(this.l,this.j)},Nr.prototype.i=function(t){return function(){return t}}({}),zt(Dr,Be);var kr=0;function Rr(t){t.j.read().then(t.Xa.bind(t)).catch(t.ka.bind(t))}function Or(t){t.readyState=4,t.l=null,t.j=null,t.A=null,Mr(t)}function Mr(t){t.onreadystatechange&&t.onreadystatechange.call(t)}(kt=Dr.prototype).open=function(t,e){if(this.readyState!=kr)throw this.abort(),Error("Error reopening a connection");this.C=t,this.B=e,this.readyState=1,Mr(this)},kt.send=function(t){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;const e={headers:this.v,method:this.C,credentials:this.m,cache:void 0};t&&(e.body=t),(this.F||Lt).fetch(new Request(this.B,e)).then(this.$a.bind(this),this.ka.bind(this))},kt.abort=function(){this.response=this.responseText="",this.v=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch((()=>{})),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,Or(this)),this.readyState=kr},kt.$a=function(t){if(this.g&&(this.l=t,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=t.headers,this.readyState=2,Mr(this)),this.g&&(this.readyState=3,Mr(this),this.g)))if("arraybuffer"===this.responseType)t.arrayBuffer().then(this.Ya.bind(this),this.ka.bind(this));else if(void 0!==Lt.ReadableStream&&"body"in t){if(this.j=t.body.getReader(),this.u){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.A=new TextDecoder;Rr(this)}else t.text().then(this.Za.bind(this),this.ka.bind(this))},kt.Xa=function(t){if(this.g){if(this.u&&t.value)this.response.push(t.value);else if(!this.u){var e=t.value?t.value:new Uint8Array(0);(e=this.A.decode(e,{stream:!t.done}))&&(this.response=this.responseText+=e)}t.done?Or(this):Mr(this),3==this.readyState&&Rr(this)}},kt.Za=function(t){this.g&&(this.response=this.responseText=t,Or(this))},kt.Ya=function(t){this.g&&(this.response=t,Or(this))},kt.ka=function(){this.g&&Or(this)},kt.setRequestHeader=function(t,e){this.v.append(t,e)},kt.getResponseHeader=function(t){return this.h&&this.h.get(t.toLowerCase())||""},kt.getAllResponseHeaders=function(){if(!this.h)return"";const t=[],e=this.h.entries();for(var n=e.next();!n.done;)n=n.value,t.push(n[0]+": "+n[1]),n=e.next();return t.join("\r\n")},Object.defineProperty(Dr.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(t){this.m=t?"include":"same-origin"}});var Lr=Lt.JSON.parse;function Pr(t){Be.call(this),this.headers=new Map,this.u=t||null,this.h=!1,this.C=this.g=null,this.I="",this.m=0,this.j="",this.l=this.G=this.v=this.F=!1,this.B=0,this.A=null,this.K=Fr,this.L=this.M=!1}zt(Pr,Be);var Fr="",Vr=/^https?$/i,Ur=["POST","PUT"];function Br(t,e){t.h=!1,t.g&&(t.l=!0,t.g.abort(),t.l=!1),t.j=e,t.m=5,jr(t),$r(t)}function jr(t){t.F||(t.F=!0,je(t,"complete"),je(t,"error"))}function qr(t){if(t.h&&void 0!==Mt&&(!t.C[1]||4!=Gr(t)||2!=t.da()))if(t.v&&4==Gr(t))nn(t.La,0,t);else if(je(t,"readystatechange"),4==Gr(t)){t.h=!1;try{const o=t.da();t:switch(o){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var e=!0;break t;default:e=!1}var n;if(!(n=e)){var r;if(r=0===o){var i=String(t.I).match(Qn)[1]||null;!i&&Lt.self&&Lt.self.location&&(i=Lt.self.location.protocol.slice(0,-1)),r=!Vr.test(i?i.toLowerCase():"")}n=r}if(n)je(t,"complete"),je(t,"success");else{t.m=6;try{var s=2<Gr(t)?t.g.statusText:""}catch(t){s=""}t.j=s+" ["+t.da()+"]",jr(t)}}finally{$r(t)}}}function $r(t,e){if(t.g){zr(t);const n=t.g,r=t.C[0]?()=>{}:null;t.g=null,t.C=null,e||je(t,"ready");try{n.onreadystatechange=r}catch(t){}}}function zr(t){t.g&&t.L&&(t.g.ontimeout=null),t.A&&(Lt.clearTimeout(t.A),t.A=null)}function Gr(t){return t.g?t.g.readyState:0}function Kr(t){try{if(!t.g)return null;if("response"in t.g)return t.g.response;switch(t.K){case Fr:case"text":return t.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in t.g)return t.g.mozResponseArrayBuffer}return null}catch(t){return null}}function Hr(t){let e="";return Ee(t,(function(t,n){e+=n,e+=":",e+=t,e+="\r\n"})),e}function Qr(t,e,n){t:{for(r in n){var r=!1;break t}r=!0}r||(n=Hr(n),"string"==typeof t?null!=n&&encodeURIComponent(String(n)):tr(t,e,n))}function Wr(t,e,n){return n&&n.internalChannelParams&&n.internalChannelParams[t]||e}function Yr(t){this.Ga=0,this.j=[],this.l=new hn,this.pa=this.wa=this.I=this.Y=this.g=this.Da=this.F=this.na=this.o=this.U=this.s=null,this.fb=this.W=0,this.cb=Wr("failFast",!1,t),this.G=this.v=this.u=this.m=this.h=null,this.aa=!0,this.Fa=this.V=-1,this.ba=this.A=this.C=0,this.ab=Wr("baseRetryDelayMs",5e3,t),this.hb=Wr("retryDelaySeedMs",1e4,t),this.eb=Wr("forwardChannelMaxRetries",2,t),this.xa=Wr("forwardChannelRequestTimeoutMs",2e4,t),this.va=t&&t.xmlHttpFactory||void 0,this.Ha=t&&t.dc||!1,this.L=void 0,this.J=t&&t.supportsCrossDomainXhr||!1,this.K="",this.i=new yr(t&&t.concurrentRequestLimit),this.Ja=new Ar,this.P=t&&t.fastHandshake||!1,this.O=t&&t.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.bb=t&&t.bc||!1,t&&t.Ea&&this.l.Ea(),t&&t.forceLongPolling&&(this.aa=!1),this.ca=!this.P&&this.aa&&t&&t.detectBufferingProxy||!1,this.qa=void 0,t&&t.longPollingTimeout&&0<t.longPollingTimeout&&(this.qa=t.longPollingTimeout),this.oa=void 0,this.S=0,this.M=!1,this.ma=this.B=null}function Xr(t){if(Zr(t),3==t.H){var e=t.W++,n=Yn(t.I);if(tr(n,"SID",t.K),tr(n,"RID",e),tr(n,"TYPE","terminate"),ni(t,n),(e=new kn(t,t.l,e)).L=2,e.v=er(Yn(n)),n=!1,Lt.navigator&&Lt.navigator.sendBeacon)try{n=Lt.navigator.sendBeacon(e.v.toString(),"")}catch(t){}!n&&Lt.Image&&((new Image).src=e.v,n=!0),n||(e.g=pi(e.l,null),e.g.ha(e.v)),e.G=Date.now(),jn(e)}di(t)}function Jr(t){t.g&&(oi(t),t.g.cancel(),t.g=null)}function Zr(t){Jr(t),t.u&&(Lt.clearTimeout(t.u),t.u=null),ci(t),t.i.cancel(),t.m&&("number"==typeof t.m&&Lt.clearTimeout(t.m),t.m=null)}function ti(t){if(!wr(t.i)&&!t.m){t.m=!0;var e=t.Na;We||Je(),Ye||(We(),Ye=!0),Xe.add(e,t),t.C=0}}function ei(t,e){var n;n=e?e.m:t.W++;const r=Yn(t.I);tr(r,"SID",t.K),tr(r,"RID",n),tr(r,"AID",t.V),ni(t,r),t.o&&t.s&&Qr(r,t.o,t.s),n=new kn(t,t.l,n,t.C+1),null===t.o&&(n.I=t.s),e&&(t.j=e.F.concat(t.j)),e=ri(t,n,1e3),n.setTimeout(Math.round(.5*t.xa)+Math.round(.5*t.xa*Math.random())),Tr(t.i,n),Pn(n,r,e)}function ni(t,e){t.na&&Ee(t.na,(function(t,n){tr(e,n,t)})),t.h&&Hn({},(function(t,n){tr(e,n,t)}))}function ri(t,e,n){n=Math.min(t.j.length,n);var r=t.h?qt(t.h.Va,t.h,t):null;t:{var i=t.j;let e=-1;for(;;){const t=["count="+n];-1==e?0<n?(e=i[0].g,t.push("ofs="+e)):e=0:t.push("ofs="+e);let s=!0;for(let o=0;o<n;o++){let n=i[o].g;const a=i[o].map;if(n-=e,0>n)e=Math.max(0,i[o].g-100),s=!1;else try{_r(a,t,"req"+n+"_")}catch(t){r&&r(a)}}if(s){r=t.join("&");break t}}}return t=t.j.splice(0,n),e.F=t,r}function ii(t){if(!t.g&&!t.u){t.ba=1;var e=t.Ma;We||Je(),Ye||(We(),Ye=!0),Xe.add(e,t),t.A=0}}function si(t){return!(t.g||t.u||3<=t.A||(t.ba++,t.u=bn(qt(t.Ma,t),hi(t,t.A)),t.A++,0))}function oi(t){null!=t.B&&(Lt.clearTimeout(t.B),t.B=null)}function ai(t){t.g=new kn(t,t.l,"rpc",t.ba),null===t.o&&(t.g.I=t.s),t.g.O=0;var e=Yn(t.wa);tr(e,"RID","rpc"),tr(e,"SID",t.K),tr(e,"AID",t.V),tr(e,"CI",t.G?"0":"1"),!t.G&&t.qa&&tr(e,"TO",t.qa),tr(e,"TYPE","xmlhttp"),ni(t,e),t.o&&t.s&&Qr(e,t.o,t.s),t.L&&t.g.setTimeout(t.L);var n=t.g;t=t.pa,n.L=1,n.v=er(Yn(e)),n.s=null,n.S=!0,Fn(n,t)}function ci(t){null!=t.v&&(Lt.clearTimeout(t.v),t.v=null)}function ui(t,e){var n=null;if(t.g==e){ci(t),oi(t),t.g=null;var r=2}else{if(!Er(t.i,e))return;n=e.F,Ir(t.i,e),r=1}if(0!=t.H)if(e.i)if(1==r){n=e.s?e.s.length:0,e=Date.now()-e.G;var i=t.C;je(r=pn(),new wn(r,n)),ti(t)}else ii(t);else if(3==(i=e.o)||0==i&&0<e.ca||!(1==r&&function(t,e){return!(br(t.i)>=t.i.j-(t.m?1:0)||(t.m?(t.j=e.F.concat(t.j),0):1==t.H||2==t.H||t.C>=(t.cb?0:t.eb)||(t.m=bn(qt(t.Na,t,e),hi(t,t.C)),t.C++,0)))}(t,e)||2==r&&si(t)))switch(n&&0<n.length&&(e=t.i,e.i=e.i.concat(n)),i){case 1:li(t,5);break;case 4:li(t,10);break;case 3:li(t,6);break;default:li(t,2)}}function hi(t,e){let n=t.ab+Math.floor(Math.random()*t.hb);return t.isActive()||(n*=2),n*e}function li(t,e){if(t.l.info("Error code "+e),2==e){var n=null;t.h&&(n=null);var r=qt(t.pb,t);n||(n=new Wn("//www.google.com/images/cleardot.gif"),Lt.location&&"http"==Lt.location.protocol||Xn(n,"https"),er(n)),function(t,e){const n=new hn;if(Lt.Image){const r=new Image;r.onload=$t(xr,n,r,"TestLoadImage: loaded",!0,e),r.onerror=$t(xr,n,r,"TestLoadImage: error",!1,e),r.onabort=$t(xr,n,r,"TestLoadImage: abort",!1,e),r.ontimeout=$t(xr,n,r,"TestLoadImage: timeout",!1,e),Lt.setTimeout((function(){r.ontimeout&&r.ontimeout()}),1e4),r.src=t}else e(!1)}(n.toString(),r)}else vn(2);t.H=0,t.h&&t.h.za(e),di(t),Zr(t)}function di(t){if(t.H=0,t.ma=[],t.h){const e=Sr(t.i);0==e.length&&0==t.j.length||(Qt(t.ma,e),Qt(t.ma,t.j),t.i.i.length=0,Ht(t.j),t.j.length=0),t.h.ya()}}function fi(t,e,n){var r=n instanceof Wn?Yn(n):new Wn(n);if(""!=r.g)e&&(r.g=e+"."+r.g),Jn(r,r.m);else{var i=Lt.location;r=i.protocol,e=e?e+"."+i.hostname:i.hostname,i=+i.port;var s=new Wn(null);r&&Xn(s,r),e&&(s.g=e),i&&Jn(s,i),n&&(s.l=n),r=s}return n=t.F,e=t.Da,n&&e&&tr(r,n,e),tr(r,"VER",t.ra),ni(t,r),r}function pi(t,e,n){if(e&&!t.J)throw Error("Can't create secondary domain capable XhrIo object.");return(e=n&&t.Ha&&!t.va?new Pr(new Nr({ob:!0})):new Pr(t.va)).Oa(t.J),e}function gi(){}function mi(){if(se&&!(10<=Number(pe)))throw Error("Environmental error: no available transport.")}function yi(t,e){Be.call(this),this.g=new Yr(e),this.l=t,this.h=e&&e.messageUrlParams||null,t=e&&e.messageHeaders||null,e&&e.clientProtocolHeaderRequired&&(t?t["X-Client-Protocol"]="webchannel":t={"X-Client-Protocol":"webchannel"}),this.g.s=t,t=e&&e.initMessageHeaders||null,e&&e.messageContentType&&(t?t["X-WebChannel-Content-Type"]=e.messageContentType:t={"X-WebChannel-Content-Type":e.messageContentType}),e&&e.Ca&&(t?t["X-WebChannel-Client-Profile"]=e.Ca:t={"X-WebChannel-Client-Profile":e.Ca}),this.g.U=t,(t=e&&e.cc)&&!Xt(t)&&(this.g.o=t),this.A=e&&e.supportsCrossDomainXhr||!1,this.v=e&&e.sendRawJson||!1,(e=e&&e.httpSessionIdParam)&&!Xt(e)&&(this.g.F=e,null!==(t=this.h)&&e in t&&e in(t=this.h)&&delete t[e]),this.j=new bi(this)}function vi(t){xn.call(this),t.__headers__&&(this.headers=t.__headers__,this.statusCode=t.__status__,delete t.__headers__,delete t.__status__);var e=t.__sm__;if(e){t:{for(const n in e){t=n;break t}t=void 0}(this.i=t)&&(t=this.i,e=null!==e&&t in e?e[t]:void 0),this.data=e}else this.data=t}function wi(){Nn.call(this),this.status=1}function bi(t){this.g=t}function Ei(){this.blockSize=-1,this.blockSize=64,this.g=Array(4),this.m=Array(this.blockSize),this.i=this.h=0,this.reset()}function Ti(t,e,n){n||(n=0);var r=Array(16);if("string"==typeof e)for(var i=0;16>i;++i)r[i]=e.charCodeAt(n++)|e.charCodeAt(n++)<<8|e.charCodeAt(n++)<<16|e.charCodeAt(n++)<<24;else for(i=0;16>i;++i)r[i]=e[n++]|e[n++]<<8|e[n++]<<16|e[n++]<<24;e=t.g[0],n=t.g[1],i=t.g[2];var s=t.g[3],o=e+(s^n&(i^s))+r[0]+3614090360&4294967295;o=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=(n=(i=(s=(e=n+(o<<7&4294967295|o>>>25))+((o=s+(i^e&(n^i))+r[1]+3905402710&4294967295)<<12&4294967295|o>>>20))+((o=i+(n^s&(e^n))+r[2]+606105819&4294967295)<<17&4294967295|o>>>15))+((o=n+(e^i&(s^e))+r[3]+3250441966&4294967295)<<22&4294967295|o>>>10))+((o=e+(s^n&(i^s))+r[4]+4118548399&4294967295)<<7&4294967295|o>>>25))+((o=s+(i^e&(n^i))+r[5]+1200080426&4294967295)<<12&4294967295|o>>>20))+((o=i+(n^s&(e^n))+r[6]+2821735955&4294967295)<<17&4294967295|o>>>15))+((o=n+(e^i&(s^e))+r[7]+4249261313&4294967295)<<22&4294967295|o>>>10))+((o=e+(s^n&(i^s))+r[8]+1770035416&4294967295)<<7&4294967295|o>>>25))+((o=s+(i^e&(n^i))+r[9]+2336552879&4294967295)<<12&4294967295|o>>>20))+((o=i+(n^s&(e^n))+r[10]+4294925233&4294967295)<<17&4294967295|o>>>15))+((o=n+(e^i&(s^e))+r[11]+2304563134&4294967295)<<22&4294967295|o>>>10))+((o=e+(s^n&(i^s))+r[12]+1804603682&4294967295)<<7&4294967295|o>>>25))+((o=s+(i^e&(n^i))+r[13]+4254626195&4294967295)<<12&4294967295|o>>>20))+((o=i+(n^s&(e^n))+r[14]+2792965006&4294967295)<<17&4294967295|o>>>15))+((o=n+(e^i&(s^e))+r[15]+1236535329&4294967295)<<22&4294967295|o>>>10))+((o=e+(i^s&(n^i))+r[1]+4129170786&4294967295)<<5&4294967295|o>>>27))+((o=s+(n^i&(e^n))+r[6]+3225465664&4294967295)<<9&4294967295|o>>>23))+((o=i+(e^n&(s^e))+r[11]+643717713&4294967295)<<14&4294967295|o>>>18))+((o=n+(s^e&(i^s))+r[0]+3921069994&4294967295)<<20&4294967295|o>>>12))+((o=e+(i^s&(n^i))+r[5]+3593408605&4294967295)<<5&4294967295|o>>>27))+((o=s+(n^i&(e^n))+r[10]+38016083&4294967295)<<9&4294967295|o>>>23))+((o=i+(e^n&(s^e))+r[15]+3634488961&4294967295)<<14&4294967295|o>>>18))+((o=n+(s^e&(i^s))+r[4]+3889429448&4294967295)<<20&4294967295|o>>>12))+((o=e+(i^s&(n^i))+r[9]+568446438&4294967295)<<5&4294967295|o>>>27))+((o=s+(n^i&(e^n))+r[14]+3275163606&4294967295)<<9&4294967295|o>>>23))+((o=i+(e^n&(s^e))+r[3]+4107603335&4294967295)<<14&4294967295|o>>>18))+((o=n+(s^e&(i^s))+r[8]+1163531501&4294967295)<<20&4294967295|o>>>12))+((o=e+(i^s&(n^i))+r[13]+2850285829&4294967295)<<5&4294967295|o>>>27))+((o=s+(n^i&(e^n))+r[2]+4243563512&4294967295)<<9&4294967295|o>>>23))+((o=i+(e^n&(s^e))+r[7]+1735328473&4294967295)<<14&4294967295|o>>>18))+((o=n+(s^e&(i^s))+r[12]+2368359562&4294967295)<<20&4294967295|o>>>12))+((o=e+(n^i^s)+r[5]+4294588738&4294967295)<<4&4294967295|o>>>28))+((o=s+(e^n^i)+r[8]+2272392833&4294967295)<<11&4294967295|o>>>21))+((o=i+(s^e^n)+r[11]+1839030562&4294967295)<<16&4294967295|o>>>16))+((o=n+(i^s^e)+r[14]+4259657740&4294967295)<<23&4294967295|o>>>9))+((o=e+(n^i^s)+r[1]+2763975236&4294967295)<<4&4294967295|o>>>28))+((o=s+(e^n^i)+r[4]+1272893353&4294967295)<<11&4294967295|o>>>21))+((o=i+(s^e^n)+r[7]+4139469664&4294967295)<<16&4294967295|o>>>16))+((o=n+(i^s^e)+r[10]+3200236656&4294967295)<<23&4294967295|o>>>9))+((o=e+(n^i^s)+r[13]+681279174&4294967295)<<4&4294967295|o>>>28))+((o=s+(e^n^i)+r[0]+3936430074&4294967295)<<11&4294967295|o>>>21))+((o=i+(s^e^n)+r[3]+3572445317&4294967295)<<16&4294967295|o>>>16))+((o=n+(i^s^e)+r[6]+76029189&4294967295)<<23&4294967295|o>>>9))+((o=e+(n^i^s)+r[9]+3654602809&4294967295)<<4&4294967295|o>>>28))+((o=s+(e^n^i)+r[12]+3873151461&4294967295)<<11&4294967295|o>>>21))+((o=i+(s^e^n)+r[15]+530742520&4294967295)<<16&4294967295|o>>>16))+((o=n+(i^s^e)+r[2]+3299628645&4294967295)<<23&4294967295|o>>>9))+((o=e+(i^(n|~s))+r[0]+4096336452&4294967295)<<6&4294967295|o>>>26))+((o=s+(n^(e|~i))+r[7]+1126891415&4294967295)<<10&4294967295|o>>>22))+((o=i+(e^(s|~n))+r[14]+2878612391&4294967295)<<15&4294967295|o>>>17))+((o=n+(s^(i|~e))+r[5]+4237533241&4294967295)<<21&4294967295|o>>>11))+((o=e+(i^(n|~s))+r[12]+1700485571&4294967295)<<6&4294967295|o>>>26))+((o=s+(n^(e|~i))+r[3]+2399980690&4294967295)<<10&4294967295|o>>>22))+((o=i+(e^(s|~n))+r[10]+4293915773&4294967295)<<15&4294967295|o>>>17))+((o=n+(s^(i|~e))+r[1]+2240044497&4294967295)<<21&4294967295|o>>>11))+((o=e+(i^(n|~s))+r[8]+1873313359&4294967295)<<6&4294967295|o>>>26))+((o=s+(n^(e|~i))+r[15]+4264355552&4294967295)<<10&4294967295|o>>>22))+((o=i+(e^(s|~n))+r[6]+2734768916&4294967295)<<15&4294967295|o>>>17))+((o=n+(s^(i|~e))+r[13]+1309151649&4294967295)<<21&4294967295|o>>>11))+((s=(e=n+((o=e+(i^(n|~s))+r[4]+4149444226&4294967295)<<6&4294967295|o>>>26))+((o=s+(n^(e|~i))+r[11]+3174756917&4294967295)<<10&4294967295|o>>>22))^((i=s+((o=i+(e^(s|~n))+r[2]+718787259&4294967295)<<15&4294967295|o>>>17))|~e))+r[9]+3951481745&4294967295,t.g[0]=t.g[0]+e&4294967295,t.g[1]=t.g[1]+(i+(o<<21&4294967295|o>>>11))&4294967295,t.g[2]=t.g[2]+i&4294967295,t.g[3]=t.g[3]+s&4294967295}function Ii(t,e){this.h=e;for(var n=[],r=!0,i=t.length-1;0<=i;i--){var s=0|t[i];r&&s==e||(n[i]=s,r=!1)}this.g=n}(kt=Pr.prototype).Oa=function(t){this.M=t},kt.ha=function(t,e,n,r){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.I+"; newUri="+t);e=e?e.toUpperCase():"GET",this.I=t,this.j="",this.m=0,this.F=!1,this.h=!0,this.g=this.u?this.u.g():An.g(),this.C=this.u?Sn(this.u):Sn(An),this.g.onreadystatechange=qt(this.La,this);try{this.G=!0,this.g.open(e,String(t),!0),this.G=!1}catch(t){return void Br(this,t)}if(t=n||"",n=new Map(this.headers),r)if(Object.getPrototypeOf(r)===Object.prototype)for(var i in r)n.set(i,r[i]);else{if("function"!=typeof r.keys||"function"!=typeof r.get)throw Error("Unknown input type for opt_headers: "+String(r));for(const t of r.keys())n.set(t,r.get(t))}r=Array.from(n.keys()).find((t=>"content-type"==t.toLowerCase())),i=Lt.FormData&&t instanceof Lt.FormData,!(0<=Kt(Ur,e))||r||i||n.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(const[t,e]of n)this.g.setRequestHeader(t,e);this.K&&(this.g.responseType=this.K),"withCredentials"in this.g&&this.g.withCredentials!==this.M&&(this.g.withCredentials=this.M);try{zr(this),0<this.B&&((this.L=function(t){return se&&"number"==typeof t.timeout&&void 0!==t.ontimeout}(this.g))?(this.g.timeout=this.B,this.g.ontimeout=qt(this.ua,this)):this.A=nn(this.ua,this.B,this)),this.v=!0,this.g.send(t),this.v=!1}catch(t){Br(this,t)}},kt.ua=function(){void 0!==Mt&&this.g&&(this.j="Timed out after "+this.B+"ms, aborting",this.m=8,je(this,"timeout"),this.abort(8))},kt.abort=function(t){this.g&&this.h&&(this.h=!1,this.l=!0,this.g.abort(),this.l=!1,this.m=t||7,je(this,"complete"),je(this,"abort"),$r(this))},kt.N=function(){this.g&&(this.h&&(this.h=!1,this.l=!0,this.g.abort(),this.l=!1),$r(this,!0)),Pr.$.N.call(this)},kt.La=function(){this.s||(this.G||this.v||this.l?qr(this):this.kb())},kt.kb=function(){qr(this)},kt.isActive=function(){return!!this.g},kt.da=function(){try{return 2<Gr(this)?this.g.status:-1}catch(t){return-1}},kt.ja=function(){try{return this.g?this.g.responseText:""}catch(t){return""}},kt.Wa=function(t){if(this.g){var e=this.g.responseText;return t&&0==e.indexOf(t)&&(e=e.substring(t.length)),Lr(e)}},kt.Ia=function(){return this.m},kt.Sa=function(){return"string"==typeof this.j?this.j:String(this.j)},(kt=Yr.prototype).ra=8,kt.H=1,kt.Na=function(t){if(this.m)if(this.m=null,1==this.H){if(!t){this.W=Math.floor(1e5*Math.random()),t=this.W++;const i=new kn(this,this.l,t);let s=this.s;if(this.U&&(s?(s=Te(s),Se(s,this.U)):s=this.U),null!==this.o||this.O||(i.I=s,s=null),this.P)t:{for(var e=0,n=0;n<this.j.length;n++){var r=this.j[n];if(void 0===(r="__data__"in r.map&&"string"==typeof(r=r.map.__data__)?r.length:void 0))break;if(4096<(e+=r)){e=n;break t}if(4096===e||n===this.j.length-1){e=n+1;break t}}e=1e3}else e=1e3;e=ri(this,i,e),tr(n=Yn(this.I),"RID",t),tr(n,"CVER",22),this.F&&tr(n,"X-HTTP-Session-Id",this.F),ni(this,n),s&&(this.O?e="headers="+encodeURIComponent(String(Hr(s)))+"&"+e:this.o&&Qr(n,this.o,s)),Tr(this.i,i),this.bb&&tr(n,"TYPE","init"),this.P?(tr(n,"$req",e),tr(n,"SID","null"),i.aa=!0,Pn(i,n,null)):Pn(i,n,e),this.H=2}}else 3==this.H&&(t?ei(this,t):0==this.j.length||wr(this.i)||ei(this))},kt.Ma=function(){if(this.u=null,ai(this),this.ca&&!(this.M||null==this.g||0>=this.S)){var t=2*this.S;this.l.info("BP detection timer enabled: "+t),this.B=bn(qt(this.jb,this),t)}},kt.jb=function(){this.B&&(this.B=null,this.l.info("BP detection timeout reached."),this.l.info("Buffering proxy detected and switch to long-polling!"),this.G=!1,this.M=!0,vn(10),Jr(this),ai(this))},kt.ib=function(){null!=this.v&&(this.v=null,Jr(this),si(this),vn(19))},kt.pb=function(t){t?(this.l.info("Successfully pinged google.com"),vn(2)):(this.l.info("Failed to ping google.com"),vn(1))},kt.isActive=function(){return!!this.h&&this.h.isActive(this)},(kt=gi.prototype).Ba=function(){},kt.Aa=function(){},kt.za=function(){},kt.ya=function(){},kt.isActive=function(){return!0},kt.Va=function(){},mi.prototype.g=function(t,e){return new yi(t,e)},zt(yi,Be),yi.prototype.m=function(){this.g.h=this.j,this.A&&(this.g.J=!0);var t=this.g,e=this.l,n=this.h||void 0;vn(0),t.Y=e,t.na=n||{},t.G=t.aa,t.I=fi(t,null,t.Y),ti(t)},yi.prototype.close=function(){Xr(this.g)},yi.prototype.u=function(t){var e=this.g;if("string"==typeof t){var n={};n.__data__=t,t=n}else this.v&&((n={}).__data__=$e(t),t=n);e.j.push(new mr(e.fb++,t)),3==e.H&&ti(e)},yi.prototype.N=function(){this.g.h=null,delete this.j,Xr(this.g),delete this.g,yi.$.N.call(this)},zt(vi,xn),zt(wi,Nn),zt(bi,gi),bi.prototype.Ba=function(){je(this.g,"a")},bi.prototype.Aa=function(t){je(this.g,new vi(t))},bi.prototype.za=function(t){je(this.g,new wi)},bi.prototype.ya=function(){je(this.g,"b")},zt(Ei,(function(){this.blockSize=-1})),Ei.prototype.reset=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.i=this.h=0},Ei.prototype.j=function(t,e){void 0===e&&(e=t.length);for(var n=e-this.blockSize,r=this.m,i=this.h,s=0;s<e;){if(0==i)for(;s<=n;)Ti(this,t,s),s+=this.blockSize;if("string"==typeof t){for(;s<e;)if(r[i++]=t.charCodeAt(s++),i==this.blockSize){Ti(this,r),i=0;break}}else for(;s<e;)if(r[i++]=t[s++],i==this.blockSize){Ti(this,r),i=0;break}}this.h=i,this.i+=e},Ei.prototype.l=function(){var t=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);t[0]=128;for(var e=1;e<t.length-8;++e)t[e]=0;var n=8*this.i;for(e=t.length-8;e<t.length;++e)t[e]=255&n,n/=256;for(this.j(t),t=Array(16),e=n=0;4>e;++e)for(var r=0;32>r;r+=8)t[n++]=this.g[e]>>>r&255;return t};var Si={};function Ci(t){return-128<=t&&128>t?function(t){var e=Si;return Object.prototype.hasOwnProperty.call(e,t)?e[t]:e[t]=function(t){return new Ii([0|t],0>t?-1:0)}(t)}(t):new Ii([0|t],0>t?-1:0)}function Ai(t){if(isNaN(t)||!isFinite(t))return xi;if(0>t)return Oi(Ai(-t));for(var e=[],n=1,r=0;t>=n;r++)e[r]=t/n|0,n*=_i;return new Ii(e,0)}var _i=4294967296,xi=Ci(0),Ni=Ci(1),Di=Ci(16777216);function ki(t){if(0!=t.h)return!1;for(var e=0;e<t.g.length;e++)if(0!=t.g[e])return!1;return!0}function Ri(t){return-1==t.h}function Oi(t){for(var e=t.g.length,n=[],r=0;r<e;r++)n[r]=~t.g[r];return new Ii(n,~t.h).add(Ni)}function Mi(t,e){return t.add(Oi(e))}function Li(t,e){for(;(65535&t[e])!=t[e];)t[e+1]+=t[e]>>>16,t[e]&=65535,e++}function Pi(t,e){this.g=t,this.h=e}function Fi(t,e){if(ki(e))throw Error("division by zero");if(ki(t))return new Pi(xi,xi);if(Ri(t))return e=Fi(Oi(t),e),new Pi(Oi(e.g),Oi(e.h));if(Ri(e))return e=Fi(t,Oi(e)),new Pi(Oi(e.g),e.h);if(30<t.g.length){if(Ri(t)||Ri(e))throw Error("slowDivide_ only works with positive integers.");for(var n=Ni,r=e;0>=r.X(t);)n=Vi(n),r=Vi(r);var i=Ui(n,1),s=Ui(r,1);for(r=Ui(r,2),n=Ui(n,2);!ki(r);){var o=s.add(r);0>=o.X(t)&&(i=i.add(n),s=o),r=Ui(r,1),n=Ui(n,1)}return e=Mi(t,i.R(e)),new Pi(i,e)}for(i=xi;0<=t.X(e);){for(n=Math.max(1,Math.floor(t.ea()/e.ea())),r=48>=(r=Math.ceil(Math.log(n)/Math.LN2))?1:Math.pow(2,r-48),o=(s=Ai(n)).R(e);Ri(o)||0<o.X(t);)o=(s=Ai(n-=r)).R(e);ki(s)&&(s=Ni),i=i.add(s),t=Mi(t,o)}return new Pi(i,t)}function Vi(t){for(var e=t.g.length+1,n=[],r=0;r<e;r++)n[r]=t.D(r)<<1|t.D(r-1)>>>31;return new Ii(n,t.h)}function Ui(t,e){var n=e>>5;e%=32;for(var r=t.g.length-n,i=[],s=0;s<r;s++)i[s]=0<e?t.D(s+n)>>>e|t.D(s+n+1)<<32-e:t.D(s+n);return new Ii(i,t.h)}(kt=Ii.prototype).ea=function(){if(Ri(this))return-Oi(this).ea();for(var t=0,e=1,n=0;n<this.g.length;n++){var r=this.D(n);t+=(0<=r?r:_i+r)*e,e*=_i}return t},kt.toString=function(t){if(2>(t=t||10)||36<t)throw Error("radix out of range: "+t);if(ki(this))return"0";if(Ri(this))return"-"+Oi(this).toString(t);for(var e=Ai(Math.pow(t,6)),n=this,r="";;){var i=Fi(n,e).g,s=((0<(n=Mi(n,i.R(e))).g.length?n.g[0]:n.h)>>>0).toString(t);if(ki(n=i))return s+r;for(;6>s.length;)s="0"+s;r=s+r}},kt.D=function(t){return 0>t?0:t<this.g.length?this.g[t]:this.h},kt.X=function(t){return Ri(t=Mi(this,t))?-1:ki(t)?0:1},kt.abs=function(){return Ri(this)?Oi(this):this},kt.add=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0,i=0;i<=e;i++){var s=r+(65535&this.D(i))+(65535&t.D(i)),o=(s>>>16)+(this.D(i)>>>16)+(t.D(i)>>>16);r=o>>>16,s&=65535,o&=65535,n[i]=o<<16|s}return new Ii(n,-2147483648&n[n.length-1]?-1:0)},kt.R=function(t){if(ki(this)||ki(t))return xi;if(Ri(this))return Ri(t)?Oi(this).R(Oi(t)):Oi(Oi(this).R(t));if(Ri(t))return Oi(this.R(Oi(t)));if(0>this.X(Di)&&0>t.X(Di))return Ai(this.ea()*t.ea());for(var e=this.g.length+t.g.length,n=[],r=0;r<2*e;r++)n[r]=0;for(r=0;r<this.g.length;r++)for(var i=0;i<t.g.length;i++){var s=this.D(r)>>>16,o=65535&this.D(r),a=t.D(i)>>>16,c=65535&t.D(i);n[2*r+2*i]+=o*c,Li(n,2*r+2*i),n[2*r+2*i+1]+=s*c,Li(n,2*r+2*i+1),n[2*r+2*i+1]+=o*a,Li(n,2*r+2*i+1),n[2*r+2*i+2]+=s*a,Li(n,2*r+2*i+2)}for(r=0;r<e;r++)n[r]=n[2*r+1]<<16|n[2*r];for(r=e;r<2*e;r++)n[r]=0;return new Ii(n,0)},kt.gb=function(t){return Fi(this,t).h},kt.and=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0;r<e;r++)n[r]=this.D(r)&t.D(r);return new Ii(n,this.h&t.h)},kt.or=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0;r<e;r++)n[r]=this.D(r)|t.D(r);return new Ii(n,this.h|t.h)},kt.xor=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],r=0;r<e;r++)n[r]=this.D(r)^t.D(r);return new Ii(n,this.h^t.h)},mi.prototype.createWebChannel=mi.prototype.g,yi.prototype.send=yi.prototype.u,yi.prototype.open=yi.prototype.m,yi.prototype.close=yi.prototype.close,En.NO_ERROR=0,En.TIMEOUT=8,En.HTTP_ERROR=6,Tn.COMPLETE="complete",Cn.EventType=_n,_n.OPEN="a",_n.CLOSE="b",_n.ERROR="c",_n.MESSAGE="d",Be.prototype.listen=Be.prototype.O,Pr.prototype.listenOnce=Pr.prototype.P,Pr.prototype.getLastError=Pr.prototype.Sa,Pr.prototype.getLastErrorCode=Pr.prototype.Ia,Pr.prototype.getStatus=Pr.prototype.da,Pr.prototype.getResponseJson=Pr.prototype.Wa,Pr.prototype.getResponseText=Pr.prototype.ja,Pr.prototype.send=Pr.prototype.ha,Pr.prototype.setWithCredentials=Pr.prototype.Oa,Ei.prototype.digest=Ei.prototype.l,Ei.prototype.reset=Ei.prototype.reset,Ei.prototype.update=Ei.prototype.j,Ii.prototype.add=Ii.prototype.add,Ii.prototype.multiply=Ii.prototype.R,Ii.prototype.modulo=Ii.prototype.gb,Ii.prototype.compare=Ii.prototype.X,Ii.prototype.toNumber=Ii.prototype.ea,Ii.prototype.toString=Ii.prototype.toString,Ii.prototype.getBits=Ii.prototype.D,Ii.fromNumber=Ai,Ii.fromString=function t(e,n){if(0==e.length)throw Error("number format error: empty string");if(2>(n=n||10)||36<n)throw Error("radix out of range: "+n);if("-"==e.charAt(0))return Oi(t(e.substring(1),n));if(0<=e.indexOf("-"))throw Error('number format error: interior "-" character');for(var r=Ai(Math.pow(n,8)),i=xi,s=0;s<e.length;s+=8){var o=Math.min(8,e.length-s),a=parseInt(e.substring(s,s+o),n);8>o?(o=Ai(Math.pow(n,o)),i=i.R(o).add(Ai(a))):i=(i=i.R(r)).add(Ai(a))}return i};var Bi=Ot.createWebChannelTransport=function(){return new mi},ji=Ot.getStatEventTarget=function(){return pn()},qi=Ot.ErrorCode=En,$i=Ot.EventType=Tn,zi=Ot.Event=dn,Gi=Ot.Stat={xb:0,Ab:1,Bb:2,Ub:3,Zb:4,Wb:5,Xb:6,Vb:7,Tb:8,Yb:9,PROXY:10,NOPROXY:11,Rb:12,Nb:13,Ob:14,Mb:15,Pb:16,Qb:17,tb:18,sb:19,ub:20},Ki=Ot.FetchXmlHttpFactory=Nr,Hi=Ot.WebChannel=Cn,Qi=Ot.XhrIo=Pr,Wi=Ot.Md5=Ei,Yi=Ot.Integer=Ii;const Xi="@firebase/firestore";class Ji{constructor(t){this.uid=t}isAuthenticated(){return null!=this.uid}toKey(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"}isEqual(t){return t.uid===this.uid}}Ji.UNAUTHENTICATED=new Ji(null),Ji.GOOGLE_CREDENTIALS=new Ji("google-credentials-uid"),Ji.FIRST_PARTY=new Ji("first-party-uid"),Ji.MOCK_USER=new Ji("mock-user");let Zi="9.23.0";const ts=new S("@firebase/firestore");function es(){return ts.logLevel}function ns(t,...e){if(ts.logLevel<=w.DEBUG){const n=e.map(ss);ts.debug(`Firestore (${Zi}): ${t}`,...n)}}function rs(t,...e){if(ts.logLevel<=w.ERROR){const n=e.map(ss);ts.error(`Firestore (${Zi}): ${t}`,...n)}}function is(t,...e){if(ts.logLevel<=w.WARN){const n=e.map(ss);ts.warn(`Firestore (${Zi}): ${t}`,...n)}}function ss(t){if("string"==typeof t)return t;try{return e=t,JSON.stringify(e)}catch(e){return t}var e}function os(t="Unexpected state"){const e=`FIRESTORE (${Zi}) INTERNAL ASSERTION FAILED: `+t;throw rs(e),new Error(e)}function as(t,e){t||os()}function cs(t,e){return t}const us={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class hs extends f{constructor(t,e){super(t,e),this.code=t,this.message=e,this.toString=()=>`${this.name}: [code=${this.code}]: ${this.message}`}}class ls{constructor(){this.promise=new Promise(((t,e)=>{this.resolve=t,this.reject=e}))}}class ds{constructor(t,e){this.user=e,this.type="OAuth",this.headers=new Map,this.headers.set("Authorization",`Bearer ${t}`)}}class fs{getToken(){return Promise.resolve(null)}invalidateToken(){}start(t,e){t.enqueueRetryable((()=>e(Ji.UNAUTHENTICATED)))}shutdown(){}}class ps{constructor(t){this.t=t,this.currentUser=Ji.UNAUTHENTICATED,this.i=0,this.forceRefresh=!1,this.auth=null}start(t,e){let n=this.i;const r=t=>this.i!==n?(n=this.i,e(t)):Promise.resolve();let i=new ls;this.o=()=>{this.i++,this.currentUser=this.u(),i.resolve(),i=new ls,t.enqueueRetryable((()=>r(this.currentUser)))};const s=()=>{const e=i;t.enqueueRetryable((async()=>{await e.promise,await r(this.currentUser)}))},o=t=>{ns("FirebaseAuthCredentialsProvider","Auth detected"),this.auth=t,this.auth.addAuthTokenListener(this.o),s()};this.t.onInit((t=>o(t))),setTimeout((()=>{if(!this.auth){const t=this.t.getImmediate({optional:!0});t?o(t):(ns("FirebaseAuthCredentialsProvider","Auth not yet detected"),i.resolve(),i=new ls)}}),0),s()}getToken(){const t=this.i,e=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(e).then((e=>this.i!==t?(ns("FirebaseAuthCredentialsProvider","getToken aborted due to token change."),this.getToken()):e?(as("string"==typeof e.accessToken),new ds(e.accessToken,this.currentUser)):null)):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.auth&&this.auth.removeAuthTokenListener(this.o)}u(){const t=this.auth&&this.auth.getUid();return as(null===t||"string"==typeof t),new Ji(t)}}class gs{constructor(t,e,n){this.h=t,this.l=e,this.m=n,this.type="FirstParty",this.user=Ji.FIRST_PARTY,this.g=new Map}p(){return this.m?this.m():null}get headers(){this.g.set("X-Goog-AuthUser",this.h);const t=this.p();return t&&this.g.set("Authorization",t),this.l&&this.g.set("X-Goog-Iam-Authorization-Token",this.l),this.g}}class ms{constructor(t,e,n){this.h=t,this.l=e,this.m=n}getToken(){return Promise.resolve(new gs(this.h,this.l,this.m))}start(t,e){t.enqueueRetryable((()=>e(Ji.FIRST_PARTY)))}shutdown(){}invalidateToken(){}}class ys{constructor(t){this.value=t,this.type="AppCheck",this.headers=new Map,t&&t.length>0&&this.headers.set("x-firebase-appcheck",this.value)}}class vs{constructor(t){this.I=t,this.forceRefresh=!1,this.appCheck=null,this.T=null}start(t,e){const n=t=>{null!=t.error&&ns("FirebaseAppCheckTokenProvider",`Error getting App Check token; using placeholder token instead. Error: ${t.error.message}`);const n=t.token!==this.T;return this.T=t.token,ns("FirebaseAppCheckTokenProvider",`Received ${n?"new":"existing"} token.`),n?e(t.token):Promise.resolve()};this.o=e=>{t.enqueueRetryable((()=>n(e)))};const r=t=>{ns("FirebaseAppCheckTokenProvider","AppCheck detected"),this.appCheck=t,this.appCheck.addTokenListener(this.o)};this.I.onInit((t=>r(t))),setTimeout((()=>{if(!this.appCheck){const t=this.I.getImmediate({optional:!0});t?r(t):ns("FirebaseAppCheckTokenProvider","AppCheck not yet detected")}}),0)}getToken(){const t=this.forceRefresh;return this.forceRefresh=!1,this.appCheck?this.appCheck.getToken(t).then((t=>t?(as("string"==typeof t.token),this.T=t.token,new ys(t.token)):null)):Promise.resolve(null)}invalidateToken(){this.forceRefresh=!0}shutdown(){this.appCheck&&this.appCheck.removeTokenListener(this.o)}}function ws(t){const e="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(t);if(e&&"function"==typeof e.getRandomValues)e.getRandomValues(n);else for(let e=0;e<t;e++)n[e]=Math.floor(256*Math.random());return n}class bs{static A(){const t=62*Math.floor(256/62);let e="";for(;e.length<20;){const n=ws(40);for(let r=0;r<n.length;++r)e.length<20&&n[r]<t&&(e+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(n[r]%62))}return e}}function Es(t,e){return t<e?-1:t>e?1:0}function Ts(t,e,n){return t.length===e.length&&t.every(((t,r)=>n(t,e[r])))}class Is{constructor(t,e){if(this.seconds=t,this.nanoseconds=e,e<0)throw new hs(us.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+e);if(e>=1e9)throw new hs(us.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+e);if(t<-62135596800)throw new hs(us.INVALID_ARGUMENT,"Timestamp seconds out of range: "+t);if(t>=253402300800)throw new hs(us.INVALID_ARGUMENT,"Timestamp seconds out of range: "+t)}static now(){return Is.fromMillis(Date.now())}static fromDate(t){return Is.fromMillis(t.getTime())}static fromMillis(t){const e=Math.floor(t/1e3),n=Math.floor(1e6*(t-1e3*e));return new Is(e,n)}toDate(){return new Date(this.toMillis())}toMillis(){return 1e3*this.seconds+this.nanoseconds/1e6}_compareTo(t){return this.seconds===t.seconds?Es(this.nanoseconds,t.nanoseconds):Es(this.seconds,t.seconds)}isEqual(t){return t.seconds===this.seconds&&t.nanoseconds===this.nanoseconds}toString(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"}toJSON(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}}valueOf(){const t=this.seconds- -62135596800;return String(t).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")}}class Ss{constructor(t){this.timestamp=t}static fromTimestamp(t){return new Ss(t)}static min(){return new Ss(new Is(0,0))}static max(){return new Ss(new Is(253402300799,999999999))}compareTo(t){return this.timestamp._compareTo(t.timestamp)}isEqual(t){return this.timestamp.isEqual(t.timestamp)}toMicroseconds(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3}toString(){return"SnapshotVersion("+this.timestamp.toString()+")"}toTimestamp(){return this.timestamp}}class Cs{constructor(t,e,n){void 0===e?e=0:e>t.length&&os(),void 0===n?n=t.length-e:n>t.length-e&&os(),this.segments=t,this.offset=e,this.len=n}get length(){return this.len}isEqual(t){return 0===Cs.comparator(this,t)}child(t){const e=this.segments.slice(this.offset,this.limit());return t instanceof Cs?t.forEach((t=>{e.push(t)})):e.push(t),this.construct(e)}limit(){return this.offset+this.length}popFirst(t){return t=void 0===t?1:t,this.construct(this.segments,this.offset+t,this.length-t)}popLast(){return this.construct(this.segments,this.offset,this.length-1)}firstSegment(){return this.segments[this.offset]}lastSegment(){return this.get(this.length-1)}get(t){return this.segments[this.offset+t]}isEmpty(){return 0===this.length}isPrefixOf(t){if(t.length<this.length)return!1;for(let e=0;e<this.length;e++)if(this.get(e)!==t.get(e))return!1;return!0}isImmediateParentOf(t){if(this.length+1!==t.length)return!1;for(let e=0;e<this.length;e++)if(this.get(e)!==t.get(e))return!1;return!0}forEach(t){for(let e=this.offset,n=this.limit();e<n;e++)t(this.segments[e])}toArray(){return this.segments.slice(this.offset,this.limit())}static comparator(t,e){const n=Math.min(t.length,e.length);for(let r=0;r<n;r++){const n=t.get(r),i=e.get(r);if(n<i)return-1;if(n>i)return 1}return t.length<e.length?-1:t.length>e.length?1:0}}class As extends Cs{construct(t,e,n){return new As(t,e,n)}canonicalString(){return this.toArray().join("/")}toString(){return this.canonicalString()}static fromString(...t){const e=[];for(const n of t){if(n.indexOf("//")>=0)throw new hs(us.INVALID_ARGUMENT,`Invalid segment (${n}). Paths must not contain // in them.`);e.push(...n.split("/").filter((t=>t.length>0)))}return new As(e)}static emptyPath(){return new As([])}}const _s=/^[_a-zA-Z][_a-zA-Z0-9]*$/;class xs extends Cs{construct(t,e,n){return new xs(t,e,n)}static isValidIdentifier(t){return _s.test(t)}canonicalString(){return this.toArray().map((t=>(t=t.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),xs.isValidIdentifier(t)||(t="`"+t+"`"),t))).join(".")}toString(){return this.canonicalString()}isKeyField(){return 1===this.length&&"__name__"===this.get(0)}static keyField(){return new xs(["__name__"])}static fromServerFormat(t){const e=[];let n="",r=0;const i=()=>{if(0===n.length)throw new hs(us.INVALID_ARGUMENT,`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`);e.push(n),n=""};let s=!1;for(;r<t.length;){const e=t[r];if("\\"===e){if(r+1===t.length)throw new hs(us.INVALID_ARGUMENT,"Path has trailing escape character: "+t);const e=t[r+1];if("\\"!==e&&"."!==e&&"`"!==e)throw new hs(us.INVALID_ARGUMENT,"Path has invalid escape sequence: "+t);n+=e,r+=2}else"`"===e?(s=!s,r++):"."!==e||s?(n+=e,r++):(i(),r++)}if(i(),s)throw new hs(us.INVALID_ARGUMENT,"Unterminated ` in path: "+t);return new xs(e)}static emptyPath(){return new xs([])}}class Ns{constructor(t){this.path=t}static fromPath(t){return new Ns(As.fromString(t))}static fromName(t){return new Ns(As.fromString(t).popFirst(5))}static empty(){return new Ns(As.emptyPath())}get collectionGroup(){return this.path.popLast().lastSegment()}hasCollectionId(t){return this.path.length>=2&&this.path.get(this.path.length-2)===t}getCollectionGroup(){return this.path.get(this.path.length-2)}getCollectionPath(){return this.path.popLast()}isEqual(t){return null!==t&&0===As.comparator(this.path,t.path)}toString(){return this.path.toString()}static comparator(t,e){return As.comparator(t.path,e.path)}static isDocumentKey(t){return t.length%2==0}static fromSegments(t){return new Ns(new As(t.slice()))}}function Ds(t){return new ks(t.readTime,t.key,-1)}class ks{constructor(t,e,n){this.readTime=t,this.documentKey=e,this.largestBatchId=n}static min(){return new ks(Ss.min(),Ns.empty(),-1)}static max(){return new ks(Ss.max(),Ns.empty(),-1)}}function Rs(t,e){let n=t.readTime.compareTo(e.readTime);return 0!==n?n:(n=Ns.comparator(t.documentKey,e.documentKey),0!==n?n:Es(t.largestBatchId,e.largestBatchId))}class Os{constructor(){this.onCommittedListeners=[]}addOnCommittedListener(t){this.onCommittedListeners.push(t)}raiseOnCommittedEvent(){this.onCommittedListeners.forEach((t=>t()))}}async function Ms(t){if(t.code!==us.FAILED_PRECONDITION||"The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab."!==t.message)throw t;ns("LocalStore","Unexpectedly lost primary lease")}class Ls{constructor(t){this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,t((t=>{this.isDone=!0,this.result=t,this.nextCallback&&this.nextCallback(t)}),(t=>{this.isDone=!0,this.error=t,this.catchCallback&&this.catchCallback(t)}))}catch(t){return this.next(void 0,t)}next(t,e){return this.callbackAttached&&os(),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(e,this.error):this.wrapSuccess(t,this.result):new Ls(((n,r)=>{this.nextCallback=e=>{this.wrapSuccess(t,e).next(n,r)},this.catchCallback=t=>{this.wrapFailure(e,t).next(n,r)}}))}toPromise(){return new Promise(((t,e)=>{this.next(t,e)}))}wrapUserFunction(t){try{const e=t();return e instanceof Ls?e:Ls.resolve(e)}catch(t){return Ls.reject(t)}}wrapSuccess(t,e){return t?this.wrapUserFunction((()=>t(e))):Ls.resolve(e)}wrapFailure(t,e){return t?this.wrapUserFunction((()=>t(e))):Ls.reject(e)}static resolve(t){return new Ls(((e,n)=>{e(t)}))}static reject(t){return new Ls(((e,n)=>{n(t)}))}static waitFor(t){return new Ls(((e,n)=>{let r=0,i=0,s=!1;t.forEach((t=>{++r,t.next((()=>{++i,s&&i===r&&e()}),(t=>n(t)))})),s=!0,i===r&&e()}))}static or(t){let e=Ls.resolve(!1);for(const n of t)e=e.next((t=>t?Ls.resolve(t):n()));return e}static forEach(t,e){const n=[];return t.forEach(((t,r)=>{n.push(e.call(this,t,r))})),this.waitFor(n)}static mapArray(t,e){return new Ls(((n,r)=>{const i=t.length,s=new Array(i);let o=0;for(let a=0;a<i;a++){const c=a;e(t[c]).next((t=>{s[c]=t,++o,o===i&&n(s)}),(t=>r(t)))}}))}static doWhile(t,e){return new Ls(((n,r)=>{const i=()=>{!0===t()?e().next((()=>{i()}),r):n()};i()}))}}function Ps(t){return"IndexedDbTransactionError"===t.name}class Fs{constructor(t,e){this.previousValue=t,e&&(e.sequenceNumberHandler=t=>this.ot(t),this.ut=t=>e.writeSequenceNumber(t))}ot(t){return this.previousValue=Math.max(t,this.previousValue),this.previousValue}next(){const t=++this.previousValue;return this.ut&&this.ut(t),t}}function Vs(t){return null==t}function Us(t){return 0===t&&1/t==-1/0}Fs.ct=-1;function Bs(t){let e=0;for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e++;return e}function js(t,e){for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(n,t[n])}function qs(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}class $s{constructor(t,e){this.comparator=t,this.root=e||Gs.EMPTY}insert(t,e){return new $s(this.comparator,this.root.insert(t,e,this.comparator).copy(null,null,Gs.BLACK,null,null))}remove(t){return new $s(this.comparator,this.root.remove(t,this.comparator).copy(null,null,Gs.BLACK,null,null))}get(t){let e=this.root;for(;!e.isEmpty();){const n=this.comparator(t,e.key);if(0===n)return e.value;n<0?e=e.left:n>0&&(e=e.right)}return null}indexOf(t){let e=0,n=this.root;for(;!n.isEmpty();){const r=this.comparator(t,n.key);if(0===r)return e+n.left.size;r<0?n=n.left:(e+=n.left.size+1,n=n.right)}return-1}isEmpty(){return this.root.isEmpty()}get size(){return this.root.size}minKey(){return this.root.minKey()}maxKey(){return this.root.maxKey()}inorderTraversal(t){return this.root.inorderTraversal(t)}forEach(t){this.inorderTraversal(((e,n)=>(t(e,n),!1)))}toString(){const t=[];return this.inorderTraversal(((e,n)=>(t.push(`${e}:${n}`),!1))),`{${t.join(", ")}}`}reverseTraversal(t){return this.root.reverseTraversal(t)}getIterator(){return new zs(this.root,null,this.comparator,!1)}getIteratorFrom(t){return new zs(this.root,t,this.comparator,!1)}getReverseIterator(){return new zs(this.root,null,this.comparator,!0)}getReverseIteratorFrom(t){return new zs(this.root,t,this.comparator,!0)}}class zs{constructor(t,e,n,r){this.isReverse=r,this.nodeStack=[];let i=1;for(;!t.isEmpty();)if(i=e?n(t.key,e):1,e&&r&&(i*=-1),i<0)t=this.isReverse?t.left:t.right;else{if(0===i){this.nodeStack.push(t);break}this.nodeStack.push(t),t=this.isReverse?t.right:t.left}}getNext(){let t=this.nodeStack.pop();const e={key:t.key,value:t.value};if(this.isReverse)for(t=t.left;!t.isEmpty();)this.nodeStack.push(t),t=t.right;else for(t=t.right;!t.isEmpty();)this.nodeStack.push(t),t=t.left;return e}hasNext(){return this.nodeStack.length>0}peek(){if(0===this.nodeStack.length)return null;const t=this.nodeStack[this.nodeStack.length-1];return{key:t.key,value:t.value}}}class Gs{constructor(t,e,n,r,i){this.key=t,this.value=e,this.color=null!=n?n:Gs.RED,this.left=null!=r?r:Gs.EMPTY,this.right=null!=i?i:Gs.EMPTY,this.size=this.left.size+1+this.right.size}copy(t,e,n,r,i){return new Gs(null!=t?t:this.key,null!=e?e:this.value,null!=n?n:this.color,null!=r?r:this.left,null!=i?i:this.right)}isEmpty(){return!1}inorderTraversal(t){return this.left.inorderTraversal(t)||t(this.key,this.value)||this.right.inorderTraversal(t)}reverseTraversal(t){return this.right.reverseTraversal(t)||t(this.key,this.value)||this.left.reverseTraversal(t)}min(){return this.left.isEmpty()?this:this.left.min()}minKey(){return this.min().key}maxKey(){return this.right.isEmpty()?this.key:this.right.maxKey()}insert(t,e,n){let r=this;const i=n(t,r.key);return r=i<0?r.copy(null,null,null,r.left.insert(t,e,n),null):0===i?r.copy(null,e,null,null,null):r.copy(null,null,null,null,r.right.insert(t,e,n)),r.fixUp()}removeMin(){if(this.left.isEmpty())return Gs.EMPTY;let t=this;return t.left.isRed()||t.left.left.isRed()||(t=t.moveRedLeft()),t=t.copy(null,null,null,t.left.removeMin(),null),t.fixUp()}remove(t,e){let n,r=this;if(e(t,r.key)<0)r.left.isEmpty()||r.left.isRed()||r.left.left.isRed()||(r=r.moveRedLeft()),r=r.copy(null,null,null,r.left.remove(t,e),null);else{if(r.left.isRed()&&(r=r.rotateRight()),r.right.isEmpty()||r.right.isRed()||r.right.left.isRed()||(r=r.moveRedRight()),0===e(t,r.key)){if(r.right.isEmpty())return Gs.EMPTY;n=r.right.min(),r=r.copy(n.key,n.value,null,null,r.right.removeMin())}r=r.copy(null,null,null,null,r.right.remove(t,e))}return r.fixUp()}isRed(){return this.color}fixUp(){let t=this;return t.right.isRed()&&!t.left.isRed()&&(t=t.rotateLeft()),t.left.isRed()&&t.left.left.isRed()&&(t=t.rotateRight()),t.left.isRed()&&t.right.isRed()&&(t=t.colorFlip()),t}moveRedLeft(){let t=this.colorFlip();return t.right.left.isRed()&&(t=t.copy(null,null,null,null,t.right.rotateRight()),t=t.rotateLeft(),t=t.colorFlip()),t}moveRedRight(){let t=this.colorFlip();return t.left.left.isRed()&&(t=t.rotateRight(),t=t.colorFlip()),t}rotateLeft(){const t=this.copy(null,null,Gs.RED,null,this.right.left);return this.right.copy(null,null,this.color,t,null)}rotateRight(){const t=this.copy(null,null,Gs.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,t)}colorFlip(){const t=this.left.copy(null,null,!this.left.color,null,null),e=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,t,e)}checkMaxDepth(){const t=this.check();return Math.pow(2,t)<=this.size+1}check(){if(this.isRed()&&this.left.isRed())throw os();if(this.right.isRed())throw os();const t=this.left.check();if(t!==this.right.check())throw os();return t+(this.isRed()?0:1)}}Gs.EMPTY=null,Gs.RED=!0,Gs.BLACK=!1,Gs.EMPTY=new class{constructor(){this.size=0}get key(){throw os()}get value(){throw os()}get color(){throw os()}get left(){throw os()}get right(){throw os()}copy(t,e,n,r,i){return this}insert(t,e,n){return new Gs(t,e)}remove(t,e){return this}isEmpty(){return!0}inorderTraversal(t){return!1}reverseTraversal(t){return!1}minKey(){return null}maxKey(){return null}isRed(){return!1}checkMaxDepth(){return!0}check(){return 0}};class Ks{constructor(t){this.comparator=t,this.data=new $s(this.comparator)}has(t){return null!==this.data.get(t)}first(){return this.data.minKey()}last(){return this.data.maxKey()}get size(){return this.data.size}indexOf(t){return this.data.indexOf(t)}forEach(t){this.data.inorderTraversal(((e,n)=>(t(e),!1)))}forEachInRange(t,e){const n=this.data.getIteratorFrom(t[0]);for(;n.hasNext();){const r=n.getNext();if(this.comparator(r.key,t[1])>=0)return;e(r.key)}}forEachWhile(t,e){let n;for(n=void 0!==e?this.data.getIteratorFrom(e):this.data.getIterator();n.hasNext();)if(!t(n.getNext().key))return}firstAfterOrEqual(t){const e=this.data.getIteratorFrom(t);return e.hasNext()?e.getNext().key:null}getIterator(){return new Hs(this.data.getIterator())}getIteratorFrom(t){return new Hs(this.data.getIteratorFrom(t))}add(t){return this.copy(this.data.remove(t).insert(t,!0))}delete(t){return this.has(t)?this.copy(this.data.remove(t)):this}isEmpty(){return this.data.isEmpty()}unionWith(t){let e=this;return e.size<t.size&&(e=t,t=this),t.forEach((t=>{e=e.add(t)})),e}isEqual(t){if(!(t instanceof Ks))return!1;if(this.size!==t.size)return!1;const e=this.data.getIterator(),n=t.data.getIterator();for(;e.hasNext();){const t=e.getNext().key,r=n.getNext().key;if(0!==this.comparator(t,r))return!1}return!0}toArray(){const t=[];return this.forEach((e=>{t.push(e)})),t}toString(){const t=[];return this.forEach((e=>t.push(e))),"SortedSet("+t.toString()+")"}copy(t){const e=new Ks(this.comparator);return e.data=t,e}}class Hs{constructor(t){this.iter=t}getNext(){return this.iter.getNext().key}hasNext(){return this.iter.hasNext()}}class Qs{constructor(t){this.fields=t,t.sort(xs.comparator)}static empty(){return new Qs([])}unionWith(t){let e=new Ks(xs.comparator);for(const t of this.fields)e=e.add(t);for(const n of t)e=e.add(n);return new Qs(e.toArray())}covers(t){for(const e of this.fields)if(e.isPrefixOf(t))return!0;return!1}isEqual(t){return Ts(this.fields,t.fields,((t,e)=>t.isEqual(e)))}}class Ws extends Error{constructor(){super(...arguments),this.name="Base64DecodeError"}}class Ys{constructor(t){this.binaryString=t}static fromBase64String(t){const e=function(t){try{return atob(t)}catch(t){throw"undefined"!=typeof DOMException&&t instanceof DOMException?new Ws("Invalid base64 string: "+t):t}}(t);return new Ys(e)}static fromUint8Array(t){const e=function(t){let e="";for(let n=0;n<t.length;++n)e+=String.fromCharCode(t[n]);return e}(t);return new Ys(e)}[Symbol.iterator](){let t=0;return{next:()=>t<this.binaryString.length?{value:this.binaryString.charCodeAt(t++),done:!1}:{value:void 0,done:!0}}}toBase64(){return t=this.binaryString,btoa(t);var t}toUint8Array(){return function(t){const e=new Uint8Array(t.length);for(let n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}(this.binaryString)}approximateByteSize(){return 2*this.binaryString.length}compareTo(t){return Es(this.binaryString,t.binaryString)}isEqual(t){return this.binaryString===t.binaryString}}Ys.EMPTY_BYTE_STRING=new Ys("");const Xs=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function Js(t){if(as(!!t),"string"==typeof t){let e=0;const n=Xs.exec(t);if(as(!!n),n[1]){let t=n[1];t=(t+"000000000").substr(0,9),e=Number(t)}const r=new Date(t);return{seconds:Math.floor(r.getTime()/1e3),nanos:e}}return{seconds:Zs(t.seconds),nanos:Zs(t.nanos)}}function Zs(t){return"number"==typeof t?t:"string"==typeof t?Number(t):0}function to(t){return"string"==typeof t?Ys.fromBase64String(t):Ys.fromUint8Array(t)}function eo(t){var e,n;return"server_timestamp"===(null===(n=((null===(e=null==t?void 0:t.mapValue)||void 0===e?void 0:e.fields)||{}).__type__)||void 0===n?void 0:n.stringValue)}function no(t){const e=t.mapValue.fields.__previous_value__;return eo(e)?no(e):e}function ro(t){const e=Js(t.mapValue.fields.__local_write_time__.timestampValue);return new Is(e.seconds,e.nanos)}class io{constructor(t,e,n,r,i,s,o,a,c){this.databaseId=t,this.appId=e,this.persistenceKey=n,this.host=r,this.ssl=i,this.forceLongPolling=s,this.autoDetectLongPolling=o,this.longPollingOptions=a,this.useFetchStreams=c}}class so{constructor(t,e){this.projectId=t,this.database=e||"(default)"}static empty(){return new so("","")}get isDefaultDatabase(){return"(default)"===this.database}isEqual(t){return t instanceof so&&t.projectId===this.projectId&&t.database===this.database}}const oo={fields:{__type__:{stringValue:"__max__"}}};function ao(t){return"nullValue"in t?0:"booleanValue"in t?1:"integerValue"in t||"doubleValue"in t?2:"timestampValue"in t?3:"stringValue"in t?5:"bytesValue"in t?6:"referenceValue"in t?7:"geoPointValue"in t?8:"arrayValue"in t?9:"mapValue"in t?eo(t)?4:To(t)?9007199254740991:10:os()}function co(t,e){if(t===e)return!0;const n=ao(t);if(n!==ao(e))return!1;switch(n){case 0:case 9007199254740991:return!0;case 1:return t.booleanValue===e.booleanValue;case 4:return ro(t).isEqual(ro(e));case 3:return function(t,e){if("string"==typeof t.timestampValue&&"string"==typeof e.timestampValue&&t.timestampValue.length===e.timestampValue.length)return t.timestampValue===e.timestampValue;const n=Js(t.timestampValue),r=Js(e.timestampValue);return n.seconds===r.seconds&&n.nanos===r.nanos}(t,e);case 5:return t.stringValue===e.stringValue;case 6:return function(t,e){return to(t.bytesValue).isEqual(to(e.bytesValue))}(t,e);case 7:return t.referenceValue===e.referenceValue;case 8:return function(t,e){return Zs(t.geoPointValue.latitude)===Zs(e.geoPointValue.latitude)&&Zs(t.geoPointValue.longitude)===Zs(e.geoPointValue.longitude)}(t,e);case 2:return function(t,e){if("integerValue"in t&&"integerValue"in e)return Zs(t.integerValue)===Zs(e.integerValue);if("doubleValue"in t&&"doubleValue"in e){const n=Zs(t.doubleValue),r=Zs(e.doubleValue);return n===r?Us(n)===Us(r):isNaN(n)&&isNaN(r)}return!1}(t,e);case 9:return Ts(t.arrayValue.values||[],e.arrayValue.values||[],co);case 10:return function(t,e){const n=t.mapValue.fields||{},r=e.mapValue.fields||{};if(Bs(n)!==Bs(r))return!1;for(const t in n)if(n.hasOwnProperty(t)&&(void 0===r[t]||!co(n[t],r[t])))return!1;return!0}(t,e);default:return os()}}function uo(t,e){return void 0!==(t.values||[]).find((t=>co(t,e)))}function ho(t,e){if(t===e)return 0;const n=ao(t),r=ao(e);if(n!==r)return Es(n,r);switch(n){case 0:case 9007199254740991:return 0;case 1:return Es(t.booleanValue,e.booleanValue);case 2:return function(t,e){const n=Zs(t.integerValue||t.doubleValue),r=Zs(e.integerValue||e.doubleValue);return n<r?-1:n>r?1:n===r?0:isNaN(n)?isNaN(r)?0:-1:1}(t,e);case 3:return lo(t.timestampValue,e.timestampValue);case 4:return lo(ro(t),ro(e));case 5:return Es(t.stringValue,e.stringValue);case 6:return function(t,e){const n=to(t),r=to(e);return n.compareTo(r)}(t.bytesValue,e.bytesValue);case 7:return function(t,e){const n=t.split("/"),r=e.split("/");for(let t=0;t<n.length&&t<r.length;t++){const e=Es(n[t],r[t]);if(0!==e)return e}return Es(n.length,r.length)}(t.referenceValue,e.referenceValue);case 8:return function(t,e){const n=Es(Zs(t.latitude),Zs(e.latitude));return 0!==n?n:Es(Zs(t.longitude),Zs(e.longitude))}(t.geoPointValue,e.geoPointValue);case 9:return function(t,e){const n=t.values||[],r=e.values||[];for(let t=0;t<n.length&&t<r.length;++t){const e=ho(n[t],r[t]);if(e)return e}return Es(n.length,r.length)}(t.arrayValue,e.arrayValue);case 10:return function(t,e){if(t===oo&&e===oo)return 0;if(t===oo)return 1;if(e===oo)return-1;const n=t.fields||{},r=Object.keys(n),i=e.fields||{},s=Object.keys(i);r.sort(),s.sort();for(let t=0;t<r.length&&t<s.length;++t){const e=Es(r[t],s[t]);if(0!==e)return e;const o=ho(n[r[t]],i[s[t]]);if(0!==o)return o}return Es(r.length,s.length)}(t.mapValue,e.mapValue);default:throw os()}}function lo(t,e){if("string"==typeof t&&"string"==typeof e&&t.length===e.length)return Es(t,e);const n=Js(t),r=Js(e),i=Es(n.seconds,r.seconds);return 0!==i?i:Es(n.nanos,r.nanos)}function fo(t){return po(t)}function po(t){return"nullValue"in t?"null":"booleanValue"in t?""+t.booleanValue:"integerValue"in t?""+t.integerValue:"doubleValue"in t?""+t.doubleValue:"timestampValue"in t?function(t){const e=Js(t);return`time(${e.seconds},${e.nanos})`}(t.timestampValue):"stringValue"in t?t.stringValue:"bytesValue"in t?to(t.bytesValue).toBase64():"referenceValue"in t?(n=t.referenceValue,Ns.fromName(n).toString()):"geoPointValue"in t?`geo(${(e=t.geoPointValue).latitude},${e.longitude})`:"arrayValue"in t?function(t){let e="[",n=!0;for(const r of t.values||[])n?n=!1:e+=",",e+=po(r);return e+"]"}(t.arrayValue):"mapValue"in t?function(t){const e=Object.keys(t.fields||{}).sort();let n="{",r=!0;for(const i of e)r?r=!1:n+=",",n+=`${i}:${po(t.fields[i])}`;return n+"}"}(t.mapValue):os();var e,n}function go(t,e){return{referenceValue:`projects/${t.projectId}/databases/${t.database}/documents/${e.path.canonicalString()}`}}function mo(t){return!!t&&"integerValue"in t}function yo(t){return!!t&&"arrayValue"in t}function vo(t){return!!t&&"nullValue"in t}function wo(t){return!!t&&"doubleValue"in t&&isNaN(Number(t.doubleValue))}function bo(t){return!!t&&"mapValue"in t}function Eo(t){if(t.geoPointValue)return{geoPointValue:Object.assign({},t.geoPointValue)};if(t.timestampValue&&"object"==typeof t.timestampValue)return{timestampValue:Object.assign({},t.timestampValue)};if(t.mapValue){const e={mapValue:{fields:{}}};return js(t.mapValue.fields,((t,n)=>e.mapValue.fields[t]=Eo(n))),e}if(t.arrayValue){const e={arrayValue:{values:[]}};for(let n=0;n<(t.arrayValue.values||[]).length;++n)e.arrayValue.values[n]=Eo(t.arrayValue.values[n]);return e}return Object.assign({},t)}function To(t){return"__max__"===(((t.mapValue||{}).fields||{}).__type__||{}).stringValue}class Io{constructor(t){this.value=t}static empty(){return new Io({mapValue:{}})}field(t){if(t.isEmpty())return this.value;{let e=this.value;for(let n=0;n<t.length-1;++n)if(e=(e.mapValue.fields||{})[t.get(n)],!bo(e))return null;return e=(e.mapValue.fields||{})[t.lastSegment()],e||null}}set(t,e){this.getFieldsMap(t.popLast())[t.lastSegment()]=Eo(e)}setAll(t){let e=xs.emptyPath(),n={},r=[];t.forEach(((t,i)=>{if(!e.isImmediateParentOf(i)){const t=this.getFieldsMap(e);this.applyChanges(t,n,r),n={},r=[],e=i.popLast()}t?n[i.lastSegment()]=Eo(t):r.push(i.lastSegment())}));const i=this.getFieldsMap(e);this.applyChanges(i,n,r)}delete(t){const e=this.field(t.popLast());bo(e)&&e.mapValue.fields&&delete e.mapValue.fields[t.lastSegment()]}isEqual(t){return co(this.value,t.value)}getFieldsMap(t){let e=this.value;e.mapValue.fields||(e.mapValue={fields:{}});for(let n=0;n<t.length;++n){let r=e.mapValue.fields[t.get(n)];bo(r)&&r.mapValue.fields||(r={mapValue:{fields:{}}},e.mapValue.fields[t.get(n)]=r),e=r}return e.mapValue.fields}applyChanges(t,e,n){js(e,((e,n)=>t[e]=n));for(const e of n)delete t[e]}clone(){return new Io(Eo(this.value))}}function So(t){const e=[];return js(t.fields,((t,n)=>{const r=new xs([t]);if(bo(n)){const t=So(n.mapValue).fields;if(0===t.length)e.push(r);else for(const n of t)e.push(r.child(n))}else e.push(r)})),new Qs(e)}class Co{constructor(t,e,n,r,i,s,o){this.key=t,this.documentType=e,this.version=n,this.readTime=r,this.createTime=i,this.data=s,this.documentState=o}static newInvalidDocument(t){return new Co(t,0,Ss.min(),Ss.min(),Ss.min(),Io.empty(),0)}static newFoundDocument(t,e,n,r){return new Co(t,1,e,Ss.min(),n,r,0)}static newNoDocument(t,e){return new Co(t,2,e,Ss.min(),Ss.min(),Io.empty(),0)}static newUnknownDocument(t,e){return new Co(t,3,e,Ss.min(),Ss.min(),Io.empty(),2)}convertToFoundDocument(t,e){return!this.createTime.isEqual(Ss.min())||2!==this.documentType&&0!==this.documentType||(this.createTime=t),this.version=t,this.documentType=1,this.data=e,this.documentState=0,this}convertToNoDocument(t){return this.version=t,this.documentType=2,this.data=Io.empty(),this.documentState=0,this}convertToUnknownDocument(t){return this.version=t,this.documentType=3,this.data=Io.empty(),this.documentState=2,this}setHasCommittedMutations(){return this.documentState=2,this}setHasLocalMutations(){return this.documentState=1,this.version=Ss.min(),this}setReadTime(t){return this.readTime=t,this}get hasLocalMutations(){return 1===this.documentState}get hasCommittedMutations(){return 2===this.documentState}get hasPendingWrites(){return this.hasLocalMutations||this.hasCommittedMutations}isValidDocument(){return 0!==this.documentType}isFoundDocument(){return 1===this.documentType}isNoDocument(){return 2===this.documentType}isUnknownDocument(){return 3===this.documentType}isEqual(t){return t instanceof Co&&this.key.isEqual(t.key)&&this.version.isEqual(t.version)&&this.documentType===t.documentType&&this.documentState===t.documentState&&this.data.isEqual(t.data)}mutableCopy(){return new Co(this.key,this.documentType,this.version,this.readTime,this.createTime,this.data.clone(),this.documentState)}toString(){return`Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`}}class Ao{constructor(t,e){this.position=t,this.inclusive=e}}function _o(t,e,n){let r=0;for(let i=0;i<t.position.length;i++){const s=e[i],o=t.position[i];if(r=s.field.isKeyField()?Ns.comparator(Ns.fromName(o.referenceValue),n.key):ho(o,n.data.field(s.field)),"desc"===s.dir&&(r*=-1),0!==r)break}return r}function xo(t,e){if(null===t)return null===e;if(null===e)return!1;if(t.inclusive!==e.inclusive||t.position.length!==e.position.length)return!1;for(let n=0;n<t.position.length;n++)if(!co(t.position[n],e.position[n]))return!1;return!0}class No{constructor(t,e="asc"){this.field=t,this.dir=e}}function Do(t,e){return t.dir===e.dir&&t.field.isEqual(e.field)}class ko{}class Ro extends ko{constructor(t,e,n){super(),this.field=t,this.op=e,this.value=n}static create(t,e,n){return t.isKeyField()?"in"===e||"not-in"===e?this.createKeyFieldInFilter(t,e,n):new Uo(t,e,n):"array-contains"===e?new $o(t,n):"in"===e?new zo(t,n):"not-in"===e?new Go(t,n):"array-contains-any"===e?new Ko(t,n):new Ro(t,e,n)}static createKeyFieldInFilter(t,e,n){return"in"===e?new Bo(t,n):new jo(t,n)}matches(t){const e=t.data.field(this.field);return"!="===this.op?null!==e&&this.matchesComparison(ho(e,this.value)):null!==e&&ao(this.value)===ao(e)&&this.matchesComparison(ho(e,this.value))}matchesComparison(t){switch(this.op){case"<":return t<0;case"<=":return t<=0;case"==":return 0===t;case"!=":return 0!==t;case">":return t>0;case">=":return t>=0;default:return os()}}isInequality(){return["<","<=",">",">=","!=","not-in"].indexOf(this.op)>=0}getFlattenedFilters(){return[this]}getFilters(){return[this]}getFirstInequalityField(){return this.isInequality()?this.field:null}}class Oo extends ko{constructor(t,e){super(),this.filters=t,this.op=e,this.lt=null}static create(t,e){return new Oo(t,e)}matches(t){return Mo(this)?void 0===this.filters.find((e=>!e.matches(t))):void 0!==this.filters.find((e=>e.matches(t)))}getFlattenedFilters(){return null!==this.lt||(this.lt=this.filters.reduce(((t,e)=>t.concat(e.getFlattenedFilters())),[])),this.lt}getFilters(){return Object.assign([],this.filters)}getFirstInequalityField(){const t=this.ft((t=>t.isInequality()));return null!==t?t.field:null}ft(t){for(const e of this.getFlattenedFilters())if(t(e))return e;return null}}function Mo(t){return"and"===t.op}function Lo(t){return function(t){for(const e of t.filters)if(e instanceof Oo)return!1;return!0}(t)&&Mo(t)}function Po(t){if(t instanceof Ro)return t.field.canonicalString()+t.op.toString()+fo(t.value);if(Lo(t))return t.filters.map((t=>Po(t))).join(",");{const e=t.filters.map((t=>Po(t))).join(",");return`${t.op}(${e})`}}function Fo(t,e){return t instanceof Ro?function(t,e){return e instanceof Ro&&t.op===e.op&&t.field.isEqual(e.field)&&co(t.value,e.value)}(t,e):t instanceof Oo?function(t,e){return e instanceof Oo&&t.op===e.op&&t.filters.length===e.filters.length&&t.filters.reduce(((t,n,r)=>t&&Fo(n,e.filters[r])),!0)}(t,e):void os()}function Vo(t){return t instanceof Ro?function(t){return`${t.field.canonicalString()} ${t.op} ${fo(t.value)}`}(t):t instanceof Oo?function(t){return t.op.toString()+" {"+t.getFilters().map(Vo).join(" ,")+"}"}(t):"Filter"}class Uo extends Ro{constructor(t,e,n){super(t,e,n),this.key=Ns.fromName(n.referenceValue)}matches(t){const e=Ns.comparator(t.key,this.key);return this.matchesComparison(e)}}class Bo extends Ro{constructor(t,e){super(t,"in",e),this.keys=qo(0,e)}matches(t){return this.keys.some((e=>e.isEqual(t.key)))}}class jo extends Ro{constructor(t,e){super(t,"not-in",e),this.keys=qo(0,e)}matches(t){return!this.keys.some((e=>e.isEqual(t.key)))}}function qo(t,e){var n;return((null===(n=e.arrayValue)||void 0===n?void 0:n.values)||[]).map((t=>Ns.fromName(t.referenceValue)))}class $o extends Ro{constructor(t,e){super(t,"array-contains",e)}matches(t){const e=t.data.field(this.field);return yo(e)&&uo(e.arrayValue,this.value)}}class zo extends Ro{constructor(t,e){super(t,"in",e)}matches(t){const e=t.data.field(this.field);return null!==e&&uo(this.value.arrayValue,e)}}class Go extends Ro{constructor(t,e){super(t,"not-in",e)}matches(t){if(uo(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;const e=t.data.field(this.field);return null!==e&&!uo(this.value.arrayValue,e)}}class Ko extends Ro{constructor(t,e){super(t,"array-contains-any",e)}matches(t){const e=t.data.field(this.field);return!(!yo(e)||!e.arrayValue.values)&&e.arrayValue.values.some((t=>uo(this.value.arrayValue,t)))}}class Ho{constructor(t,e=null,n=[],r=[],i=null,s=null,o=null){this.path=t,this.collectionGroup=e,this.orderBy=n,this.filters=r,this.limit=i,this.startAt=s,this.endAt=o,this.dt=null}}function Qo(t,e=null,n=[],r=[],i=null,s=null,o=null){return new Ho(t,e,n,r,i,s,o)}function Wo(t){const e=cs(t);if(null===e.dt){let t=e.path.canonicalString();null!==e.collectionGroup&&(t+="|cg:"+e.collectionGroup),t+="|f:",t+=e.filters.map((t=>Po(t))).join(","),t+="|ob:",t+=e.orderBy.map((t=>function(t){return t.field.canonicalString()+t.dir}(t))).join(","),Vs(e.limit)||(t+="|l:",t+=e.limit),e.startAt&&(t+="|lb:",t+=e.startAt.inclusive?"b:":"a:",t+=e.startAt.position.map((t=>fo(t))).join(",")),e.endAt&&(t+="|ub:",t+=e.endAt.inclusive?"a:":"b:",t+=e.endAt.position.map((t=>fo(t))).join(",")),e.dt=t}return e.dt}function Yo(t,e){if(t.limit!==e.limit)return!1;if(t.orderBy.length!==e.orderBy.length)return!1;for(let n=0;n<t.orderBy.length;n++)if(!Do(t.orderBy[n],e.orderBy[n]))return!1;if(t.filters.length!==e.filters.length)return!1;for(let n=0;n<t.filters.length;n++)if(!Fo(t.filters[n],e.filters[n]))return!1;return t.collectionGroup===e.collectionGroup&&!!t.path.isEqual(e.path)&&!!xo(t.startAt,e.startAt)&&xo(t.endAt,e.endAt)}function Xo(t){return Ns.isDocumentKey(t.path)&&null===t.collectionGroup&&0===t.filters.length}class Jo{constructor(t,e=null,n=[],r=[],i=null,s="F",o=null,a=null){this.path=t,this.collectionGroup=e,this.explicitOrderBy=n,this.filters=r,this.limit=i,this.limitType=s,this.startAt=o,this.endAt=a,this.wt=null,this._t=null,this.startAt,this.endAt}}function Zo(t){return new Jo(t)}function ta(t){return 0===t.filters.length&&null===t.limit&&null==t.startAt&&null==t.endAt&&(0===t.explicitOrderBy.length||1===t.explicitOrderBy.length&&t.explicitOrderBy[0].field.isKeyField())}function ea(t){return t.explicitOrderBy.length>0?t.explicitOrderBy[0].field:null}function na(t){for(const e of t.filters){const t=e.getFirstInequalityField();if(null!==t)return t}return null}function ra(t){return null!==t.collectionGroup}function ia(t){const e=cs(t);if(null===e.wt){e.wt=[];const t=na(e),n=ea(e);if(null!==t&&null===n)t.isKeyField()||e.wt.push(new No(t)),e.wt.push(new No(xs.keyField(),"asc"));else{let t=!1;for(const n of e.explicitOrderBy)e.wt.push(n),n.field.isKeyField()&&(t=!0);if(!t){const t=e.explicitOrderBy.length>0?e.explicitOrderBy[e.explicitOrderBy.length-1].dir:"asc";e.wt.push(new No(xs.keyField(),t))}}}return e.wt}function sa(t){const e=cs(t);if(!e._t)if("F"===e.limitType)e._t=Qo(e.path,e.collectionGroup,ia(e),e.filters,e.limit,e.startAt,e.endAt);else{const t=[];for(const n of ia(e)){const e="desc"===n.dir?"asc":"desc";t.push(new No(n.field,e))}const n=e.endAt?new Ao(e.endAt.position,e.endAt.inclusive):null,r=e.startAt?new Ao(e.startAt.position,e.startAt.inclusive):null;e._t=Qo(e.path,e.collectionGroup,t,e.filters,e.limit,n,r)}return e._t}function oa(t,e){e.getFirstInequalityField(),na(t);const n=t.filters.concat([e]);return new Jo(t.path,t.collectionGroup,t.explicitOrderBy.slice(),n,t.limit,t.limitType,t.startAt,t.endAt)}function aa(t,e,n){return new Jo(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),e,n,t.startAt,t.endAt)}function ca(t,e){return Yo(sa(t),sa(e))&&t.limitType===e.limitType}function ua(t){return`${Wo(sa(t))}|lt:${t.limitType}`}function ha(t){return`Query(target=${function(t){let e=t.path.canonicalString();return null!==t.collectionGroup&&(e+=" collectionGroup="+t.collectionGroup),t.filters.length>0&&(e+=`, filters: [${t.filters.map((t=>Vo(t))).join(", ")}]`),Vs(t.limit)||(e+=", limit: "+t.limit),t.orderBy.length>0&&(e+=`, orderBy: [${t.orderBy.map((t=>function(t){return`${t.field.canonicalString()} (${t.dir})`}(t))).join(", ")}]`),t.startAt&&(e+=", startAt: ",e+=t.startAt.inclusive?"b:":"a:",e+=t.startAt.position.map((t=>fo(t))).join(",")),t.endAt&&(e+=", endAt: ",e+=t.endAt.inclusive?"a:":"b:",e+=t.endAt.position.map((t=>fo(t))).join(",")),`Target(${e})`}(sa(t))}; limitType=${t.limitType})`}function la(t,e){return e.isFoundDocument()&&function(t,e){const n=e.key.path;return null!==t.collectionGroup?e.key.hasCollectionId(t.collectionGroup)&&t.path.isPrefixOf(n):Ns.isDocumentKey(t.path)?t.path.isEqual(n):t.path.isImmediateParentOf(n)}(t,e)&&function(t,e){for(const n of ia(t))if(!n.field.isKeyField()&&null===e.data.field(n.field))return!1;return!0}(t,e)&&function(t,e){for(const n of t.filters)if(!n.matches(e))return!1;return!0}(t,e)&&function(t,e){return!(t.startAt&&!function(t,e,n){const r=_o(t,e,n);return t.inclusive?r<=0:r<0}(t.startAt,ia(t),e)||t.endAt&&!function(t,e,n){const r=_o(t,e,n);return t.inclusive?r>=0:r>0}(t.endAt,ia(t),e))}(t,e)}function da(t){return(e,n)=>{let r=!1;for(const i of ia(t)){const t=fa(i,e,n);if(0!==t)return t;r=r||i.field.isKeyField()}return 0}}function fa(t,e,n){const r=t.field.isKeyField()?Ns.comparator(e.key,n.key):function(t,e,n){const r=e.data.field(t),i=n.data.field(t);return null!==r&&null!==i?ho(r,i):os()}(t.field,e,n);switch(t.dir){case"asc":return r;case"desc":return-1*r;default:return os()}}class pa{constructor(t,e){this.mapKeyFn=t,this.equalsFn=e,this.inner={},this.innerSize=0}get(t){const e=this.mapKeyFn(t),n=this.inner[e];if(void 0!==n)for(const[e,r]of n)if(this.equalsFn(e,t))return r}has(t){return void 0!==this.get(t)}set(t,e){const n=this.mapKeyFn(t),r=this.inner[n];if(void 0===r)return this.inner[n]=[[t,e]],void this.innerSize++;for(let n=0;n<r.length;n++)if(this.equalsFn(r[n][0],t))return void(r[n]=[t,e]);r.push([t,e]),this.innerSize++}delete(t){const e=this.mapKeyFn(t),n=this.inner[e];if(void 0===n)return!1;for(let r=0;r<n.length;r++)if(this.equalsFn(n[r][0],t))return 1===n.length?delete this.inner[e]:n.splice(r,1),this.innerSize--,!0;return!1}forEach(t){js(this.inner,((e,n)=>{for(const[e,r]of n)t(e,r)}))}isEmpty(){return qs(this.inner)}size(){return this.innerSize}}const ga=new $s(Ns.comparator);function ma(){return ga}const ya=new $s(Ns.comparator);function va(...t){let e=ya;for(const n of t)e=e.insert(n.key,n);return e}function wa(t){let e=ya;return t.forEach(((t,n)=>e=e.insert(t,n.overlayedDocument))),e}function ba(){return Ta()}function Ea(){return Ta()}function Ta(){return new pa((t=>t.toString()),((t,e)=>t.isEqual(e)))}const Ia=new $s(Ns.comparator),Sa=new Ks(Ns.comparator);function Ca(...t){let e=Sa;for(const n of t)e=e.add(n);return e}const Aa=new Ks(Es);function _a(t,e){if(t.useProto3Json){if(isNaN(e))return{doubleValue:"NaN"};if(e===1/0)return{doubleValue:"Infinity"};if(e===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:Us(e)?"-0":e}}function xa(t){return{integerValue:""+t}}function Na(t,e){return function(t){return"number"==typeof t&&Number.isInteger(t)&&!Us(t)&&t<=Number.MAX_SAFE_INTEGER&&t>=Number.MIN_SAFE_INTEGER}(e)?xa(e):_a(t,e)}class Da{constructor(){this._=void 0}}function ka(t,e,n){return t instanceof Ma?function(t,e){const n={fields:{__type__:{stringValue:"server_timestamp"},__local_write_time__:{timestampValue:{seconds:t.seconds,nanos:t.nanoseconds}}}};return e&&eo(e)&&(e=no(e)),e&&(n.fields.__previous_value__=e),{mapValue:n}}(n,e):t instanceof La?Pa(t,e):t instanceof Fa?Va(t,e):function(t,e){const n=Oa(t,e),r=Ba(n)+Ba(t.gt);return mo(n)&&mo(t.gt)?xa(r):_a(t.serializer,r)}(t,e)}function Ra(t,e,n){return t instanceof La?Pa(t,e):t instanceof Fa?Va(t,e):n}function Oa(t,e){return t instanceof Ua?mo(n=e)||function(t){return!!t&&"doubleValue"in t}(n)?e:{integerValue:0}:null;var n}class Ma extends Da{}class La extends Da{constructor(t){super(),this.elements=t}}function Pa(t,e){const n=ja(e);for(const e of t.elements)n.some((t=>co(t,e)))||n.push(e);return{arrayValue:{values:n}}}class Fa extends Da{constructor(t){super(),this.elements=t}}function Va(t,e){let n=ja(e);for(const e of t.elements)n=n.filter((t=>!co(t,e)));return{arrayValue:{values:n}}}class Ua extends Da{constructor(t,e){super(),this.serializer=t,this.gt=e}}function Ba(t){return Zs(t.integerValue||t.doubleValue)}function ja(t){return yo(t)&&t.arrayValue.values?t.arrayValue.values.slice():[]}class qa{constructor(t,e){this.version=t,this.transformResults=e}}class $a{constructor(t,e){this.updateTime=t,this.exists=e}static none(){return new $a}static exists(t){return new $a(void 0,t)}static updateTime(t){return new $a(t)}get isNone(){return void 0===this.updateTime&&void 0===this.exists}isEqual(t){return this.exists===t.exists&&(this.updateTime?!!t.updateTime&&this.updateTime.isEqual(t.updateTime):!t.updateTime)}}function za(t,e){return void 0!==t.updateTime?e.isFoundDocument()&&e.version.isEqual(t.updateTime):void 0===t.exists||t.exists===e.isFoundDocument()}class Ga{}function Ka(t,e){if(!t.hasLocalMutations||e&&0===e.fields.length)return null;if(null===e)return t.isNoDocument()?new nc(t.key,$a.none()):new Xa(t.key,t.data,$a.none());{const n=t.data,r=Io.empty();let i=new Ks(xs.comparator);for(let t of e.fields)if(!i.has(t)){let e=n.field(t);null===e&&t.length>1&&(t=t.popLast(),e=n.field(t)),null===e?r.delete(t):r.set(t,e),i=i.add(t)}return new Ja(t.key,r,new Qs(i.toArray()),$a.none())}}function Ha(t,e,n){t instanceof Xa?function(t,e,n){const r=t.value.clone(),i=tc(t.fieldTransforms,e,n.transformResults);r.setAll(i),e.convertToFoundDocument(n.version,r).setHasCommittedMutations()}(t,e,n):t instanceof Ja?function(t,e,n){if(!za(t.precondition,e))return void e.convertToUnknownDocument(n.version);const r=tc(t.fieldTransforms,e,n.transformResults),i=e.data;i.setAll(Za(t)),i.setAll(r),e.convertToFoundDocument(n.version,i).setHasCommittedMutations()}(t,e,n):function(t,e,n){e.convertToNoDocument(n.version).setHasCommittedMutations()}(0,e,n)}function Qa(t,e,n,r){return t instanceof Xa?function(t,e,n,r){if(!za(t.precondition,e))return n;const i=t.value.clone(),s=ec(t.fieldTransforms,r,e);return i.setAll(s),e.convertToFoundDocument(e.version,i).setHasLocalMutations(),null}(t,e,n,r):t instanceof Ja?function(t,e,n,r){if(!za(t.precondition,e))return n;const i=ec(t.fieldTransforms,r,e),s=e.data;return s.setAll(Za(t)),s.setAll(i),e.convertToFoundDocument(e.version,s).setHasLocalMutations(),null===n?null:n.unionWith(t.fieldMask.fields).unionWith(t.fieldTransforms.map((t=>t.field)))}(t,e,n,r):function(t,e,n){return za(t.precondition,e)?(e.convertToNoDocument(e.version).setHasLocalMutations(),null):n}(t,e,n)}function Wa(t,e){let n=null;for(const r of t.fieldTransforms){const t=e.data.field(r.field),i=Oa(r.transform,t||null);null!=i&&(null===n&&(n=Io.empty()),n.set(r.field,i))}return n||null}function Ya(t,e){return t.type===e.type&&!!t.key.isEqual(e.key)&&!!t.precondition.isEqual(e.precondition)&&!!function(t,e){return void 0===t&&void 0===e||!(!t||!e)&&Ts(t,e,((t,e)=>function(t,e){return t.field.isEqual(e.field)&&function(t,e){return t instanceof La&&e instanceof La||t instanceof Fa&&e instanceof Fa?Ts(t.elements,e.elements,co):t instanceof Ua&&e instanceof Ua?co(t.gt,e.gt):t instanceof Ma&&e instanceof Ma}(t.transform,e.transform)}(t,e)))}(t.fieldTransforms,e.fieldTransforms)&&(0===t.type?t.value.isEqual(e.value):1!==t.type||t.data.isEqual(e.data)&&t.fieldMask.isEqual(e.fieldMask))}class Xa extends Ga{constructor(t,e,n,r=[]){super(),this.key=t,this.value=e,this.precondition=n,this.fieldTransforms=r,this.type=0}getFieldMask(){return null}}class Ja extends Ga{constructor(t,e,n,r,i=[]){super(),this.key=t,this.data=e,this.fieldMask=n,this.precondition=r,this.fieldTransforms=i,this.type=1}getFieldMask(){return this.fieldMask}}function Za(t){const e=new Map;return t.fieldMask.fields.forEach((n=>{if(!n.isEmpty()){const r=t.data.field(n);e.set(n,r)}})),e}function tc(t,e,n){const r=new Map;as(t.length===n.length);for(let i=0;i<n.length;i++){const s=t[i],o=s.transform,a=e.data.field(s.field);r.set(s.field,Ra(o,a,n[i]))}return r}function ec(t,e,n){const r=new Map;for(const i of t){const t=i.transform,s=n.data.field(i.field);r.set(i.field,ka(t,s,e))}return r}class nc extends Ga{constructor(t,e){super(),this.key=t,this.precondition=e,this.type=2,this.fieldTransforms=[]}getFieldMask(){return null}}class rc extends Ga{constructor(t,e){super(),this.key=t,this.precondition=e,this.type=3,this.fieldTransforms=[]}getFieldMask(){return null}}class ic{constructor(t,e,n,r){this.batchId=t,this.localWriteTime=e,this.baseMutations=n,this.mutations=r}applyToRemoteDocument(t,e){const n=e.mutationResults;for(let e=0;e<this.mutations.length;e++){const r=this.mutations[e];r.key.isEqual(t.key)&&Ha(r,t,n[e])}}applyToLocalView(t,e){for(const n of this.baseMutations)n.key.isEqual(t.key)&&(e=Qa(n,t,e,this.localWriteTime));for(const n of this.mutations)n.key.isEqual(t.key)&&(e=Qa(n,t,e,this.localWriteTime));return e}applyToLocalDocumentSet(t,e){const n=Ea();return this.mutations.forEach((r=>{const i=t.get(r.key),s=i.overlayedDocument;let o=this.applyToLocalView(s,i.mutatedFields);o=e.has(r.key)?null:o;const a=Ka(s,o);null!==a&&n.set(r.key,a),s.isValidDocument()||s.convertToNoDocument(Ss.min())})),n}keys(){return this.mutations.reduce(((t,e)=>t.add(e.key)),Ca())}isEqual(t){return this.batchId===t.batchId&&Ts(this.mutations,t.mutations,((t,e)=>Ya(t,e)))&&Ts(this.baseMutations,t.baseMutations,((t,e)=>Ya(t,e)))}}class sc{constructor(t,e,n,r){this.batch=t,this.commitVersion=e,this.mutationResults=n,this.docVersions=r}static from(t,e,n){as(t.mutations.length===n.length);let r=Ia;const i=t.mutations;for(let t=0;t<i.length;t++)r=r.insert(i[t].key,n[t].version);return new sc(t,e,n,r)}}class oc{constructor(t,e){this.largestBatchId=t,this.mutation=e}getKey(){return this.mutation.key}isEqual(t){return null!==t&&this.mutation===t.mutation}toString(){return`Overlay{\n      largestBatchId: ${this.largestBatchId},\n      mutation: ${this.mutation.toString()}\n    }`}}class ac{constructor(t,e){this.count=t,this.unchangedNames=e}}var cc,uc;function hc(t){if(void 0===t)return rs("GRPC error has no .code"),us.UNKNOWN;switch(t){case cc.OK:return us.OK;case cc.CANCELLED:return us.CANCELLED;case cc.UNKNOWN:return us.UNKNOWN;case cc.DEADLINE_EXCEEDED:return us.DEADLINE_EXCEEDED;case cc.RESOURCE_EXHAUSTED:return us.RESOURCE_EXHAUSTED;case cc.INTERNAL:return us.INTERNAL;case cc.UNAVAILABLE:return us.UNAVAILABLE;case cc.UNAUTHENTICATED:return us.UNAUTHENTICATED;case cc.INVALID_ARGUMENT:return us.INVALID_ARGUMENT;case cc.NOT_FOUND:return us.NOT_FOUND;case cc.ALREADY_EXISTS:return us.ALREADY_EXISTS;case cc.PERMISSION_DENIED:return us.PERMISSION_DENIED;case cc.FAILED_PRECONDITION:return us.FAILED_PRECONDITION;case cc.ABORTED:return us.ABORTED;case cc.OUT_OF_RANGE:return us.OUT_OF_RANGE;case cc.UNIMPLEMENTED:return us.UNIMPLEMENTED;case cc.DATA_LOSS:return us.DATA_LOSS;default:return os()}}(uc=cc||(cc={}))[uc.OK=0]="OK",uc[uc.CANCELLED=1]="CANCELLED",uc[uc.UNKNOWN=2]="UNKNOWN",uc[uc.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",uc[uc.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",uc[uc.NOT_FOUND=5]="NOT_FOUND",uc[uc.ALREADY_EXISTS=6]="ALREADY_EXISTS",uc[uc.PERMISSION_DENIED=7]="PERMISSION_DENIED",uc[uc.UNAUTHENTICATED=16]="UNAUTHENTICATED",uc[uc.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",uc[uc.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",uc[uc.ABORTED=10]="ABORTED",uc[uc.OUT_OF_RANGE=11]="OUT_OF_RANGE",uc[uc.UNIMPLEMENTED=12]="UNIMPLEMENTED",uc[uc.INTERNAL=13]="INTERNAL",uc[uc.UNAVAILABLE=14]="UNAVAILABLE",uc[uc.DATA_LOSS=15]="DATA_LOSS";class lc{constructor(){this.onExistenceFilterMismatchCallbacks=new Map}static get instance(){return dc}static getOrCreateInstance(){return null===dc&&(dc=new lc),dc}onExistenceFilterMismatch(t){const e=Symbol();return this.onExistenceFilterMismatchCallbacks.set(e,t),()=>this.onExistenceFilterMismatchCallbacks.delete(e)}notifyOnExistenceFilterMismatch(t){this.onExistenceFilterMismatchCallbacks.forEach((e=>e(t)))}}let dc=null;const fc=new Yi([4294967295,4294967295],0);function pc(t){const e=(new TextEncoder).encode(t),n=new Wi;return n.update(e),new Uint8Array(n.digest())}function gc(t){const e=new DataView(t.buffer),n=e.getUint32(0,!0),r=e.getUint32(4,!0),i=e.getUint32(8,!0),s=e.getUint32(12,!0);return[new Yi([n,r],0),new Yi([i,s],0)]}class mc{constructor(t,e,n){if(this.bitmap=t,this.padding=e,this.hashCount=n,e<0||e>=8)throw new yc(`Invalid padding: ${e}`);if(n<0)throw new yc(`Invalid hash count: ${n}`);if(t.length>0&&0===this.hashCount)throw new yc(`Invalid hash count: ${n}`);if(0===t.length&&0!==e)throw new yc(`Invalid padding when bitmap length is 0: ${e}`);this.It=8*t.length-e,this.Tt=Yi.fromNumber(this.It)}Et(t,e,n){let r=t.add(e.multiply(Yi.fromNumber(n)));return 1===r.compare(fc)&&(r=new Yi([r.getBits(0),r.getBits(1)],0)),r.modulo(this.Tt).toNumber()}At(t){return!!(this.bitmap[Math.floor(t/8)]&1<<t%8)}vt(t){if(0===this.It)return!1;const e=pc(t),[n,r]=gc(e);for(let t=0;t<this.hashCount;t++){const e=this.Et(n,r,t);if(!this.At(e))return!1}return!0}static create(t,e,n){const r=t%8==0?0:8-t%8,i=new Uint8Array(Math.ceil(t/8)),s=new mc(i,r,e);return n.forEach((t=>s.insert(t))),s}insert(t){if(0===this.It)return;const e=pc(t),[n,r]=gc(e);for(let t=0;t<this.hashCount;t++){const e=this.Et(n,r,t);this.Rt(e)}}Rt(t){const e=Math.floor(t/8),n=t%8;this.bitmap[e]|=1<<n}}class yc extends Error{constructor(){super(...arguments),this.name="BloomFilterError"}}class vc{constructor(t,e,n,r,i){this.snapshotVersion=t,this.targetChanges=e,this.targetMismatches=n,this.documentUpdates=r,this.resolvedLimboDocuments=i}static createSynthesizedRemoteEventForCurrentChange(t,e,n){const r=new Map;return r.set(t,wc.createSynthesizedTargetChangeForCurrentChange(t,e,n)),new vc(Ss.min(),r,new $s(Es),ma(),Ca())}}class wc{constructor(t,e,n,r,i){this.resumeToken=t,this.current=e,this.addedDocuments=n,this.modifiedDocuments=r,this.removedDocuments=i}static createSynthesizedTargetChangeForCurrentChange(t,e,n){return new wc(n,e,Ca(),Ca(),Ca())}}class bc{constructor(t,e,n,r){this.Pt=t,this.removedTargetIds=e,this.key=n,this.bt=r}}class Ec{constructor(t,e){this.targetId=t,this.Vt=e}}class Tc{constructor(t,e,n=Ys.EMPTY_BYTE_STRING,r=null){this.state=t,this.targetIds=e,this.resumeToken=n,this.cause=r}}class Ic{constructor(){this.St=0,this.Dt=Ac(),this.Ct=Ys.EMPTY_BYTE_STRING,this.xt=!1,this.Nt=!0}get current(){return this.xt}get resumeToken(){return this.Ct}get kt(){return 0!==this.St}get Mt(){return this.Nt}$t(t){t.approximateByteSize()>0&&(this.Nt=!0,this.Ct=t)}Ot(){let t=Ca(),e=Ca(),n=Ca();return this.Dt.forEach(((r,i)=>{switch(i){case 0:t=t.add(r);break;case 2:e=e.add(r);break;case 1:n=n.add(r);break;default:os()}})),new wc(this.Ct,this.xt,t,e,n)}Ft(){this.Nt=!1,this.Dt=Ac()}Bt(t,e){this.Nt=!0,this.Dt=this.Dt.insert(t,e)}Lt(t){this.Nt=!0,this.Dt=this.Dt.remove(t)}qt(){this.St+=1}Ut(){this.St-=1}Kt(){this.Nt=!0,this.xt=!0}}class Sc{constructor(t){this.Gt=t,this.Qt=new Map,this.jt=ma(),this.zt=Cc(),this.Wt=new $s(Es)}Ht(t){for(const e of t.Pt)t.bt&&t.bt.isFoundDocument()?this.Jt(e,t.bt):this.Yt(e,t.key,t.bt);for(const e of t.removedTargetIds)this.Yt(e,t.key,t.bt)}Xt(t){this.forEachTarget(t,(e=>{const n=this.Zt(e);switch(t.state){case 0:this.te(e)&&n.$t(t.resumeToken);break;case 1:n.Ut(),n.kt||n.Ft(),n.$t(t.resumeToken);break;case 2:n.Ut(),n.kt||this.removeTarget(e);break;case 3:this.te(e)&&(n.Kt(),n.$t(t.resumeToken));break;case 4:this.te(e)&&(this.ee(e),n.$t(t.resumeToken));break;default:os()}}))}forEachTarget(t,e){t.targetIds.length>0?t.targetIds.forEach(e):this.Qt.forEach(((t,n)=>{this.te(n)&&e(n)}))}ne(t){var e;const n=t.targetId,r=t.Vt.count,i=this.se(n);if(i){const s=i.target;if(Xo(s))if(0===r){const t=new Ns(s.path);this.Yt(n,t,Co.newNoDocument(t,Ss.min()))}else as(1===r);else{const i=this.ie(n);if(i!==r){const r=this.re(t,i);if(0!==r){this.ee(n);const t=2===r?"TargetPurposeExistenceFilterMismatchBloom":"TargetPurposeExistenceFilterMismatch";this.Wt=this.Wt.insert(n,t)}null===(e=lc.instance)||void 0===e||e.notifyOnExistenceFilterMismatch(function(t,e,n){var r,i,s,o,a,c;const u={localCacheCount:e,existenceFilterCount:n.count},h=n.unchangedNames;return h&&(u.bloomFilter={applied:0===t,hashCount:null!==(r=null==h?void 0:h.hashCount)&&void 0!==r?r:0,bitmapLength:null!==(o=null===(s=null===(i=null==h?void 0:h.bits)||void 0===i?void 0:i.bitmap)||void 0===s?void 0:s.length)&&void 0!==o?o:0,padding:null!==(c=null===(a=null==h?void 0:h.bits)||void 0===a?void 0:a.padding)&&void 0!==c?c:0}),u}(r,i,t.Vt))}}}}re(t,e){const{unchangedNames:n,count:r}=t.Vt;if(!n||!n.bits)return 1;const{bits:{bitmap:i="",padding:s=0},hashCount:o=0}=n;let a,c;try{a=to(i).toUint8Array()}catch(t){if(t instanceof Ws)return is("Decoding the base64 bloom filter in existence filter failed ("+t.message+"); ignoring the bloom filter and falling back to full re-query."),1;throw t}try{c=new mc(a,s,o)}catch(t){return is(t instanceof yc?"BloomFilter error: ":"Applying bloom filter failed: ",t),1}return 0===c.It?1:r!==e-this.oe(t.targetId,c)?2:0}oe(t,e){const n=this.Gt.getRemoteKeysForTarget(t);let r=0;return n.forEach((n=>{const i=this.Gt.ue(),s=`projects/${i.projectId}/databases/${i.database}/documents/${n.path.canonicalString()}`;e.vt(s)||(this.Yt(t,n,null),r++)})),r}ce(t){const e=new Map;this.Qt.forEach(((n,r)=>{const i=this.se(r);if(i){if(n.current&&Xo(i.target)){const e=new Ns(i.target.path);null!==this.jt.get(e)||this.ae(r,e)||this.Yt(r,e,Co.newNoDocument(e,t))}n.Mt&&(e.set(r,n.Ot()),n.Ft())}}));let n=Ca();this.zt.forEach(((t,e)=>{let r=!0;e.forEachWhile((t=>{const e=this.se(t);return!e||"TargetPurposeLimboResolution"===e.purpose||(r=!1,!1)})),r&&(n=n.add(t))})),this.jt.forEach(((e,n)=>n.setReadTime(t)));const r=new vc(t,e,this.Wt,this.jt,n);return this.jt=ma(),this.zt=Cc(),this.Wt=new $s(Es),r}Jt(t,e){if(!this.te(t))return;const n=this.ae(t,e.key)?2:0;this.Zt(t).Bt(e.key,n),this.jt=this.jt.insert(e.key,e),this.zt=this.zt.insert(e.key,this.he(e.key).add(t))}Yt(t,e,n){if(!this.te(t))return;const r=this.Zt(t);this.ae(t,e)?r.Bt(e,1):r.Lt(e),this.zt=this.zt.insert(e,this.he(e).delete(t)),n&&(this.jt=this.jt.insert(e,n))}removeTarget(t){this.Qt.delete(t)}ie(t){const e=this.Zt(t).Ot();return this.Gt.getRemoteKeysForTarget(t).size+e.addedDocuments.size-e.removedDocuments.size}qt(t){this.Zt(t).qt()}Zt(t){let e=this.Qt.get(t);return e||(e=new Ic,this.Qt.set(t,e)),e}he(t){let e=this.zt.get(t);return e||(e=new Ks(Es),this.zt=this.zt.insert(t,e)),e}te(t){const e=null!==this.se(t);return e||ns("WatchChangeAggregator","Detected inactive target",t),e}se(t){const e=this.Qt.get(t);return e&&e.kt?null:this.Gt.le(t)}ee(t){this.Qt.set(t,new Ic),this.Gt.getRemoteKeysForTarget(t).forEach((e=>{this.Yt(t,e,null)}))}ae(t,e){return this.Gt.getRemoteKeysForTarget(t).has(e)}}function Cc(){return new $s(Ns.comparator)}function Ac(){return new $s(Ns.comparator)}const _c={asc:"ASCENDING",desc:"DESCENDING"},xc={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},Nc={and:"AND",or:"OR"};class Dc{constructor(t,e){this.databaseId=t,this.useProto3Json=e}}function kc(t,e){return t.useProto3Json||Vs(e)?e:{value:e}}function Rc(t,e){return t.useProto3Json?`${new Date(1e3*e.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")}.${("000000000"+e.nanoseconds).slice(-9)}Z`:{seconds:""+e.seconds,nanos:e.nanoseconds}}function Oc(t,e){return t.useProto3Json?e.toBase64():e.toUint8Array()}function Mc(t,e){return Rc(t,e.toTimestamp())}function Lc(t){return as(!!t),Ss.fromTimestamp(function(t){const e=Js(t);return new Is(e.seconds,e.nanos)}(t))}function Pc(t,e){return function(t){return new As(["projects",t.projectId,"databases",t.database])}(t).child("documents").child(e).canonicalString()}function Fc(t){const e=As.fromString(t);return as(eu(e)),e}function Vc(t,e){return Pc(t.databaseId,e.path)}function Uc(t,e){const n=Fc(e);if(n.get(1)!==t.databaseId.projectId)throw new hs(us.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+n.get(1)+" vs "+t.databaseId.projectId);if(n.get(3)!==t.databaseId.database)throw new hs(us.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+n.get(3)+" vs "+t.databaseId.database);return new Ns(qc(n))}function Bc(t,e){return Pc(t.databaseId,e)}function jc(t){return new As(["projects",t.databaseId.projectId,"databases",t.databaseId.database]).canonicalString()}function qc(t){return as(t.length>4&&"documents"===t.get(4)),t.popFirst(5)}function $c(t,e,n){return{name:Vc(t,e),fields:n.value.mapValue.fields}}function zc(t,e){return{documents:[Bc(t,e.path)]}}function Gc(t,e){const n={structuredQuery:{}},r=e.path;null!==e.collectionGroup?(n.parent=Bc(t,r),n.structuredQuery.from=[{collectionId:e.collectionGroup,allDescendants:!0}]):(n.parent=Bc(t,r.popLast()),n.structuredQuery.from=[{collectionId:r.lastSegment()}]);const i=function(t){if(0!==t.length)return Zc(Oo.create(t,"and"))}(e.filters);i&&(n.structuredQuery.where=i);const s=function(t){if(0!==t.length)return t.map((t=>function(t){return{field:Xc(t.field),direction:Qc(t.dir)}}(t)))}(e.orderBy);s&&(n.structuredQuery.orderBy=s);const o=kc(t,e.limit);var a;return null!==o&&(n.structuredQuery.limit=o),e.startAt&&(n.structuredQuery.startAt={before:(a=e.startAt).inclusive,values:a.position}),e.endAt&&(n.structuredQuery.endAt=function(t){return{before:!t.inclusive,values:t.position}}(e.endAt)),n}function Kc(t){let e=function(t){const e=Fc(t);return 4===e.length?As.emptyPath():qc(e)}(t.parent);const n=t.structuredQuery,r=n.from?n.from.length:0;let i=null;if(r>0){as(1===r);const t=n.from[0];t.allDescendants?i=t.collectionId:e=e.child(t.collectionId)}let s=[];n.where&&(s=function(t){const e=Hc(t);return e instanceof Oo&&Lo(e)?e.getFilters():[e]}(n.where));let o=[];n.orderBy&&(o=n.orderBy.map((t=>function(t){return new No(Jc(t.field),function(t){switch(t){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}(t.direction))}(t))));let a=null;n.limit&&(a=function(t){let e;return e="object"==typeof t?t.value:t,Vs(e)?null:e}(n.limit));let c=null;n.startAt&&(c=function(t){const e=!!t.before,n=t.values||[];return new Ao(n,e)}(n.startAt));let u=null;return n.endAt&&(u=function(t){const e=!t.before,n=t.values||[];return new Ao(n,e)}(n.endAt)),function(t,e,n,r,i,s,o,a){return new Jo(t,e,n,r,i,s,o,a)}(e,i,o,s,a,"F",c,u)}function Hc(t){return void 0!==t.unaryFilter?function(t){switch(t.unaryFilter.op){case"IS_NAN":const e=Jc(t.unaryFilter.field);return Ro.create(e,"==",{doubleValue:NaN});case"IS_NULL":const n=Jc(t.unaryFilter.field);return Ro.create(n,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":const r=Jc(t.unaryFilter.field);return Ro.create(r,"!=",{doubleValue:NaN});case"IS_NOT_NULL":const i=Jc(t.unaryFilter.field);return Ro.create(i,"!=",{nullValue:"NULL_VALUE"});default:return os()}}(t):void 0!==t.fieldFilter?function(t){return Ro.create(Jc(t.fieldFilter.field),function(t){switch(t){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";default:return os()}}(t.fieldFilter.op),t.fieldFilter.value)}(t):void 0!==t.compositeFilter?function(t){return Oo.create(t.compositeFilter.filters.map((t=>Hc(t))),function(t){switch(t){case"AND":return"and";case"OR":return"or";default:return os()}}(t.compositeFilter.op))}(t):os()}function Qc(t){return _c[t]}function Wc(t){return xc[t]}function Yc(t){return Nc[t]}function Xc(t){return{fieldPath:t.canonicalString()}}function Jc(t){return xs.fromServerFormat(t.fieldPath)}function Zc(t){return t instanceof Ro?function(t){if("=="===t.op){if(wo(t.value))return{unaryFilter:{field:Xc(t.field),op:"IS_NAN"}};if(vo(t.value))return{unaryFilter:{field:Xc(t.field),op:"IS_NULL"}}}else if("!="===t.op){if(wo(t.value))return{unaryFilter:{field:Xc(t.field),op:"IS_NOT_NAN"}};if(vo(t.value))return{unaryFilter:{field:Xc(t.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:Xc(t.field),op:Wc(t.op),value:t.value}}}(t):t instanceof Oo?function(t){const e=t.getFilters().map((t=>Zc(t)));return 1===e.length?e[0]:{compositeFilter:{op:Yc(t.op),filters:e}}}(t):os()}function tu(t){const e=[];return t.fields.forEach((t=>e.push(t.canonicalString()))),{fieldPaths:e}}function eu(t){return t.length>=4&&"projects"===t.get(0)&&"databases"===t.get(2)}class nu{constructor(t,e,n,r,i=Ss.min(),s=Ss.min(),o=Ys.EMPTY_BYTE_STRING,a=null){this.target=t,this.targetId=e,this.purpose=n,this.sequenceNumber=r,this.snapshotVersion=i,this.lastLimboFreeSnapshotVersion=s,this.resumeToken=o,this.expectedCount=a}withSequenceNumber(t){return new nu(this.target,this.targetId,this.purpose,t,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,this.expectedCount)}withResumeToken(t,e){return new nu(this.target,this.targetId,this.purpose,this.sequenceNumber,e,this.lastLimboFreeSnapshotVersion,t,null)}withExpectedCount(t){return new nu(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken,t)}withLastLimboFreeSnapshotVersion(t){return new nu(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,t,this.resumeToken,this.expectedCount)}}class ru{constructor(t){this.fe=t}}function iu(t){const e=Kc({parent:t.parent,structuredQuery:t.structuredQuery});return"LAST"===t.limitType?aa(e,e.limit,"L"):e}class su{constructor(){}_e(t,e){this.me(t,e),e.ge()}me(t,e){if("nullValue"in t)this.ye(e,5);else if("booleanValue"in t)this.ye(e,10),e.pe(t.booleanValue?1:0);else if("integerValue"in t)this.ye(e,15),e.pe(Zs(t.integerValue));else if("doubleValue"in t){const n=Zs(t.doubleValue);isNaN(n)?this.ye(e,13):(this.ye(e,15),Us(n)?e.pe(0):e.pe(n))}else if("timestampValue"in t){const n=t.timestampValue;this.ye(e,20),"string"==typeof n?e.Ie(n):(e.Ie(`${n.seconds||""}`),e.pe(n.nanos||0))}else if("stringValue"in t)this.Te(t.stringValue,e),this.Ee(e);else if("bytesValue"in t)this.ye(e,30),e.Ae(to(t.bytesValue)),this.Ee(e);else if("referenceValue"in t)this.ve(t.referenceValue,e);else if("geoPointValue"in t){const n=t.geoPointValue;this.ye(e,45),e.pe(n.latitude||0),e.pe(n.longitude||0)}else"mapValue"in t?To(t)?this.ye(e,Number.MAX_SAFE_INTEGER):(this.Re(t.mapValue,e),this.Ee(e)):"arrayValue"in t?(this.Pe(t.arrayValue,e),this.Ee(e)):os()}Te(t,e){this.ye(e,25),this.be(t,e)}be(t,e){e.Ie(t)}Re(t,e){const n=t.fields||{};this.ye(e,55);for(const t of Object.keys(n))this.Te(t,e),this.me(n[t],e)}Pe(t,e){const n=t.values||[];this.ye(e,50);for(const t of n)this.me(t,e)}ve(t,e){this.ye(e,37),Ns.fromName(t).path.forEach((t=>{this.ye(e,60),this.be(t,e)}))}ye(t,e){t.pe(e)}Ee(t){t.pe(2)}}su.Ve=new su;class ou{constructor(){this.rn=new au}addToCollectionParentIndex(t,e){return this.rn.add(e),Ls.resolve()}getCollectionParents(t,e){return Ls.resolve(this.rn.getEntries(e))}addFieldIndex(t,e){return Ls.resolve()}deleteFieldIndex(t,e){return Ls.resolve()}getDocumentsMatchingTarget(t,e){return Ls.resolve(null)}getIndexType(t,e){return Ls.resolve(0)}getFieldIndexes(t,e){return Ls.resolve([])}getNextCollectionGroupToUpdate(t){return Ls.resolve(null)}getMinOffset(t,e){return Ls.resolve(ks.min())}getMinOffsetFromCollectionGroup(t,e){return Ls.resolve(ks.min())}updateCollectionGroup(t,e,n){return Ls.resolve()}updateIndexEntries(t,e){return Ls.resolve()}}class au{constructor(){this.index={}}add(t){const e=t.lastSegment(),n=t.popLast(),r=this.index[e]||new Ks(As.comparator),i=!r.has(n);return this.index[e]=r.add(n),i}has(t){const e=t.lastSegment(),n=t.popLast(),r=this.index[e];return r&&r.has(n)}getEntries(t){return(this.index[t]||new Ks(As.comparator)).toArray()}}new Uint8Array(0);class cu{constructor(t,e,n){this.cacheSizeCollectionThreshold=t,this.percentileToCollect=e,this.maximumSequenceNumbersToCollect=n}static withCacheSize(t){return new cu(t,cu.DEFAULT_COLLECTION_PERCENTILE,cu.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)}}cu.DEFAULT_COLLECTION_PERCENTILE=10,cu.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,cu.DEFAULT=new cu(41943040,cu.DEFAULT_COLLECTION_PERCENTILE,cu.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),cu.DISABLED=new cu(-1,0,0);class uu{constructor(t){this.Nn=t}next(){return this.Nn+=2,this.Nn}static kn(){return new uu(0)}static Mn(){return new uu(-1)}}class hu{constructor(){this.changes=new pa((t=>t.toString()),((t,e)=>t.isEqual(e))),this.changesApplied=!1}addEntry(t){this.assertNotApplied(),this.changes.set(t.key,t)}removeEntry(t,e){this.assertNotApplied(),this.changes.set(t,Co.newInvalidDocument(t).setReadTime(e))}getEntry(t,e){this.assertNotApplied();const n=this.changes.get(e);return void 0!==n?Ls.resolve(n):this.getFromCache(t,e)}getEntries(t,e){return this.getAllFromCache(t,e)}apply(t){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(t)}assertNotApplied(){}}class lu{constructor(t,e){this.overlayedDocument=t,this.mutatedFields=e}}class du{constructor(t,e,n,r){this.remoteDocumentCache=t,this.mutationQueue=e,this.documentOverlayCache=n,this.indexManager=r}getDocument(t,e){let n=null;return this.documentOverlayCache.getOverlay(t,e).next((r=>(n=r,this.remoteDocumentCache.getEntry(t,e)))).next((t=>(null!==n&&Qa(n.mutation,t,Qs.empty(),Is.now()),t)))}getDocuments(t,e){return this.remoteDocumentCache.getEntries(t,e).next((e=>this.getLocalViewOfDocuments(t,e,Ca()).next((()=>e))))}getLocalViewOfDocuments(t,e,n=Ca()){const r=ba();return this.populateOverlays(t,r,e).next((()=>this.computeViews(t,e,r,n).next((t=>{let e=va();return t.forEach(((t,n)=>{e=e.insert(t,n.overlayedDocument)})),e}))))}getOverlayedDocuments(t,e){const n=ba();return this.populateOverlays(t,n,e).next((()=>this.computeViews(t,e,n,Ca())))}populateOverlays(t,e,n){const r=[];return n.forEach((t=>{e.has(t)||r.push(t)})),this.documentOverlayCache.getOverlays(t,r).next((t=>{t.forEach(((t,n)=>{e.set(t,n)}))}))}computeViews(t,e,n,r){let i=ma();const s=Ta(),o=Ta();return e.forEach(((t,e)=>{const o=n.get(e.key);r.has(e.key)&&(void 0===o||o.mutation instanceof Ja)?i=i.insert(e.key,e):void 0!==o?(s.set(e.key,o.mutation.getFieldMask()),Qa(o.mutation,e,o.mutation.getFieldMask(),Is.now())):s.set(e.key,Qs.empty())})),this.recalculateAndSaveOverlays(t,i).next((t=>(t.forEach(((t,e)=>s.set(t,e))),e.forEach(((t,e)=>{var n;return o.set(t,new lu(e,null!==(n=s.get(t))&&void 0!==n?n:null))})),o)))}recalculateAndSaveOverlays(t,e){const n=Ta();let r=new $s(((t,e)=>t-e)),i=Ca();return this.mutationQueue.getAllMutationBatchesAffectingDocumentKeys(t,e).next((t=>{for(const i of t)i.keys().forEach((t=>{const s=e.get(t);if(null===s)return;let o=n.get(t)||Qs.empty();o=i.applyToLocalView(s,o),n.set(t,o);const a=(r.get(i.batchId)||Ca()).add(t);r=r.insert(i.batchId,a)}))})).next((()=>{const s=[],o=r.getReverseIterator();for(;o.hasNext();){const r=o.getNext(),a=r.key,c=r.value,u=Ea();c.forEach((t=>{if(!i.has(t)){const r=Ka(e.get(t),n.get(t));null!==r&&u.set(t,r),i=i.add(t)}})),s.push(this.documentOverlayCache.saveOverlays(t,a,u))}return Ls.waitFor(s)})).next((()=>n))}recalculateAndSaveOverlaysForDocumentKeys(t,e){return this.remoteDocumentCache.getEntries(t,e).next((e=>this.recalculateAndSaveOverlays(t,e)))}getDocumentsMatchingQuery(t,e,n){return function(t){return Ns.isDocumentKey(t.path)&&null===t.collectionGroup&&0===t.filters.length}(e)?this.getDocumentsMatchingDocumentQuery(t,e.path):ra(e)?this.getDocumentsMatchingCollectionGroupQuery(t,e,n):this.getDocumentsMatchingCollectionQuery(t,e,n)}getNextDocuments(t,e,n,r){return this.remoteDocumentCache.getAllFromCollectionGroup(t,e,n,r).next((i=>{const s=r-i.size>0?this.documentOverlayCache.getOverlaysForCollectionGroup(t,e,n.largestBatchId,r-i.size):Ls.resolve(ba());let o=-1,a=i;return s.next((e=>Ls.forEach(e,((e,n)=>(o<n.largestBatchId&&(o=n.largestBatchId),i.get(e)?Ls.resolve():this.remoteDocumentCache.getEntry(t,e).next((t=>{a=a.insert(e,t)}))))).next((()=>this.populateOverlays(t,e,i))).next((()=>this.computeViews(t,a,e,Ca()))).next((t=>({batchId:o,changes:wa(t)})))))}))}getDocumentsMatchingDocumentQuery(t,e){return this.getDocument(t,new Ns(e)).next((t=>{let e=va();return t.isFoundDocument()&&(e=e.insert(t.key,t)),e}))}getDocumentsMatchingCollectionGroupQuery(t,e,n){const r=e.collectionGroup;let i=va();return this.indexManager.getCollectionParents(t,r).next((s=>Ls.forEach(s,(s=>{const o=function(t,e){return new Jo(e,null,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt)}(e,s.child(r));return this.getDocumentsMatchingCollectionQuery(t,o,n).next((t=>{t.forEach(((t,e)=>{i=i.insert(t,e)}))}))})).next((()=>i))))}getDocumentsMatchingCollectionQuery(t,e,n){let r;return this.documentOverlayCache.getOverlaysForCollection(t,e.path,n.largestBatchId).next((i=>(r=i,this.remoteDocumentCache.getDocumentsMatchingQuery(t,e,n,r)))).next((t=>{r.forEach(((e,n)=>{const r=n.getKey();null===t.get(r)&&(t=t.insert(r,Co.newInvalidDocument(r)))}));let n=va();return t.forEach(((t,i)=>{const s=r.get(t);void 0!==s&&Qa(s.mutation,i,Qs.empty(),Is.now()),la(e,i)&&(n=n.insert(t,i))})),n}))}}class fu{constructor(t){this.serializer=t,this.cs=new Map,this.hs=new Map}getBundleMetadata(t,e){return Ls.resolve(this.cs.get(e))}saveBundleMetadata(t,e){var n;return this.cs.set(e.id,{id:(n=e).id,version:n.version,createTime:Lc(n.createTime)}),Ls.resolve()}getNamedQuery(t,e){return Ls.resolve(this.hs.get(e))}saveNamedQuery(t,e){return this.hs.set(e.name,function(t){return{name:t.name,query:iu(t.bundledQuery),readTime:Lc(t.readTime)}}(e)),Ls.resolve()}}class pu{constructor(){this.overlays=new $s(Ns.comparator),this.ls=new Map}getOverlay(t,e){return Ls.resolve(this.overlays.get(e))}getOverlays(t,e){const n=ba();return Ls.forEach(e,(e=>this.getOverlay(t,e).next((t=>{null!==t&&n.set(e,t)})))).next((()=>n))}saveOverlays(t,e,n){return n.forEach(((n,r)=>{this.we(t,e,r)})),Ls.resolve()}removeOverlaysForBatchId(t,e,n){const r=this.ls.get(n);return void 0!==r&&(r.forEach((t=>this.overlays=this.overlays.remove(t))),this.ls.delete(n)),Ls.resolve()}getOverlaysForCollection(t,e,n){const r=ba(),i=e.length+1,s=new Ns(e.child("")),o=this.overlays.getIteratorFrom(s);for(;o.hasNext();){const t=o.getNext().value,s=t.getKey();if(!e.isPrefixOf(s.path))break;s.path.length===i&&t.largestBatchId>n&&r.set(t.getKey(),t)}return Ls.resolve(r)}getOverlaysForCollectionGroup(t,e,n,r){let i=new $s(((t,e)=>t-e));const s=this.overlays.getIterator();for(;s.hasNext();){const t=s.getNext().value;if(t.getKey().getCollectionGroup()===e&&t.largestBatchId>n){let e=i.get(t.largestBatchId);null===e&&(e=ba(),i=i.insert(t.largestBatchId,e)),e.set(t.getKey(),t)}}const o=ba(),a=i.getIterator();for(;a.hasNext()&&(a.getNext().value.forEach(((t,e)=>o.set(t,e))),!(o.size()>=r)););return Ls.resolve(o)}we(t,e,n){const r=this.overlays.get(n.key);if(null!==r){const t=this.ls.get(r.largestBatchId).delete(n.key);this.ls.set(r.largestBatchId,t)}this.overlays=this.overlays.insert(n.key,new oc(e,n));let i=this.ls.get(e);void 0===i&&(i=Ca(),this.ls.set(e,i)),this.ls.set(e,i.add(n.key))}}class gu{constructor(){this.fs=new Ks(mu.ds),this.ws=new Ks(mu._s)}isEmpty(){return this.fs.isEmpty()}addReference(t,e){const n=new mu(t,e);this.fs=this.fs.add(n),this.ws=this.ws.add(n)}gs(t,e){t.forEach((t=>this.addReference(t,e)))}removeReference(t,e){this.ys(new mu(t,e))}ps(t,e){t.forEach((t=>this.removeReference(t,e)))}Is(t){const e=new Ns(new As([])),n=new mu(e,t),r=new mu(e,t+1),i=[];return this.ws.forEachInRange([n,r],(t=>{this.ys(t),i.push(t.key)})),i}Ts(){this.fs.forEach((t=>this.ys(t)))}ys(t){this.fs=this.fs.delete(t),this.ws=this.ws.delete(t)}Es(t){const e=new Ns(new As([])),n=new mu(e,t),r=new mu(e,t+1);let i=Ca();return this.ws.forEachInRange([n,r],(t=>{i=i.add(t.key)})),i}containsKey(t){const e=new mu(t,0),n=this.fs.firstAfterOrEqual(e);return null!==n&&t.isEqual(n.key)}}class mu{constructor(t,e){this.key=t,this.As=e}static ds(t,e){return Ns.comparator(t.key,e.key)||Es(t.As,e.As)}static _s(t,e){return Es(t.As,e.As)||Ns.comparator(t.key,e.key)}}class yu{constructor(t,e){this.indexManager=t,this.referenceDelegate=e,this.mutationQueue=[],this.vs=1,this.Rs=new Ks(mu.ds)}checkEmpty(t){return Ls.resolve(0===this.mutationQueue.length)}addMutationBatch(t,e,n,r){const i=this.vs;this.vs++,this.mutationQueue.length>0&&this.mutationQueue[this.mutationQueue.length-1];const s=new ic(i,e,n,r);this.mutationQueue.push(s);for(const e of r)this.Rs=this.Rs.add(new mu(e.key,i)),this.indexManager.addToCollectionParentIndex(t,e.key.path.popLast());return Ls.resolve(s)}lookupMutationBatch(t,e){return Ls.resolve(this.Ps(e))}getNextMutationBatchAfterBatchId(t,e){const n=e+1,r=this.bs(n),i=r<0?0:r;return Ls.resolve(this.mutationQueue.length>i?this.mutationQueue[i]:null)}getHighestUnacknowledgedBatchId(){return Ls.resolve(0===this.mutationQueue.length?-1:this.vs-1)}getAllMutationBatches(t){return Ls.resolve(this.mutationQueue.slice())}getAllMutationBatchesAffectingDocumentKey(t,e){const n=new mu(e,0),r=new mu(e,Number.POSITIVE_INFINITY),i=[];return this.Rs.forEachInRange([n,r],(t=>{const e=this.Ps(t.As);i.push(e)})),Ls.resolve(i)}getAllMutationBatchesAffectingDocumentKeys(t,e){let n=new Ks(Es);return e.forEach((t=>{const e=new mu(t,0),r=new mu(t,Number.POSITIVE_INFINITY);this.Rs.forEachInRange([e,r],(t=>{n=n.add(t.As)}))})),Ls.resolve(this.Vs(n))}getAllMutationBatchesAffectingQuery(t,e){const n=e.path,r=n.length+1;let i=n;Ns.isDocumentKey(i)||(i=i.child(""));const s=new mu(new Ns(i),0);let o=new Ks(Es);return this.Rs.forEachWhile((t=>{const e=t.key.path;return!!n.isPrefixOf(e)&&(e.length===r&&(o=o.add(t.As)),!0)}),s),Ls.resolve(this.Vs(o))}Vs(t){const e=[];return t.forEach((t=>{const n=this.Ps(t);null!==n&&e.push(n)})),e}removeMutationBatch(t,e){as(0===this.Ss(e.batchId,"removed")),this.mutationQueue.shift();let n=this.Rs;return Ls.forEach(e.mutations,(r=>{const i=new mu(r.key,e.batchId);return n=n.delete(i),this.referenceDelegate.markPotentiallyOrphaned(t,r.key)})).next((()=>{this.Rs=n}))}Cn(t){}containsKey(t,e){const n=new mu(e,0),r=this.Rs.firstAfterOrEqual(n);return Ls.resolve(e.isEqual(r&&r.key))}performConsistencyCheck(t){return this.mutationQueue.length,Ls.resolve()}Ss(t,e){return this.bs(t)}bs(t){return 0===this.mutationQueue.length?0:t-this.mutationQueue[0].batchId}Ps(t){const e=this.bs(t);return e<0||e>=this.mutationQueue.length?null:this.mutationQueue[e]}}class vu{constructor(t){this.Ds=t,this.docs=new $s(Ns.comparator),this.size=0}setIndexManager(t){this.indexManager=t}addEntry(t,e){const n=e.key,r=this.docs.get(n),i=r?r.size:0,s=this.Ds(e);return this.docs=this.docs.insert(n,{document:e.mutableCopy(),size:s}),this.size+=s-i,this.indexManager.addToCollectionParentIndex(t,n.path.popLast())}removeEntry(t){const e=this.docs.get(t);e&&(this.docs=this.docs.remove(t),this.size-=e.size)}getEntry(t,e){const n=this.docs.get(e);return Ls.resolve(n?n.document.mutableCopy():Co.newInvalidDocument(e))}getEntries(t,e){let n=ma();return e.forEach((t=>{const e=this.docs.get(t);n=n.insert(t,e?e.document.mutableCopy():Co.newInvalidDocument(t))})),Ls.resolve(n)}getDocumentsMatchingQuery(t,e,n,r){let i=ma();const s=e.path,o=new Ns(s.child("")),a=this.docs.getIteratorFrom(o);for(;a.hasNext();){const{key:t,value:{document:o}}=a.getNext();if(!s.isPrefixOf(t.path))break;t.path.length>s.length+1||Rs(Ds(o),n)<=0||(r.has(o.key)||la(e,o))&&(i=i.insert(o.key,o.mutableCopy()))}return Ls.resolve(i)}getAllFromCollectionGroup(t,e,n,r){os()}Cs(t,e){return Ls.forEach(this.docs,(t=>e(t)))}newChangeBuffer(t){return new wu(this)}getSize(t){return Ls.resolve(this.size)}}class wu extends hu{constructor(t){super(),this.os=t}applyChanges(t){const e=[];return this.changes.forEach(((n,r)=>{r.isValidDocument()?e.push(this.os.addEntry(t,r)):this.os.removeEntry(n)})),Ls.waitFor(e)}getFromCache(t,e){return this.os.getEntry(t,e)}getAllFromCache(t,e){return this.os.getEntries(t,e)}}class bu{constructor(t){this.persistence=t,this.xs=new pa((t=>Wo(t)),Yo),this.lastRemoteSnapshotVersion=Ss.min(),this.highestTargetId=0,this.Ns=0,this.ks=new gu,this.targetCount=0,this.Ms=uu.kn()}forEachTarget(t,e){return this.xs.forEach(((t,n)=>e(n))),Ls.resolve()}getLastRemoteSnapshotVersion(t){return Ls.resolve(this.lastRemoteSnapshotVersion)}getHighestSequenceNumber(t){return Ls.resolve(this.Ns)}allocateTargetId(t){return this.highestTargetId=this.Ms.next(),Ls.resolve(this.highestTargetId)}setTargetsMetadata(t,e,n){return n&&(this.lastRemoteSnapshotVersion=n),e>this.Ns&&(this.Ns=e),Ls.resolve()}Fn(t){this.xs.set(t.target,t);const e=t.targetId;e>this.highestTargetId&&(this.Ms=new uu(e),this.highestTargetId=e),t.sequenceNumber>this.Ns&&(this.Ns=t.sequenceNumber)}addTargetData(t,e){return this.Fn(e),this.targetCount+=1,Ls.resolve()}updateTargetData(t,e){return this.Fn(e),Ls.resolve()}removeTargetData(t,e){return this.xs.delete(e.target),this.ks.Is(e.targetId),this.targetCount-=1,Ls.resolve()}removeTargets(t,e,n){let r=0;const i=[];return this.xs.forEach(((s,o)=>{o.sequenceNumber<=e&&null===n.get(o.targetId)&&(this.xs.delete(s),i.push(this.removeMatchingKeysForTargetId(t,o.targetId)),r++)})),Ls.waitFor(i).next((()=>r))}getTargetCount(t){return Ls.resolve(this.targetCount)}getTargetData(t,e){const n=this.xs.get(e)||null;return Ls.resolve(n)}addMatchingKeys(t,e,n){return this.ks.gs(e,n),Ls.resolve()}removeMatchingKeys(t,e,n){this.ks.ps(e,n);const r=this.persistence.referenceDelegate,i=[];return r&&e.forEach((e=>{i.push(r.markPotentiallyOrphaned(t,e))})),Ls.waitFor(i)}removeMatchingKeysForTargetId(t,e){return this.ks.Is(e),Ls.resolve()}getMatchingKeysForTargetId(t,e){const n=this.ks.Es(e);return Ls.resolve(n)}containsKey(t,e){return Ls.resolve(this.ks.containsKey(e))}}class Eu{constructor(t,e){this.$s={},this.overlays={},this.Os=new Fs(0),this.Fs=!1,this.Fs=!0,this.referenceDelegate=t(this),this.Bs=new bu(this),this.indexManager=new ou,this.remoteDocumentCache=function(t){return new vu(t)}((t=>this.referenceDelegate.Ls(t))),this.serializer=new ru(e),this.qs=new fu(this.serializer)}start(){return Promise.resolve()}shutdown(){return this.Fs=!1,Promise.resolve()}get started(){return this.Fs}setDatabaseDeletedListener(){}setNetworkEnabled(){}getIndexManager(t){return this.indexManager}getDocumentOverlayCache(t){let e=this.overlays[t.toKey()];return e||(e=new pu,this.overlays[t.toKey()]=e),e}getMutationQueue(t,e){let n=this.$s[t.toKey()];return n||(n=new yu(e,this.referenceDelegate),this.$s[t.toKey()]=n),n}getTargetCache(){return this.Bs}getRemoteDocumentCache(){return this.remoteDocumentCache}getBundleCache(){return this.qs}runTransaction(t,e,n){ns("MemoryPersistence","Starting transaction:",t);const r=new Tu(this.Os.next());return this.referenceDelegate.Us(),n(r).next((t=>this.referenceDelegate.Ks(r).next((()=>t)))).toPromise().then((t=>(r.raiseOnCommittedEvent(),t)))}Gs(t,e){return Ls.or(Object.values(this.$s).map((n=>()=>n.containsKey(t,e))))}}class Tu extends Os{constructor(t){super(),this.currentSequenceNumber=t}}class Iu{constructor(t){this.persistence=t,this.Qs=new gu,this.js=null}static zs(t){return new Iu(t)}get Ws(){if(this.js)return this.js;throw os()}addReference(t,e,n){return this.Qs.addReference(n,e),this.Ws.delete(n.toString()),Ls.resolve()}removeReference(t,e,n){return this.Qs.removeReference(n,e),this.Ws.add(n.toString()),Ls.resolve()}markPotentiallyOrphaned(t,e){return this.Ws.add(e.toString()),Ls.resolve()}removeTarget(t,e){this.Qs.Is(e.targetId).forEach((t=>this.Ws.add(t.toString())));const n=this.persistence.getTargetCache();return n.getMatchingKeysForTargetId(t,e.targetId).next((t=>{t.forEach((t=>this.Ws.add(t.toString())))})).next((()=>n.removeTargetData(t,e)))}Us(){this.js=new Set}Ks(t){const e=this.persistence.getRemoteDocumentCache().newChangeBuffer();return Ls.forEach(this.Ws,(n=>{const r=Ns.fromPath(n);return this.Hs(t,r).next((t=>{t||e.removeEntry(r,Ss.min())}))})).next((()=>(this.js=null,e.apply(t))))}updateLimboDocument(t,e){return this.Hs(t,e).next((t=>{t?this.Ws.delete(e.toString()):this.Ws.add(e.toString())}))}Ls(t){return 0}Hs(t,e){return Ls.or([()=>Ls.resolve(this.Qs.containsKey(e)),()=>this.persistence.getTargetCache().containsKey(t,e),()=>this.persistence.Gs(t,e)])}}class Su{constructor(t,e,n,r){this.targetId=t,this.fromCache=e,this.Fi=n,this.Bi=r}static Li(t,e){let n=Ca(),r=Ca();for(const t of e.docChanges)switch(t.type){case 0:n=n.add(t.doc.key);break;case 1:r=r.add(t.doc.key)}return new Su(t,e.fromCache,n,r)}}class Cu{constructor(){this.qi=!1}initialize(t,e){this.Ui=t,this.indexManager=e,this.qi=!0}getDocumentsMatchingQuery(t,e,n,r){return this.Ki(t,e).next((i=>i||this.Gi(t,e,r,n))).next((n=>n||this.Qi(t,e)))}Ki(t,e){if(ta(e))return Ls.resolve(null);let n=sa(e);return this.indexManager.getIndexType(t,n).next((r=>0===r?null:(null!==e.limit&&1===r&&(e=aa(e,null,"F"),n=sa(e)),this.indexManager.getDocumentsMatchingTarget(t,n).next((r=>{const i=Ca(...r);return this.Ui.getDocuments(t,i).next((r=>this.indexManager.getMinOffset(t,n).next((n=>{const s=this.ji(e,r);return this.zi(e,s,i,n.readTime)?this.Ki(t,aa(e,null,"F")):this.Wi(t,s,e,n)}))))})))))}Gi(t,e,n,r){return ta(e)||r.isEqual(Ss.min())?this.Qi(t,e):this.Ui.getDocuments(t,n).next((i=>{const s=this.ji(e,i);return this.zi(e,s,n,r)?this.Qi(t,e):(es()<=w.DEBUG&&ns("QueryEngine","Re-using previous result from %s to execute query: %s",r.toString(),ha(e)),this.Wi(t,s,e,function(t,e){const n=t.toTimestamp().seconds,r=t.toTimestamp().nanoseconds+1,i=Ss.fromTimestamp(1e9===r?new Is(n+1,0):new Is(n,r));return new ks(i,Ns.empty(),e)}(r,-1)))}))}ji(t,e){let n=new Ks(da(t));return e.forEach(((e,r)=>{la(t,r)&&(n=n.add(r))})),n}zi(t,e,n,r){if(null===t.limit)return!1;if(n.size!==e.size)return!0;const i="F"===t.limitType?e.last():e.first();return!!i&&(i.hasPendingWrites||i.version.compareTo(r)>0)}Qi(t,e){return es()<=w.DEBUG&&ns("QueryEngine","Using full collection scan to execute query:",ha(e)),this.Ui.getDocumentsMatchingQuery(t,e,ks.min())}Wi(t,e,n,r){return this.Ui.getDocumentsMatchingQuery(t,n,r).next((t=>(e.forEach((e=>{t=t.insert(e.key,e)})),t)))}}class Au{constructor(t,e,n,r){this.persistence=t,this.Hi=e,this.serializer=r,this.Ji=new $s(Es),this.Yi=new pa((t=>Wo(t)),Yo),this.Xi=new Map,this.Zi=t.getRemoteDocumentCache(),this.Bs=t.getTargetCache(),this.qs=t.getBundleCache(),this.tr(n)}tr(t){this.documentOverlayCache=this.persistence.getDocumentOverlayCache(t),this.indexManager=this.persistence.getIndexManager(t),this.mutationQueue=this.persistence.getMutationQueue(t,this.indexManager),this.localDocuments=new du(this.Zi,this.mutationQueue,this.documentOverlayCache,this.indexManager),this.Zi.setIndexManager(this.indexManager),this.Hi.initialize(this.localDocuments,this.indexManager)}collectGarbage(t){return this.persistence.runTransaction("Collect garbage","readwrite-primary",(e=>t.collect(e,this.Ji)))}}async function _u(t,e){const n=cs(t);return await n.persistence.runTransaction("Handle user change","readonly",(t=>{let r;return n.mutationQueue.getAllMutationBatches(t).next((i=>(r=i,n.tr(e),n.mutationQueue.getAllMutationBatches(t)))).next((e=>{const i=[],s=[];let o=Ca();for(const t of r){i.push(t.batchId);for(const e of t.mutations)o=o.add(e.key)}for(const t of e){s.push(t.batchId);for(const e of t.mutations)o=o.add(e.key)}return n.localDocuments.getDocuments(t,o).next((t=>({er:t,removedBatchIds:i,addedBatchIds:s})))}))}))}function xu(t){const e=cs(t);return e.persistence.runTransaction("Get last remote snapshot version","readonly",(t=>e.Bs.getLastRemoteSnapshotVersion(t)))}function Nu(t,e){const n=cs(t);return n.persistence.runTransaction("Get next mutation batch","readonly",(t=>(void 0===e&&(e=-1),n.mutationQueue.getNextMutationBatchAfterBatchId(t,e))))}async function Du(t,e,n){const r=cs(t),i=r.Ji.get(e),s=n?"readwrite":"readwrite-primary";try{n||await r.persistence.runTransaction("Release target",s,(t=>r.persistence.referenceDelegate.removeTarget(t,i)))}catch(t){if(!Ps(t))throw t;ns("LocalStore",`Failed to update sequence numbers for target ${e}: ${t}`)}r.Ji=r.Ji.remove(e),r.Yi.delete(i.target)}function ku(t,e,n){const r=cs(t);let i=Ss.min(),s=Ca();return r.persistence.runTransaction("Execute query","readonly",(t=>function(t,e,n){const r=cs(t),i=r.Yi.get(n);return void 0!==i?Ls.resolve(r.Ji.get(i)):r.Bs.getTargetData(e,n)}(r,t,sa(e)).next((e=>{if(e)return i=e.lastLimboFreeSnapshotVersion,r.Bs.getMatchingKeysForTargetId(t,e.targetId).next((t=>{s=t}))})).next((()=>r.Hi.getDocumentsMatchingQuery(t,e,n?i:Ss.min(),n?s:Ca()))).next((t=>(function(t,e,n){let r=t.Xi.get(e)||Ss.min();n.forEach(((t,e)=>{e.readTime.compareTo(r)>0&&(r=e.readTime)})),t.Xi.set(e,r)}(r,function(t){return t.collectionGroup||(t.path.length%2==1?t.path.lastSegment():t.path.get(t.path.length-2))}(e),t),{documents:t,ir:s})))))}class Ru{constructor(){this.activeTargetIds=Aa}lr(t){this.activeTargetIds=this.activeTargetIds.add(t)}dr(t){this.activeTargetIds=this.activeTargetIds.delete(t)}hr(){const t={activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()};return JSON.stringify(t)}}class Ou{constructor(){this.Hr=new Ru,this.Jr={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}addPendingMutation(t){}updateMutationState(t,e,n){}addLocalQueryTarget(t){return this.Hr.lr(t),this.Jr[t]||"not-current"}updateQueryState(t,e,n){this.Jr[t]=e}removeLocalQueryTarget(t){this.Hr.dr(t)}isLocalQueryTarget(t){return this.Hr.activeTargetIds.has(t)}clearQueryState(t){delete this.Jr[t]}getAllActiveQueryTargets(){return this.Hr.activeTargetIds}isActiveQueryTarget(t){return this.Hr.activeTargetIds.has(t)}start(){return this.Hr=new Ru,Promise.resolve()}handleUserChange(t,e,n){}setOnlineState(t){}shutdown(){}writeSequenceNumber(t){}notifyBundleLoaded(t){}}class Mu{Yr(t){}shutdown(){}}class Lu{constructor(){this.Xr=()=>this.Zr(),this.eo=()=>this.no(),this.so=[],this.io()}Yr(t){this.so.push(t)}shutdown(){window.removeEventListener("online",this.Xr),window.removeEventListener("offline",this.eo)}io(){window.addEventListener("online",this.Xr),window.addEventListener("offline",this.eo)}Zr(){ns("ConnectivityMonitor","Network connectivity changed: AVAILABLE");for(const t of this.so)t(0)}no(){ns("ConnectivityMonitor","Network connectivity changed: UNAVAILABLE");for(const t of this.so)t(1)}static D(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener}}let Pu=null;function Fu(){return null===Pu?Pu=268435456+Math.round(2147483648*Math.random()):Pu++,"0x"+Pu.toString(16)}const Vu={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery",RunAggregationQuery:"runAggregationQuery"};class Uu{constructor(t){this.ro=t.ro,this.oo=t.oo}uo(t){this.co=t}ao(t){this.ho=t}onMessage(t){this.lo=t}close(){this.oo()}send(t){this.ro(t)}fo(){this.co()}wo(t){this.ho(t)}_o(t){this.lo(t)}}const Bu="WebChannelConnection";class ju extends class{constructor(t){this.databaseInfo=t,this.databaseId=t.databaseId;const e=t.ssl?"https":"http";this.mo=e+"://"+t.host,this.yo="projects/"+this.databaseId.projectId+"/databases/"+this.databaseId.database+"/documents"}get po(){return!1}Io(t,e,n,r,i){const s=Fu(),o=this.To(t,e);ns("RestConnection",`Sending RPC '${t}' ${s}:`,o,n);const a={};return this.Eo(a,r,i),this.Ao(t,o,a,n).then((e=>(ns("RestConnection",`Received RPC '${t}' ${s}: `,e),e)),(e=>{throw is("RestConnection",`RPC '${t}' ${s} failed with error: `,e,"url: ",o,"request:",n),e}))}vo(t,e,n,r,i,s){return this.Io(t,e,n,r,i)}Eo(t,e,n){t["X-Goog-Api-Client"]="gl-js/ fire/"+Zi,t["Content-Type"]="text/plain",this.databaseInfo.appId&&(t["X-Firebase-GMPID"]=this.databaseInfo.appId),e&&e.headers.forEach(((e,n)=>t[n]=e)),n&&n.headers.forEach(((e,n)=>t[n]=e))}To(t,e){const n=Vu[t];return`${this.mo}/v1/${e}:${n}`}}{constructor(t){super(t),this.forceLongPolling=t.forceLongPolling,this.autoDetectLongPolling=t.autoDetectLongPolling,this.useFetchStreams=t.useFetchStreams,this.longPollingOptions=t.longPollingOptions}Ao(t,e,n,r){const i=Fu();return new Promise(((s,o)=>{const a=new Qi;a.setWithCredentials(!0),a.listenOnce($i.COMPLETE,(()=>{try{switch(a.getLastErrorCode()){case qi.NO_ERROR:const e=a.getResponseJson();ns(Bu,`XHR for RPC '${t}' ${i} received:`,JSON.stringify(e)),s(e);break;case qi.TIMEOUT:ns(Bu,`RPC '${t}' ${i} timed out`),o(new hs(us.DEADLINE_EXCEEDED,"Request time out"));break;case qi.HTTP_ERROR:const n=a.getStatus();if(ns(Bu,`RPC '${t}' ${i} failed with status:`,n,"response text:",a.getResponseText()),n>0){let t=a.getResponseJson();Array.isArray(t)&&(t=t[0]);const e=null==t?void 0:t.error;if(e&&e.status&&e.message){const t=function(t){const e=t.toLowerCase().replace(/_/g,"-");return Object.values(us).indexOf(e)>=0?e:us.UNKNOWN}(e.status);o(new hs(t,e.message))}else o(new hs(us.UNKNOWN,"Server responded with status "+a.getStatus()))}else o(new hs(us.UNAVAILABLE,"Connection failed."));break;default:os()}}finally{ns(Bu,`RPC '${t}' ${i} completed.`)}}));const c=JSON.stringify(r);ns(Bu,`RPC '${t}' ${i} sending request:`,r),a.send(e,"POST",c,n,15)}))}Ro(t,e,n){const r=Fu(),i=[this.mo,"/","google.firestore.v1.Firestore","/",t,"/channel"],s=Bi(),o=ji(),a={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:`projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling},c=this.longPollingOptions.timeoutSeconds;void 0!==c&&(a.longPollingTimeout=Math.round(1e3*c)),this.useFetchStreams&&(a.xmlHttpFactory=new Ki({})),this.Eo(a.initMessageHeaders,e,n),a.encodeInitMessageHeaders=!0;const u=i.join("");ns(Bu,`Creating RPC '${t}' stream ${r}: ${u}`,a);const h=s.createWebChannel(u,a);let l=!1,d=!1;const f=new Uu({ro:e=>{d?ns(Bu,`Not sending because RPC '${t}' stream ${r} is closed:`,e):(l||(ns(Bu,`Opening RPC '${t}' stream ${r} transport.`),h.open(),l=!0),ns(Bu,`RPC '${t}' stream ${r} sending:`,e),h.send(e))},oo:()=>h.close()}),p=(t,e,n)=>{t.listen(e,(t=>{try{n(t)}catch(t){setTimeout((()=>{throw t}),0)}}))};return p(h,Hi.EventType.OPEN,(()=>{d||ns(Bu,`RPC '${t}' stream ${r} transport opened.`)})),p(h,Hi.EventType.CLOSE,(()=>{d||(d=!0,ns(Bu,`RPC '${t}' stream ${r} transport closed`),f.wo())})),p(h,Hi.EventType.ERROR,(e=>{d||(d=!0,is(Bu,`RPC '${t}' stream ${r} transport errored:`,e),f.wo(new hs(us.UNAVAILABLE,"The operation could not be completed")))})),p(h,Hi.EventType.MESSAGE,(e=>{var n;if(!d){const i=e.data[0];as(!!i);const s=i,o=s.error||(null===(n=s[0])||void 0===n?void 0:n.error);if(o){ns(Bu,`RPC '${t}' stream ${r} received error:`,o);const e=o.status;let n=function(t){const e=cc[t];if(void 0!==e)return hc(e)}(e),i=o.message;void 0===n&&(n=us.INTERNAL,i="Unknown error status: "+e+" with message "+o.message),d=!0,f.wo(new hs(n,i)),h.close()}else ns(Bu,`RPC '${t}' stream ${r} received:`,i),f._o(i)}})),p(o,zi.STAT_EVENT,(e=>{e.stat===Gi.PROXY?ns(Bu,`RPC '${t}' stream ${r} detected buffering proxy`):e.stat===Gi.NOPROXY&&ns(Bu,`RPC '${t}' stream ${r} detected no buffering proxy`)})),setTimeout((()=>{f.fo()}),0),f}}function qu(){return"undefined"!=typeof document?document:null}function $u(t){return new Dc(t,!0)}class zu{constructor(t,e,n=1e3,r=1.5,i=6e4){this.ii=t,this.timerId=e,this.Po=n,this.bo=r,this.Vo=i,this.So=0,this.Do=null,this.Co=Date.now(),this.reset()}reset(){this.So=0}xo(){this.So=this.Vo}No(t){this.cancel();const e=Math.floor(this.So+this.ko()),n=Math.max(0,Date.now()-this.Co),r=Math.max(0,e-n);r>0&&ns("ExponentialBackoff",`Backing off for ${r} ms (base delay: ${this.So} ms, delay with jitter: ${e} ms, last attempt: ${n} ms ago)`),this.Do=this.ii.enqueueAfterDelay(this.timerId,r,(()=>(this.Co=Date.now(),t()))),this.So*=this.bo,this.So<this.Po&&(this.So=this.Po),this.So>this.Vo&&(this.So=this.Vo)}Mo(){null!==this.Do&&(this.Do.skipDelay(),this.Do=null)}cancel(){null!==this.Do&&(this.Do.cancel(),this.Do=null)}ko(){return(Math.random()-.5)*this.So}}class Gu{constructor(t,e,n,r,i,s,o,a){this.ii=t,this.$o=n,this.Oo=r,this.connection=i,this.authCredentialsProvider=s,this.appCheckCredentialsProvider=o,this.listener=a,this.state=0,this.Fo=0,this.Bo=null,this.Lo=null,this.stream=null,this.qo=new zu(t,e)}Uo(){return 1===this.state||5===this.state||this.Ko()}Ko(){return 2===this.state||3===this.state}start(){4!==this.state?this.auth():this.Go()}async stop(){this.Uo()&&await this.close(0)}Qo(){this.state=0,this.qo.reset()}jo(){this.Ko()&&null===this.Bo&&(this.Bo=this.ii.enqueueAfterDelay(this.$o,6e4,(()=>this.zo())))}Wo(t){this.Ho(),this.stream.send(t)}async zo(){if(this.Ko())return this.close(0)}Ho(){this.Bo&&(this.Bo.cancel(),this.Bo=null)}Jo(){this.Lo&&(this.Lo.cancel(),this.Lo=null)}async close(t,e){this.Ho(),this.Jo(),this.qo.cancel(),this.Fo++,4!==t?this.qo.reset():e&&e.code===us.RESOURCE_EXHAUSTED?(rs(e.toString()),rs("Using maximum backoff delay to prevent overloading the backend."),this.qo.xo()):e&&e.code===us.UNAUTHENTICATED&&3!==this.state&&(this.authCredentialsProvider.invalidateToken(),this.appCheckCredentialsProvider.invalidateToken()),null!==this.stream&&(this.Yo(),this.stream.close(),this.stream=null),this.state=t,await this.listener.ao(e)}Yo(){}auth(){this.state=1;const t=this.Xo(this.Fo),e=this.Fo;Promise.all([this.authCredentialsProvider.getToken(),this.appCheckCredentialsProvider.getToken()]).then((([t,n])=>{this.Fo===e&&this.Zo(t,n)}),(e=>{t((()=>{const t=new hs(us.UNKNOWN,"Fetching auth token failed: "+e.message);return this.tu(t)}))}))}Zo(t,e){const n=this.Xo(this.Fo);this.stream=this.eu(t,e),this.stream.uo((()=>{n((()=>(this.state=2,this.Lo=this.ii.enqueueAfterDelay(this.Oo,1e4,(()=>(this.Ko()&&(this.state=3),Promise.resolve()))),this.listener.uo())))})),this.stream.ao((t=>{n((()=>this.tu(t)))})),this.stream.onMessage((t=>{n((()=>this.onMessage(t)))}))}Go(){this.state=5,this.qo.No((async()=>{this.state=0,this.start()}))}tu(t){return ns("PersistentStream",`close with error: ${t}`),this.stream=null,this.close(4,t)}Xo(t){return e=>{this.ii.enqueueAndForget((()=>this.Fo===t?e():(ns("PersistentStream","stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve())))}}}class Ku extends Gu{constructor(t,e,n,r,i,s){super(t,"listen_stream_connection_backoff","listen_stream_idle","health_check_timeout",e,n,r,s),this.serializer=i}eu(t,e){return this.connection.Ro("Listen",t,e)}onMessage(t){this.qo.reset();const e=function(t,e){let n;if("targetChange"in e){e.targetChange;const r=function(t){return"NO_CHANGE"===t?0:"ADD"===t?1:"REMOVE"===t?2:"CURRENT"===t?3:"RESET"===t?4:os()}(e.targetChange.targetChangeType||"NO_CHANGE"),i=e.targetChange.targetIds||[],s=function(t,e){return t.useProto3Json?(as(void 0===e||"string"==typeof e),Ys.fromBase64String(e||"")):(as(void 0===e||e instanceof Uint8Array),Ys.fromUint8Array(e||new Uint8Array))}(t,e.targetChange.resumeToken),o=e.targetChange.cause,a=o&&function(t){const e=void 0===t.code?us.UNKNOWN:hc(t.code);return new hs(e,t.message||"")}(o);n=new Tc(r,i,s,a||null)}else if("documentChange"in e){e.documentChange;const r=e.documentChange;r.document,r.document.name,r.document.updateTime;const i=Uc(t,r.document.name),s=Lc(r.document.updateTime),o=r.document.createTime?Lc(r.document.createTime):Ss.min(),a=new Io({mapValue:{fields:r.document.fields}}),c=Co.newFoundDocument(i,s,o,a),u=r.targetIds||[],h=r.removedTargetIds||[];n=new bc(u,h,c.key,c)}else if("documentDelete"in e){e.documentDelete;const r=e.documentDelete;r.document;const i=Uc(t,r.document),s=r.readTime?Lc(r.readTime):Ss.min(),o=Co.newNoDocument(i,s),a=r.removedTargetIds||[];n=new bc([],a,o.key,o)}else if("documentRemove"in e){e.documentRemove;const r=e.documentRemove;r.document;const i=Uc(t,r.document),s=r.removedTargetIds||[];n=new bc([],s,i,null)}else{if(!("filter"in e))return os();{e.filter;const t=e.filter;t.targetId;const{count:r=0,unchangedNames:i}=t,s=new ac(r,i),o=t.targetId;n=new Ec(o,s)}}return n}(this.serializer,t),n=function(t){if(!("targetChange"in t))return Ss.min();const e=t.targetChange;return e.targetIds&&e.targetIds.length?Ss.min():e.readTime?Lc(e.readTime):Ss.min()}(t);return this.listener.nu(e,n)}su(t){const e={};e.database=jc(this.serializer),e.addTarget=function(t,e){let n;const r=e.target;if(n=Xo(r)?{documents:zc(t,r)}:{query:Gc(t,r)},n.targetId=e.targetId,e.resumeToken.approximateByteSize()>0){n.resumeToken=Oc(t,e.resumeToken);const r=kc(t,e.expectedCount);null!==r&&(n.expectedCount=r)}else if(e.snapshotVersion.compareTo(Ss.min())>0){n.readTime=Rc(t,e.snapshotVersion.toTimestamp());const r=kc(t,e.expectedCount);null!==r&&(n.expectedCount=r)}return n}(this.serializer,t);const n=function(t,e){const n=function(t){switch(t){case"TargetPurposeListen":return null;case"TargetPurposeExistenceFilterMismatch":return"existence-filter-mismatch";case"TargetPurposeExistenceFilterMismatchBloom":return"existence-filter-mismatch-bloom";case"TargetPurposeLimboResolution":return"limbo-document";default:return os()}}(e.purpose);return null==n?null:{"goog-listen-tags":n}}(this.serializer,t);n&&(e.labels=n),this.Wo(e)}iu(t){const e={};e.database=jc(this.serializer),e.removeTarget=t,this.Wo(e)}}class Hu extends Gu{constructor(t,e,n,r,i,s){super(t,"write_stream_connection_backoff","write_stream_idle","health_check_timeout",e,n,r,s),this.serializer=i,this.ru=!1}get ou(){return this.ru}start(){this.ru=!1,this.lastStreamToken=void 0,super.start()}Yo(){this.ru&&this.uu([])}eu(t,e){return this.connection.Ro("Write",t,e)}onMessage(t){if(as(!!t.streamToken),this.lastStreamToken=t.streamToken,this.ru){this.qo.reset();const e=function(t,e){return t&&t.length>0?(as(void 0!==e),t.map((t=>function(t,e){let n=t.updateTime?Lc(t.updateTime):Lc(e);return n.isEqual(Ss.min())&&(n=Lc(e)),new qa(n,t.transformResults||[])}(t,e)))):[]}(t.writeResults,t.commitTime),n=Lc(t.commitTime);return this.listener.cu(n,e)}return as(!t.writeResults||0===t.writeResults.length),this.ru=!0,this.listener.au()}hu(){const t={};t.database=jc(this.serializer),this.Wo(t)}uu(t){const e={streamToken:this.lastStreamToken,writes:t.map((t=>function(t,e){let n;if(e instanceof Xa)n={update:$c(t,e.key,e.value)};else if(e instanceof nc)n={delete:Vc(t,e.key)};else if(e instanceof Ja)n={update:$c(t,e.key,e.data),updateMask:tu(e.fieldMask)};else{if(!(e instanceof rc))return os();n={verify:Vc(t,e.key)}}return e.fieldTransforms.length>0&&(n.updateTransforms=e.fieldTransforms.map((t=>function(t,e){const n=e.transform;if(n instanceof Ma)return{fieldPath:e.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(n instanceof La)return{fieldPath:e.field.canonicalString(),appendMissingElements:{values:n.elements}};if(n instanceof Fa)return{fieldPath:e.field.canonicalString(),removeAllFromArray:{values:n.elements}};if(n instanceof Ua)return{fieldPath:e.field.canonicalString(),increment:n.gt};throw os()}(0,t)))),e.precondition.isNone||(n.currentDocument=function(t,e){return void 0!==e.updateTime?{updateTime:Mc(t,e.updateTime)}:void 0!==e.exists?{exists:e.exists}:os()}(t,e.precondition)),n}(this.serializer,t)))};this.Wo(e)}}class Qu extends class{}{constructor(t,e,n,r){super(),this.authCredentials=t,this.appCheckCredentials=e,this.connection=n,this.serializer=r,this.lu=!1}fu(){if(this.lu)throw new hs(us.FAILED_PRECONDITION,"The client has already been terminated.")}Io(t,e,n){return this.fu(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then((([r,i])=>this.connection.Io(t,e,n,r,i))).catch((t=>{throw"FirebaseError"===t.name?(t.code===us.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),t):new hs(us.UNKNOWN,t.toString())}))}vo(t,e,n,r){return this.fu(),Promise.all([this.authCredentials.getToken(),this.appCheckCredentials.getToken()]).then((([i,s])=>this.connection.vo(t,e,n,i,s,r))).catch((t=>{throw"FirebaseError"===t.name?(t.code===us.UNAUTHENTICATED&&(this.authCredentials.invalidateToken(),this.appCheckCredentials.invalidateToken()),t):new hs(us.UNKNOWN,t.toString())}))}terminate(){this.lu=!0}}class Wu{constructor(t,e){this.asyncQueue=t,this.onlineStateHandler=e,this.state="Unknown",this.wu=0,this._u=null,this.mu=!0}gu(){0===this.wu&&(this.yu("Unknown"),this._u=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,(()=>(this._u=null,this.pu("Backend didn't respond within 10 seconds."),this.yu("Offline"),Promise.resolve()))))}Iu(t){"Online"===this.state?this.yu("Unknown"):(this.wu++,this.wu>=1&&(this.Tu(),this.pu(`Connection failed 1 times. Most recent error: ${t.toString()}`),this.yu("Offline")))}set(t){this.Tu(),this.wu=0,"Online"===t&&(this.mu=!1),this.yu(t)}yu(t){t!==this.state&&(this.state=t,this.onlineStateHandler(t))}pu(t){const e=`Could not reach Cloud Firestore backend. ${t}\nThis typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;this.mu?(rs(e),this.mu=!1):ns("OnlineStateTracker",e)}Tu(){null!==this._u&&(this._u.cancel(),this._u=null)}}class Yu{constructor(t,e,n,r,i){this.localStore=t,this.datastore=e,this.asyncQueue=n,this.remoteSyncer={},this.Eu=[],this.Au=new Map,this.vu=new Set,this.Ru=[],this.Pu=i,this.Pu.Yr((t=>{n.enqueueAndForget((async()=>{sh(this)&&(ns("RemoteStore","Restarting streams for network reachability change."),await async function(t){const e=cs(t);e.vu.add(4),await Ju(e),e.bu.set("Unknown"),e.vu.delete(4),await Xu(e)}(this))}))})),this.bu=new Wu(n,r)}}async function Xu(t){if(sh(t))for(const e of t.Ru)await e(!0)}async function Ju(t){for(const e of t.Ru)await e(!1)}function Zu(t,e){const n=cs(t);n.Au.has(e.targetId)||(n.Au.set(e.targetId,e),ih(n)?rh(n):Th(n).Ko()&&eh(n,e))}function th(t,e){const n=cs(t),r=Th(n);n.Au.delete(e),r.Ko()&&nh(n,e),0===n.Au.size&&(r.Ko()?r.jo():sh(n)&&n.bu.set("Unknown"))}function eh(t,e){if(t.Vu.qt(e.targetId),e.resumeToken.approximateByteSize()>0||e.snapshotVersion.compareTo(Ss.min())>0){const n=t.remoteSyncer.getRemoteKeysForTarget(e.targetId).size;e=e.withExpectedCount(n)}Th(t).su(e)}function nh(t,e){t.Vu.qt(e),Th(t).iu(e)}function rh(t){t.Vu=new Sc({getRemoteKeysForTarget:e=>t.remoteSyncer.getRemoteKeysForTarget(e),le:e=>t.Au.get(e)||null,ue:()=>t.datastore.serializer.databaseId}),Th(t).start(),t.bu.gu()}function ih(t){return sh(t)&&!Th(t).Uo()&&t.Au.size>0}function sh(t){return 0===cs(t).vu.size}function oh(t){t.Vu=void 0}async function ah(t){t.Au.forEach(((e,n)=>{eh(t,e)}))}async function ch(t,e){oh(t),ih(t)?(t.bu.Iu(e),rh(t)):t.bu.set("Unknown")}async function uh(t,e,n){if(t.bu.set("Online"),e instanceof Tc&&2===e.state&&e.cause)try{await async function(t,e){const n=e.cause;for(const r of e.targetIds)t.Au.has(r)&&(await t.remoteSyncer.rejectListen(r,n),t.Au.delete(r),t.Vu.removeTarget(r))}(t,e)}catch(n){ns("RemoteStore","Failed to remove targets %s: %s ",e.targetIds.join(","),n),await hh(t,n)}else if(e instanceof bc?t.Vu.Ht(e):e instanceof Ec?t.Vu.ne(e):t.Vu.Xt(e),!n.isEqual(Ss.min()))try{const e=await xu(t.localStore);n.compareTo(e)>=0&&await function(t,e){const n=t.Vu.ce(e);return n.targetChanges.forEach(((n,r)=>{if(n.resumeToken.approximateByteSize()>0){const i=t.Au.get(r);i&&t.Au.set(r,i.withResumeToken(n.resumeToken,e))}})),n.targetMismatches.forEach(((e,n)=>{const r=t.Au.get(e);if(!r)return;t.Au.set(e,r.withResumeToken(Ys.EMPTY_BYTE_STRING,r.snapshotVersion)),nh(t,e);const i=new nu(r.target,e,n,r.sequenceNumber);eh(t,i)})),t.remoteSyncer.applyRemoteEvent(n)}(t,n)}catch(e){ns("RemoteStore","Failed to raise snapshot:",e),await hh(t,e)}}async function hh(t,e,n){if(!Ps(e))throw e;t.vu.add(1),await Ju(t),t.bu.set("Offline"),n||(n=()=>xu(t.localStore)),t.asyncQueue.enqueueRetryable((async()=>{ns("RemoteStore","Retrying IndexedDB access"),await n(),t.vu.delete(1),await Xu(t)}))}function lh(t,e){return e().catch((n=>hh(t,n,e)))}async function dh(t){const e=cs(t),n=Ih(e);let r=e.Eu.length>0?e.Eu[e.Eu.length-1].batchId:-1;for(;fh(e);)try{const t=await Nu(e.localStore,r);if(null===t){0===e.Eu.length&&n.jo();break}r=t.batchId,ph(e,t)}catch(t){await hh(e,t)}gh(e)&&mh(e)}function fh(t){return sh(t)&&t.Eu.length<10}function ph(t,e){t.Eu.push(e);const n=Ih(t);n.Ko()&&n.ou&&n.uu(e.mutations)}function gh(t){return sh(t)&&!Ih(t).Uo()&&t.Eu.length>0}function mh(t){Ih(t).start()}async function yh(t){Ih(t).hu()}async function vh(t){const e=Ih(t);for(const n of t.Eu)e.uu(n.mutations)}async function wh(t,e,n){const r=t.Eu.shift(),i=sc.from(r,e,n);await lh(t,(()=>t.remoteSyncer.applySuccessfulWrite(i))),await dh(t)}async function bh(t,e){e&&Ih(t).ou&&await async function(t,e){if(function(t){switch(t){default:return os();case us.CANCELLED:case us.UNKNOWN:case us.DEADLINE_EXCEEDED:case us.RESOURCE_EXHAUSTED:case us.INTERNAL:case us.UNAVAILABLE:case us.UNAUTHENTICATED:return!1;case us.INVALID_ARGUMENT:case us.NOT_FOUND:case us.ALREADY_EXISTS:case us.PERMISSION_DENIED:case us.FAILED_PRECONDITION:case us.ABORTED:case us.OUT_OF_RANGE:case us.UNIMPLEMENTED:case us.DATA_LOSS:return!0}}(n=e.code)&&n!==us.ABORTED){const n=t.Eu.shift();Ih(t).Qo(),await lh(t,(()=>t.remoteSyncer.rejectFailedWrite(n.batchId,e))),await dh(t)}var n}(t,e),gh(t)&&mh(t)}async function Eh(t,e){const n=cs(t);n.asyncQueue.verifyOperationInProgress(),ns("RemoteStore","RemoteStore received new credentials");const r=sh(n);n.vu.add(3),await Ju(n),r&&n.bu.set("Unknown"),await n.remoteSyncer.handleCredentialChange(e),n.vu.delete(3),await Xu(n)}function Th(t){return t.Su||(t.Su=function(t,e,n){const r=cs(t);return r.fu(),new Ku(e,r.connection,r.authCredentials,r.appCheckCredentials,r.serializer,n)}(t.datastore,t.asyncQueue,{uo:ah.bind(null,t),ao:ch.bind(null,t),nu:uh.bind(null,t)}),t.Ru.push((async e=>{e?(t.Su.Qo(),ih(t)?rh(t):t.bu.set("Unknown")):(await t.Su.stop(),oh(t))}))),t.Su}function Ih(t){return t.Du||(t.Du=function(t,e,n){const r=cs(t);return r.fu(),new Hu(e,r.connection,r.authCredentials,r.appCheckCredentials,r.serializer,n)}(t.datastore,t.asyncQueue,{uo:yh.bind(null,t),ao:bh.bind(null,t),au:vh.bind(null,t),cu:wh.bind(null,t)}),t.Ru.push((async e=>{e?(t.Du.Qo(),await dh(t)):(await t.Du.stop(),t.Eu.length>0&&(ns("RemoteStore",`Stopping write stream with ${t.Eu.length} pending writes`),t.Eu=[]))}))),t.Du}class Sh{constructor(t,e,n,r,i){this.asyncQueue=t,this.timerId=e,this.targetTimeMs=n,this.op=r,this.removalCallback=i,this.deferred=new ls,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch((t=>{}))}static createAndSchedule(t,e,n,r,i){const s=Date.now()+n,o=new Sh(t,e,s,r,i);return o.start(n),o}start(t){this.timerHandle=setTimeout((()=>this.handleDelayElapsed()),t)}skipDelay(){return this.handleDelayElapsed()}cancel(t){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new hs(us.CANCELLED,"Operation cancelled"+(t?": "+t:""))))}handleDelayElapsed(){this.asyncQueue.enqueueAndForget((()=>null!==this.timerHandle?(this.clearTimeout(),this.op().then((t=>this.deferred.resolve(t)))):Promise.resolve()))}clearTimeout(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)}}function Ch(t,e){if(rs("AsyncQueue",`${e}: ${t}`),Ps(t))return new hs(us.UNAVAILABLE,`${e}: ${t}`);throw t}class Ah{constructor(t){this.comparator=t?(e,n)=>t(e,n)||Ns.comparator(e.key,n.key):(t,e)=>Ns.comparator(t.key,e.key),this.keyedMap=va(),this.sortedSet=new $s(this.comparator)}static emptySet(t){return new Ah(t.comparator)}has(t){return null!=this.keyedMap.get(t)}get(t){return this.keyedMap.get(t)}first(){return this.sortedSet.minKey()}last(){return this.sortedSet.maxKey()}isEmpty(){return this.sortedSet.isEmpty()}indexOf(t){const e=this.keyedMap.get(t);return e?this.sortedSet.indexOf(e):-1}get size(){return this.sortedSet.size}forEach(t){this.sortedSet.inorderTraversal(((e,n)=>(t(e),!1)))}add(t){const e=this.delete(t.key);return e.copy(e.keyedMap.insert(t.key,t),e.sortedSet.insert(t,null))}delete(t){const e=this.get(t);return e?this.copy(this.keyedMap.remove(t),this.sortedSet.remove(e)):this}isEqual(t){if(!(t instanceof Ah))return!1;if(this.size!==t.size)return!1;const e=this.sortedSet.getIterator(),n=t.sortedSet.getIterator();for(;e.hasNext();){const t=e.getNext().key,r=n.getNext().key;if(!t.isEqual(r))return!1}return!0}toString(){const t=[];return this.forEach((e=>{t.push(e.toString())})),0===t.length?"DocumentSet ()":"DocumentSet (\n  "+t.join("  \n")+"\n)"}copy(t,e){const n=new Ah;return n.comparator=this.comparator,n.keyedMap=t,n.sortedSet=e,n}}class _h{constructor(){this.Cu=new $s(Ns.comparator)}track(t){const e=t.doc.key,n=this.Cu.get(e);n?0!==t.type&&3===n.type?this.Cu=this.Cu.insert(e,t):3===t.type&&1!==n.type?this.Cu=this.Cu.insert(e,{type:n.type,doc:t.doc}):2===t.type&&2===n.type?this.Cu=this.Cu.insert(e,{type:2,doc:t.doc}):2===t.type&&0===n.type?this.Cu=this.Cu.insert(e,{type:0,doc:t.doc}):1===t.type&&0===n.type?this.Cu=this.Cu.remove(e):1===t.type&&2===n.type?this.Cu=this.Cu.insert(e,{type:1,doc:n.doc}):0===t.type&&1===n.type?this.Cu=this.Cu.insert(e,{type:2,doc:t.doc}):os():this.Cu=this.Cu.insert(e,t)}xu(){const t=[];return this.Cu.inorderTraversal(((e,n)=>{t.push(n)})),t}}class xh{constructor(t,e,n,r,i,s,o,a,c){this.query=t,this.docs=e,this.oldDocs=n,this.docChanges=r,this.mutatedKeys=i,this.fromCache=s,this.syncStateChanged=o,this.excludesMetadataChanges=a,this.hasCachedResults=c}static fromInitialDocuments(t,e,n,r,i){const s=[];return e.forEach((t=>{s.push({type:0,doc:t})})),new xh(t,e,Ah.emptySet(e),s,n,r,!0,!1,i)}get hasPendingWrites(){return!this.mutatedKeys.isEmpty()}isEqual(t){if(!(this.fromCache===t.fromCache&&this.hasCachedResults===t.hasCachedResults&&this.syncStateChanged===t.syncStateChanged&&this.mutatedKeys.isEqual(t.mutatedKeys)&&ca(this.query,t.query)&&this.docs.isEqual(t.docs)&&this.oldDocs.isEqual(t.oldDocs)))return!1;const e=this.docChanges,n=t.docChanges;if(e.length!==n.length)return!1;for(let t=0;t<e.length;t++)if(e[t].type!==n[t].type||!e[t].doc.isEqual(n[t].doc))return!1;return!0}}class Nh{constructor(){this.Nu=void 0,this.listeners=[]}}class Dh{constructor(){this.queries=new pa((t=>ua(t)),ca),this.onlineState="Unknown",this.ku=new Set}}async function kh(t,e){const n=cs(t),r=e.query;let i=!1,s=n.queries.get(r);if(s||(i=!0,s=new Nh),i)try{s.Nu=await n.onListen(r)}catch(t){const n=Ch(t,`Initialization of query '${ha(e.query)}' failed`);return void e.onError(n)}n.queries.set(r,s),s.listeners.push(e),e.Mu(n.onlineState),s.Nu&&e.$u(s.Nu)&&Lh(n)}async function Rh(t,e){const n=cs(t),r=e.query;let i=!1;const s=n.queries.get(r);if(s){const t=s.listeners.indexOf(e);t>=0&&(s.listeners.splice(t,1),i=0===s.listeners.length)}if(i)return n.queries.delete(r),n.onUnlisten(r)}function Oh(t,e){const n=cs(t);let r=!1;for(const t of e){const e=t.query,i=n.queries.get(e);if(i){for(const e of i.listeners)e.$u(t)&&(r=!0);i.Nu=t}}r&&Lh(n)}function Mh(t,e,n){const r=cs(t),i=r.queries.get(e);if(i)for(const t of i.listeners)t.onError(n);r.queries.delete(e)}function Lh(t){t.ku.forEach((t=>{t.next()}))}class Ph{constructor(t,e,n){this.query=t,this.Ou=e,this.Fu=!1,this.Bu=null,this.onlineState="Unknown",this.options=n||{}}$u(t){if(!this.options.includeMetadataChanges){const e=[];for(const n of t.docChanges)3!==n.type&&e.push(n);t=new xh(t.query,t.docs,t.oldDocs,e,t.mutatedKeys,t.fromCache,t.syncStateChanged,!0,t.hasCachedResults)}let e=!1;return this.Fu?this.Lu(t)&&(this.Ou.next(t),e=!0):this.qu(t,this.onlineState)&&(this.Uu(t),e=!0),this.Bu=t,e}onError(t){this.Ou.error(t)}Mu(t){this.onlineState=t;let e=!1;return this.Bu&&!this.Fu&&this.qu(this.Bu,t)&&(this.Uu(this.Bu),e=!0),e}qu(t,e){if(!t.fromCache)return!0;const n="Offline"!==e;return(!this.options.Ku||!n)&&(!t.docs.isEmpty()||t.hasCachedResults||"Offline"===e)}Lu(t){if(t.docChanges.length>0)return!0;const e=this.Bu&&this.Bu.hasPendingWrites!==t.hasPendingWrites;return!(!t.syncStateChanged&&!e)&&!0===this.options.includeMetadataChanges}Uu(t){t=xh.fromInitialDocuments(t.query,t.docs,t.mutatedKeys,t.fromCache,t.hasCachedResults),this.Fu=!0,this.Ou.next(t)}}class Fh{constructor(t){this.key=t}}class Vh{constructor(t){this.key=t}}class Uh{constructor(t,e){this.query=t,this.Yu=e,this.Xu=null,this.hasCachedResults=!1,this.current=!1,this.Zu=Ca(),this.mutatedKeys=Ca(),this.tc=da(t),this.ec=new Ah(this.tc)}get nc(){return this.Yu}sc(t,e){const n=e?e.ic:new _h,r=e?e.ec:this.ec;let i=e?e.mutatedKeys:this.mutatedKeys,s=r,o=!1;const a="F"===this.query.limitType&&r.size===this.query.limit?r.last():null,c="L"===this.query.limitType&&r.size===this.query.limit?r.first():null;if(t.inorderTraversal(((t,e)=>{const u=r.get(t),h=la(this.query,e)?e:null,l=!!u&&this.mutatedKeys.has(u.key),d=!!h&&(h.hasLocalMutations||this.mutatedKeys.has(h.key)&&h.hasCommittedMutations);let f=!1;u&&h?u.data.isEqual(h.data)?l!==d&&(n.track({type:3,doc:h}),f=!0):this.rc(u,h)||(n.track({type:2,doc:h}),f=!0,(a&&this.tc(h,a)>0||c&&this.tc(h,c)<0)&&(o=!0)):!u&&h?(n.track({type:0,doc:h}),f=!0):u&&!h&&(n.track({type:1,doc:u}),f=!0,(a||c)&&(o=!0)),f&&(h?(s=s.add(h),i=d?i.add(t):i.delete(t)):(s=s.delete(t),i=i.delete(t)))})),null!==this.query.limit)for(;s.size>this.query.limit;){const t="F"===this.query.limitType?s.last():s.first();s=s.delete(t.key),i=i.delete(t.key),n.track({type:1,doc:t})}return{ec:s,ic:n,zi:o,mutatedKeys:i}}rc(t,e){return t.hasLocalMutations&&e.hasCommittedMutations&&!e.hasLocalMutations}applyChanges(t,e,n){const r=this.ec;this.ec=t.ec,this.mutatedKeys=t.mutatedKeys;const i=t.ic.xu();i.sort(((t,e)=>function(t,e){const n=t=>{switch(t){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return os()}};return n(t)-n(e)}(t.type,e.type)||this.tc(t.doc,e.doc))),this.oc(n);const s=e?this.uc():[],o=0===this.Zu.size&&this.current?1:0,a=o!==this.Xu;return this.Xu=o,0!==i.length||a?{snapshot:new xh(this.query,t.ec,r,i,t.mutatedKeys,0===o,a,!1,!!n&&n.resumeToken.approximateByteSize()>0),cc:s}:{cc:s}}Mu(t){return this.current&&"Offline"===t?(this.current=!1,this.applyChanges({ec:this.ec,ic:new _h,mutatedKeys:this.mutatedKeys,zi:!1},!1)):{cc:[]}}ac(t){return!this.Yu.has(t)&&!!this.ec.has(t)&&!this.ec.get(t).hasLocalMutations}oc(t){t&&(t.addedDocuments.forEach((t=>this.Yu=this.Yu.add(t))),t.modifiedDocuments.forEach((t=>{})),t.removedDocuments.forEach((t=>this.Yu=this.Yu.delete(t))),this.current=t.current)}uc(){if(!this.current)return[];const t=this.Zu;this.Zu=Ca(),this.ec.forEach((t=>{this.ac(t.key)&&(this.Zu=this.Zu.add(t.key))}));const e=[];return t.forEach((t=>{this.Zu.has(t)||e.push(new Vh(t))})),this.Zu.forEach((n=>{t.has(n)||e.push(new Fh(n))})),e}hc(t){this.Yu=t.ir,this.Zu=Ca();const e=this.sc(t.documents);return this.applyChanges(e,!0)}lc(){return xh.fromInitialDocuments(this.query,this.ec,this.mutatedKeys,0===this.Xu,this.hasCachedResults)}}class Bh{constructor(t,e,n){this.query=t,this.targetId=e,this.view=n}}class jh{constructor(t){this.key=t,this.fc=!1}}class qh{constructor(t,e,n,r,i,s){this.localStore=t,this.remoteStore=e,this.eventManager=n,this.sharedClientState=r,this.currentUser=i,this.maxConcurrentLimboResolutions=s,this.dc={},this.wc=new pa((t=>ua(t)),ca),this._c=new Map,this.mc=new Set,this.gc=new $s(Ns.comparator),this.yc=new Map,this.Ic=new gu,this.Tc={},this.Ec=new Map,this.Ac=uu.Mn(),this.onlineState="Unknown",this.vc=void 0}get isPrimaryClient(){return!0===this.vc}}async function $h(t,e){const n=function(t){const e=cs(t);return e.remoteStore.remoteSyncer.applyRemoteEvent=Gh.bind(null,e),e.remoteStore.remoteSyncer.getRemoteKeysForTarget=sl.bind(null,e),e.remoteStore.remoteSyncer.rejectListen=Hh.bind(null,e),e.dc.nu=Oh.bind(null,e.eventManager),e.dc.Pc=Mh.bind(null,e.eventManager),e}(t);let r,i;const s=n.wc.get(e);if(s)r=s.targetId,n.sharedClientState.addLocalQueryTarget(r),i=s.view.lc();else{const t=await function(t,e){const n=cs(t);return n.persistence.runTransaction("Allocate target","readwrite",(t=>{let r;return n.Bs.getTargetData(t,e).next((i=>i?(r=i,Ls.resolve(r)):n.Bs.allocateTargetId(t).next((i=>(r=new nu(e,i,"TargetPurposeListen",t.currentSequenceNumber),n.Bs.addTargetData(t,r).next((()=>r)))))))})).then((t=>{const r=n.Ji.get(t.targetId);return(null===r||t.snapshotVersion.compareTo(r.snapshotVersion)>0)&&(n.Ji=n.Ji.insert(t.targetId,t),n.Yi.set(e,t.targetId)),t}))}(n.localStore,sa(e)),s=n.sharedClientState.addLocalQueryTarget(t.targetId);r=t.targetId,i=await async function(t,e,n,r,i){t.Rc=(e,n,r)=>async function(t,e,n,r){let i=e.view.sc(n);i.zi&&(i=await ku(t.localStore,e.query,!1).then((({documents:t})=>e.view.sc(t,i))));const s=r&&r.targetChanges.get(e.targetId),o=e.view.applyChanges(i,t.isPrimaryClient,s);return tl(t,e.targetId,o.cc),o.snapshot}(t,e,n,r);const s=await ku(t.localStore,e,!0),o=new Uh(e,s.ir),a=o.sc(s.documents),c=wc.createSynthesizedTargetChangeForCurrentChange(n,r&&"Offline"!==t.onlineState,i),u=o.applyChanges(a,t.isPrimaryClient,c);tl(t,n,u.cc);const h=new Bh(e,n,o);return t.wc.set(e,h),t._c.has(n)?t._c.get(n).push(e):t._c.set(n,[e]),u.snapshot}(n,e,r,"current"===s,t.resumeToken),n.isPrimaryClient&&Zu(n.remoteStore,t)}return i}async function zh(t,e){const n=cs(t),r=n.wc.get(e),i=n._c.get(r.targetId);if(i.length>1)return n._c.set(r.targetId,i.filter((t=>!ca(t,e)))),void n.wc.delete(e);n.isPrimaryClient?(n.sharedClientState.removeLocalQueryTarget(r.targetId),n.sharedClientState.isActiveQueryTarget(r.targetId)||await Du(n.localStore,r.targetId,!1).then((()=>{n.sharedClientState.clearQueryState(r.targetId),th(n.remoteStore,r.targetId),Jh(n,r.targetId)})).catch(Ms)):(Jh(n,r.targetId),await Du(n.localStore,r.targetId,!0))}async function Gh(t,e){const n=cs(t);try{const t=await function(t,e){const n=cs(t),r=e.snapshotVersion;let i=n.Ji;return n.persistence.runTransaction("Apply remote event","readwrite-primary",(t=>{const s=n.Zi.newChangeBuffer({trackRemovals:!0});i=n.Ji;const o=[];e.targetChanges.forEach(((s,a)=>{const c=i.get(a);if(!c)return;o.push(n.Bs.removeMatchingKeys(t,s.removedDocuments,a).next((()=>n.Bs.addMatchingKeys(t,s.addedDocuments,a))));let u=c.withSequenceNumber(t.currentSequenceNumber);null!==e.targetMismatches.get(a)?u=u.withResumeToken(Ys.EMPTY_BYTE_STRING,Ss.min()).withLastLimboFreeSnapshotVersion(Ss.min()):s.resumeToken.approximateByteSize()>0&&(u=u.withResumeToken(s.resumeToken,r)),i=i.insert(a,u),function(t,e,n){return 0===t.resumeToken.approximateByteSize()||e.snapshotVersion.toMicroseconds()-t.snapshotVersion.toMicroseconds()>=3e8||n.addedDocuments.size+n.modifiedDocuments.size+n.removedDocuments.size>0}(c,u,s)&&o.push(n.Bs.updateTargetData(t,u))}));let a=ma(),c=Ca();if(e.documentUpdates.forEach((r=>{e.resolvedLimboDocuments.has(r)&&o.push(n.persistence.referenceDelegate.updateLimboDocument(t,r))})),o.push(function(t,e,n){let r=Ca(),i=Ca();return n.forEach((t=>r=r.add(t))),e.getEntries(t,r).next((t=>{let r=ma();return n.forEach(((n,s)=>{const o=t.get(n);s.isFoundDocument()!==o.isFoundDocument()&&(i=i.add(n)),s.isNoDocument()&&s.version.isEqual(Ss.min())?(e.removeEntry(n,s.readTime),r=r.insert(n,s)):!o.isValidDocument()||s.version.compareTo(o.version)>0||0===s.version.compareTo(o.version)&&o.hasPendingWrites?(e.addEntry(s),r=r.insert(n,s)):ns("LocalStore","Ignoring outdated watch update for ",n,". Current version:",o.version," Watch version:",s.version)})),{nr:r,sr:i}}))}(t,s,e.documentUpdates).next((t=>{a=t.nr,c=t.sr}))),!r.isEqual(Ss.min())){const e=n.Bs.getLastRemoteSnapshotVersion(t).next((e=>n.Bs.setTargetsMetadata(t,t.currentSequenceNumber,r)));o.push(e)}return Ls.waitFor(o).next((()=>s.apply(t))).next((()=>n.localDocuments.getLocalViewOfDocuments(t,a,c))).next((()=>a))})).then((t=>(n.Ji=i,t)))}(n.localStore,e);e.targetChanges.forEach(((t,e)=>{const r=n.yc.get(e);r&&(as(t.addedDocuments.size+t.modifiedDocuments.size+t.removedDocuments.size<=1),t.addedDocuments.size>0?r.fc=!0:t.modifiedDocuments.size>0?as(r.fc):t.removedDocuments.size>0&&(as(r.fc),r.fc=!1))})),await rl(n,t,e)}catch(t){await Ms(t)}}function Kh(t,e,n){const r=cs(t);if(r.isPrimaryClient&&0===n||!r.isPrimaryClient&&1===n){const t=[];r.wc.forEach(((n,r)=>{const i=r.view.Mu(e);i.snapshot&&t.push(i.snapshot)})),function(t,e){const n=cs(t);n.onlineState=e;let r=!1;n.queries.forEach(((t,n)=>{for(const t of n.listeners)t.Mu(e)&&(r=!0)})),r&&Lh(n)}(r.eventManager,e),t.length&&r.dc.nu(t),r.onlineState=e,r.isPrimaryClient&&r.sharedClientState.setOnlineState(e)}}async function Hh(t,e,n){const r=cs(t);r.sharedClientState.updateQueryState(e,"rejected",n);const i=r.yc.get(e),s=i&&i.key;if(s){let t=new $s(Ns.comparator);t=t.insert(s,Co.newNoDocument(s,Ss.min()));const n=Ca().add(s),i=new vc(Ss.min(),new Map,new $s(Es),t,n);await Gh(r,i),r.gc=r.gc.remove(s),r.yc.delete(e),nl(r)}else await Du(r.localStore,e,!1).then((()=>Jh(r,e,n))).catch(Ms)}async function Qh(t,e){const n=cs(t),r=e.batch.batchId;try{const t=await function(t,e){const n=cs(t);return n.persistence.runTransaction("Acknowledge batch","readwrite-primary",(t=>{const r=e.batch.keys(),i=n.Zi.newChangeBuffer({trackRemovals:!0});return function(t,e,n,r){const i=n.batch,s=i.keys();let o=Ls.resolve();return s.forEach((t=>{o=o.next((()=>r.getEntry(e,t))).next((e=>{const s=n.docVersions.get(t);as(null!==s),e.version.compareTo(s)<0&&(i.applyToRemoteDocument(e,n),e.isValidDocument()&&(e.setReadTime(n.commitVersion),r.addEntry(e)))}))})),o.next((()=>t.mutationQueue.removeMutationBatch(e,i)))}(n,t,e,i).next((()=>i.apply(t))).next((()=>n.mutationQueue.performConsistencyCheck(t))).next((()=>n.documentOverlayCache.removeOverlaysForBatchId(t,r,e.batch.batchId))).next((()=>n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(t,function(t){let e=Ca();for(let n=0;n<t.mutationResults.length;++n)t.mutationResults[n].transformResults.length>0&&(e=e.add(t.batch.mutations[n].key));return e}(e)))).next((()=>n.localDocuments.getDocuments(t,r)))}))}(n.localStore,e);Xh(n,r,null),Yh(n,r),n.sharedClientState.updateMutationState(r,"acknowledged"),await rl(n,t)}catch(t){await Ms(t)}}async function Wh(t,e,n){const r=cs(t);try{const t=await function(t,e){const n=cs(t);return n.persistence.runTransaction("Reject batch","readwrite-primary",(t=>{let r;return n.mutationQueue.lookupMutationBatch(t,e).next((e=>(as(null!==e),r=e.keys(),n.mutationQueue.removeMutationBatch(t,e)))).next((()=>n.mutationQueue.performConsistencyCheck(t))).next((()=>n.documentOverlayCache.removeOverlaysForBatchId(t,r,e))).next((()=>n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(t,r))).next((()=>n.localDocuments.getDocuments(t,r)))}))}(r.localStore,e);Xh(r,e,n),Yh(r,e),r.sharedClientState.updateMutationState(e,"rejected",n),await rl(r,t)}catch(n){await Ms(n)}}function Yh(t,e){(t.Ec.get(e)||[]).forEach((t=>{t.resolve()})),t.Ec.delete(e)}function Xh(t,e,n){const r=cs(t);let i=r.Tc[r.currentUser.toKey()];if(i){const t=i.get(e);t&&(n?t.reject(n):t.resolve(),i=i.remove(e)),r.Tc[r.currentUser.toKey()]=i}}function Jh(t,e,n=null){t.sharedClientState.removeLocalQueryTarget(e);for(const r of t._c.get(e))t.wc.delete(r),n&&t.dc.Pc(r,n);t._c.delete(e),t.isPrimaryClient&&t.Ic.Is(e).forEach((e=>{t.Ic.containsKey(e)||Zh(t,e)}))}function Zh(t,e){t.mc.delete(e.path.canonicalString());const n=t.gc.get(e);null!==n&&(th(t.remoteStore,n),t.gc=t.gc.remove(e),t.yc.delete(n),nl(t))}function tl(t,e,n){for(const r of n)r instanceof Fh?(t.Ic.addReference(r.key,e),el(t,r)):r instanceof Vh?(ns("SyncEngine","Document no longer in limbo: "+r.key),t.Ic.removeReference(r.key,e),t.Ic.containsKey(r.key)||Zh(t,r.key)):os()}function el(t,e){const n=e.key,r=n.path.canonicalString();t.gc.get(n)||t.mc.has(r)||(ns("SyncEngine","New document in limbo: "+n),t.mc.add(r),nl(t))}function nl(t){for(;t.mc.size>0&&t.gc.size<t.maxConcurrentLimboResolutions;){const e=t.mc.values().next().value;t.mc.delete(e);const n=new Ns(As.fromString(e)),r=t.Ac.next();t.yc.set(r,new jh(n)),t.gc=t.gc.insert(n,r),Zu(t.remoteStore,new nu(sa(Zo(n.path)),r,"TargetPurposeLimboResolution",Fs.ct))}}async function rl(t,e,n){const r=cs(t),i=[],s=[],o=[];r.wc.isEmpty()||(r.wc.forEach(((t,a)=>{o.push(r.Rc(a,e,n).then((t=>{if((t||n)&&r.isPrimaryClient&&r.sharedClientState.updateQueryState(a.targetId,(null==t?void 0:t.fromCache)?"not-current":"current"),t){i.push(t);const e=Su.Li(a.targetId,t);s.push(e)}})))})),await Promise.all(o),r.dc.nu(i),await async function(t,e){const n=cs(t);try{await n.persistence.runTransaction("notifyLocalViewChanges","readwrite",(t=>Ls.forEach(e,(e=>Ls.forEach(e.Fi,(r=>n.persistence.referenceDelegate.addReference(t,e.targetId,r))).next((()=>Ls.forEach(e.Bi,(r=>n.persistence.referenceDelegate.removeReference(t,e.targetId,r)))))))))}catch(t){if(!Ps(t))throw t;ns("LocalStore","Failed to update sequence numbers: "+t)}for(const t of e){const e=t.targetId;if(!t.fromCache){const t=n.Ji.get(e),r=t.snapshotVersion,i=t.withLastLimboFreeSnapshotVersion(r);n.Ji=n.Ji.insert(e,i)}}}(r.localStore,s))}async function il(t,e){const n=cs(t);if(!n.currentUser.isEqual(e)){ns("SyncEngine","User change. New user:",e.toKey());const t=await _u(n.localStore,e);n.currentUser=e,function(t){t.Ec.forEach((t=>{t.forEach((t=>{t.reject(new hs(us.CANCELLED,"'waitForPendingWrites' promise is rejected due to a user change."))}))})),t.Ec.clear()}(n),n.sharedClientState.handleUserChange(e,t.removedBatchIds,t.addedBatchIds),await rl(n,t.er)}}function sl(t,e){const n=cs(t),r=n.yc.get(e);if(r&&r.fc)return Ca().add(r.key);{let t=Ca();const r=n._c.get(e);if(!r)return t;for(const e of r){const r=n.wc.get(e);t=t.unionWith(r.view.nc)}return t}}function ol(t){const e=cs(t);return e.remoteStore.remoteSyncer.applySuccessfulWrite=Qh.bind(null,e),e.remoteStore.remoteSyncer.rejectFailedWrite=Wh.bind(null,e),e}class al{constructor(){this.synchronizeTabs=!1}async initialize(t){this.serializer=$u(t.databaseInfo.databaseId),this.sharedClientState=this.createSharedClientState(t),this.persistence=this.createPersistence(t),await this.persistence.start(),this.localStore=this.createLocalStore(t),this.gcScheduler=this.createGarbageCollectionScheduler(t,this.localStore),this.indexBackfillerScheduler=this.createIndexBackfillerScheduler(t,this.localStore)}createGarbageCollectionScheduler(t,e){return null}createIndexBackfillerScheduler(t,e){return null}createLocalStore(t){return function(t,e,n,r){return new Au(t,e,n,r)}(this.persistence,new Cu,t.initialUser,this.serializer)}createPersistence(t){return new Eu(Iu.zs,this.serializer)}createSharedClientState(t){return new Ou}async terminate(){this.gcScheduler&&this.gcScheduler.stop(),await this.sharedClientState.shutdown(),await this.persistence.shutdown()}}class cl{async initialize(t,e){this.localStore||(this.localStore=t.localStore,this.sharedClientState=t.sharedClientState,this.datastore=this.createDatastore(e),this.remoteStore=this.createRemoteStore(e),this.eventManager=this.createEventManager(e),this.syncEngine=this.createSyncEngine(e,!t.synchronizeTabs),this.sharedClientState.onlineStateHandler=t=>Kh(this.syncEngine,t,1),this.remoteStore.remoteSyncer.handleCredentialChange=il.bind(null,this.syncEngine),await async function(t,e){const n=cs(t);e?(n.vu.delete(2),await Xu(n)):e||(n.vu.add(2),await Ju(n),n.bu.set("Unknown"))}(this.remoteStore,this.syncEngine.isPrimaryClient))}createEventManager(t){return new Dh}createDatastore(t){const e=$u(t.databaseInfo.databaseId),n=(r=t.databaseInfo,new ju(r));var r;return function(t,e,n,r){return new Qu(t,e,n,r)}(t.authCredentials,t.appCheckCredentials,n,e)}createRemoteStore(t){return e=this.localStore,n=this.datastore,r=t.asyncQueue,i=t=>Kh(this.syncEngine,t,0),s=Lu.D()?new Lu:new Mu,new Yu(e,n,r,i,s);var e,n,r,i,s}createSyncEngine(t,e){return function(t,e,n,r,i,s,o){const a=new qh(t,e,n,r,i,s);return o&&(a.vc=!0),a}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,t.initialUser,t.maxConcurrentLimboResolutions,e)}terminate(){return async function(t){const e=cs(t);ns("RemoteStore","RemoteStore shutting down."),e.vu.add(5),await Ju(e),e.Pu.shutdown(),e.bu.set("Unknown")}(this.remoteStore)}}class ul{constructor(t){this.observer=t,this.muted=!1}next(t){this.observer.next&&this.Sc(this.observer.next,t)}error(t){this.observer.error?this.Sc(this.observer.error,t):rs("Uncaught Error in snapshot listener:",t.toString())}Dc(){this.muted=!0}Sc(t,e){this.muted||setTimeout((()=>{this.muted||t(e)}),0)}}class hl{constructor(t,e,n,r){this.authCredentials=t,this.appCheckCredentials=e,this.asyncQueue=n,this.databaseInfo=r,this.user=Ji.UNAUTHENTICATED,this.clientId=bs.A(),this.authCredentialListener=()=>Promise.resolve(),this.appCheckCredentialListener=()=>Promise.resolve(),this.authCredentials.start(n,(async t=>{ns("FirestoreClient","Received user=",t.uid),await this.authCredentialListener(t),this.user=t})),this.appCheckCredentials.start(n,(t=>(ns("FirestoreClient","Received new app check token=",t),this.appCheckCredentialListener(t,this.user))))}async getConfiguration(){return{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,authCredentials:this.authCredentials,appCheckCredentials:this.appCheckCredentials,initialUser:this.user,maxConcurrentLimboResolutions:100}}setCredentialChangeListener(t){this.authCredentialListener=t}setAppCheckTokenChangeListener(t){this.appCheckCredentialListener=t}verifyNotTerminated(){if(this.asyncQueue.isShuttingDown)throw new hs(us.FAILED_PRECONDITION,"The client has already been terminated.")}terminate(){this.asyncQueue.enterRestrictedMode();const t=new ls;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted((async()=>{try{this._onlineComponents&&await this._onlineComponents.terminate(),this._offlineComponents&&await this._offlineComponents.terminate(),this.authCredentials.shutdown(),this.appCheckCredentials.shutdown(),t.resolve()}catch(e){const n=Ch(e,"Failed to shutdown persistence");t.reject(n)}})),t.promise}}async function ll(t,e){t.asyncQueue.verifyOperationInProgress(),ns("FirestoreClient","Initializing OfflineComponentProvider");const n=await t.getConfiguration();await e.initialize(n);let r=n.initialUser;t.setCredentialChangeListener((async t=>{r.isEqual(t)||(await _u(e.localStore,t),r=t)})),e.persistence.setDatabaseDeletedListener((()=>t.terminate())),t._offlineComponents=e}async function dl(t,e){t.asyncQueue.verifyOperationInProgress();const n=await async function(t){if(!t._offlineComponents)if(t._uninitializedComponentsProvider){ns("FirestoreClient","Using user provided OfflineComponentProvider");try{await ll(t,t._uninitializedComponentsProvider._offline)}catch(e){const n=e;if(!function(t){return"FirebaseError"===t.name?t.code===us.FAILED_PRECONDITION||t.code===us.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&t instanceof DOMException)||22===t.code||20===t.code||11===t.code}(n))throw n;is("Error using user provided cache. Falling back to memory cache: "+n),await ll(t,new al)}}else ns("FirestoreClient","Using default OfflineComponentProvider"),await ll(t,new al);return t._offlineComponents}(t);ns("FirestoreClient","Initializing OnlineComponentProvider");const r=await t.getConfiguration();await e.initialize(n,r),t.setCredentialChangeListener((t=>Eh(e.remoteStore,t))),t.setAppCheckTokenChangeListener(((t,n)=>Eh(e.remoteStore,n))),t._onlineComponents=e}async function fl(t){return t._onlineComponents||(t._uninitializedComponentsProvider?(ns("FirestoreClient","Using user provided OnlineComponentProvider"),await dl(t,t._uninitializedComponentsProvider._online)):(ns("FirestoreClient","Using default OnlineComponentProvider"),await dl(t,new cl))),t._onlineComponents}async function pl(t){const e=await fl(t),n=e.eventManager;return n.onListen=$h.bind(null,e.syncEngine),n.onUnlisten=zh.bind(null,e.syncEngine),n}function gl(t){const e={};return void 0!==t.timeoutSeconds&&(e.timeoutSeconds=t.timeoutSeconds),e}const ml=new Map;function yl(t,e,n){if(!n)throw new hs(us.INVALID_ARGUMENT,`Function ${t}() cannot be called with an empty ${e}.`)}function vl(t){if(!Ns.isDocumentKey(t))throw new hs(us.INVALID_ARGUMENT,`Invalid document reference. Document references must have an even number of segments, but ${t} has ${t.length}.`)}function wl(t){if(Ns.isDocumentKey(t))throw new hs(us.INVALID_ARGUMENT,`Invalid collection reference. Collection references must have an odd number of segments, but ${t} has ${t.length}.`)}function bl(t){if(void 0===t)return"undefined";if(null===t)return"null";if("string"==typeof t)return t.length>20&&(t=`${t.substring(0,20)}...`),JSON.stringify(t);if("number"==typeof t||"boolean"==typeof t)return""+t;if("object"==typeof t){if(t instanceof Array)return"an array";{const e=function(t){return t.constructor?t.constructor.name:null}(t);return e?`a custom ${e} object`:"an object"}}return"function"==typeof t?"a function":os()}function El(t,e){if("_delegate"in t&&(t=t._delegate),!(t instanceof e)){if(e.name===t.constructor.name)throw new hs(us.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");{const n=bl(t);throw new hs(us.INVALID_ARGUMENT,`Expected type '${e.name}', but it was: ${n}`)}}return t}class Tl{constructor(t){var e,n;if(void 0===t.host){if(void 0!==t.ssl)throw new hs(us.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host="firestore.googleapis.com",this.ssl=!0}else this.host=t.host,this.ssl=null===(e=t.ssl)||void 0===e||e;if(this.credentials=t.credentials,this.ignoreUndefinedProperties=!!t.ignoreUndefinedProperties,this.cache=t.localCache,void 0===t.cacheSizeBytes)this.cacheSizeBytes=41943040;else{if(-1!==t.cacheSizeBytes&&t.cacheSizeBytes<1048576)throw new hs(us.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=t.cacheSizeBytes}(function(t,e,n,r){if(!0===e&&!0===r)throw new hs(us.INVALID_ARGUMENT,"experimentalForceLongPolling and experimentalAutoDetectLongPolling cannot be used together.")})(0,t.experimentalForceLongPolling,0,t.experimentalAutoDetectLongPolling),this.experimentalForceLongPolling=!!t.experimentalForceLongPolling,this.experimentalForceLongPolling?this.experimentalAutoDetectLongPolling=!1:void 0===t.experimentalAutoDetectLongPolling?this.experimentalAutoDetectLongPolling=!0:this.experimentalAutoDetectLongPolling=!!t.experimentalAutoDetectLongPolling,this.experimentalLongPollingOptions=gl(null!==(n=t.experimentalLongPollingOptions)&&void 0!==n?n:{}),function(t){if(void 0!==t.timeoutSeconds){if(isNaN(t.timeoutSeconds))throw new hs(us.INVALID_ARGUMENT,`invalid long polling timeout: ${t.timeoutSeconds} (must not be NaN)`);if(t.timeoutSeconds<5)throw new hs(us.INVALID_ARGUMENT,`invalid long polling timeout: ${t.timeoutSeconds} (minimum allowed value is 5)`);if(t.timeoutSeconds>30)throw new hs(us.INVALID_ARGUMENT,`invalid long polling timeout: ${t.timeoutSeconds} (maximum allowed value is 30)`)}}(this.experimentalLongPollingOptions),this.useFetchStreams=!!t.useFetchStreams}isEqual(t){return this.host===t.host&&this.ssl===t.ssl&&this.credentials===t.credentials&&this.cacheSizeBytes===t.cacheSizeBytes&&this.experimentalForceLongPolling===t.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===t.experimentalAutoDetectLongPolling&&(e=this.experimentalLongPollingOptions,n=t.experimentalLongPollingOptions,e.timeoutSeconds===n.timeoutSeconds)&&this.ignoreUndefinedProperties===t.ignoreUndefinedProperties&&this.useFetchStreams===t.useFetchStreams;var e,n}}class Il{constructor(t,e,n,r){this._authCredentials=t,this._appCheckCredentials=e,this._databaseId=n,this._app=r,this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new Tl({}),this._settingsFrozen=!1}get app(){if(!this._app)throw new hs(us.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app}get _initialized(){return this._settingsFrozen}get _terminated(){return void 0!==this._terminateTask}_setSettings(t){if(this._settingsFrozen)throw new hs(us.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new Tl(t),void 0!==t.credentials&&(this._authCredentials=function(t){if(!t)return new fs;switch(t.type){case"firstParty":return new ms(t.sessionIndex||"0",t.iamToken||null,t.authTokenFactory||null);case"provider":return t.client;default:throw new hs(us.INVALID_ARGUMENT,"makeAuthCredentialsProvider failed due to invalid credential type")}}(t.credentials))}_getSettings(){return this._settings}_freezeSettings(){return this._settingsFrozen=!0,this._settings}_delete(){return this._terminateTask||(this._terminateTask=this._terminate()),this._terminateTask}toJSON(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}}_terminate(){return function(t){const e=ml.get(t);e&&(ns("ComponentProvider","Removing Datastore"),ml.delete(t),e.terminate())}(this),Promise.resolve()}}class Sl{constructor(t,e,n){this.converter=e,this._key=n,this.type="document",this.firestore=t}get _path(){return this._key.path}get id(){return this._key.path.lastSegment()}get path(){return this._key.path.canonicalString()}get parent(){return new Al(this.firestore,this.converter,this._key.path.popLast())}withConverter(t){return new Sl(this.firestore,t,this._key)}}class Cl{constructor(t,e,n){this.converter=e,this._query=n,this.type="query",this.firestore=t}withConverter(t){return new Cl(this.firestore,t,this._query)}}class Al extends Cl{constructor(t,e,n){super(t,e,Zo(n)),this._path=n,this.type="collection"}get id(){return this._query.path.lastSegment()}get path(){return this._query.path.canonicalString()}get parent(){const t=this._path.popLast();return t.isEmpty()?null:new Sl(this.firestore,null,new Ns(t))}withConverter(t){return new Al(this.firestore,t,this._path)}}function _l(t,e,...n){if(t=m(t),yl("collection","path",e),t instanceof Il){const r=As.fromString(e,...n);return wl(r),new Al(t,null,r)}{if(!(t instanceof Sl||t instanceof Al))throw new hs(us.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");const r=t._path.child(As.fromString(e,...n));return wl(r),new Al(t.firestore,null,r)}}function xl(t,e,...n){if(t=m(t),1===arguments.length&&(e=bs.A()),yl("doc","path",e),t instanceof Il){const r=As.fromString(e,...n);return vl(r),new Sl(t,null,new Ns(r))}{if(!(t instanceof Sl||t instanceof Al))throw new hs(us.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");const r=t._path.child(As.fromString(e,...n));return vl(r),new Sl(t.firestore,t instanceof Al?t.converter:null,new Ns(r))}}class Nl{constructor(){this.Gc=Promise.resolve(),this.Qc=[],this.jc=!1,this.zc=[],this.Wc=null,this.Hc=!1,this.Jc=!1,this.Yc=[],this.qo=new zu(this,"async_queue_retry"),this.Xc=()=>{const t=qu();t&&ns("AsyncQueue","Visibility state changed to "+t.visibilityState),this.qo.Mo()};const t=qu();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.Xc)}get isShuttingDown(){return this.jc}enqueueAndForget(t){this.enqueue(t)}enqueueAndForgetEvenWhileRestricted(t){this.Zc(),this.ta(t)}enterRestrictedMode(t){if(!this.jc){this.jc=!0,this.Jc=t||!1;const e=qu();e&&"function"==typeof e.removeEventListener&&e.removeEventListener("visibilitychange",this.Xc)}}enqueue(t){if(this.Zc(),this.jc)return new Promise((()=>{}));const e=new ls;return this.ta((()=>this.jc&&this.Jc?Promise.resolve():(t().then(e.resolve,e.reject),e.promise))).then((()=>e.promise))}enqueueRetryable(t){this.enqueueAndForget((()=>(this.Qc.push(t),this.ea())))}async ea(){if(0!==this.Qc.length){try{await this.Qc[0](),this.Qc.shift(),this.qo.reset()}catch(t){if(!Ps(t))throw t;ns("AsyncQueue","Operation failed with retryable error: "+t)}this.Qc.length>0&&this.qo.No((()=>this.ea()))}}ta(t){const e=this.Gc.then((()=>(this.Hc=!0,t().catch((t=>{this.Wc=t,this.Hc=!1;const e=function(t){let e=t.message||"";return t.stack&&(e=t.stack.includes(t.message)?t.stack:t.message+"\n"+t.stack),e}(t);throw rs("INTERNAL UNHANDLED ERROR: ",e),t})).then((t=>(this.Hc=!1,t))))));return this.Gc=e,e}enqueueAfterDelay(t,e,n){this.Zc(),this.Yc.indexOf(t)>-1&&(e=0);const r=Sh.createAndSchedule(this,t,e,n,(t=>this.na(t)));return this.zc.push(r),r}Zc(){this.Wc&&os()}verifyOperationInProgress(){}async sa(){let t;do{t=this.Gc,await t}while(t!==this.Gc)}ia(t){for(const e of this.zc)if(e.timerId===t)return!0;return!1}ra(t){return this.sa().then((()=>{this.zc.sort(((t,e)=>t.targetTimeMs-e.targetTimeMs));for(const e of this.zc)if(e.skipDelay(),"all"!==t&&e.timerId===t)break;return this.sa()}))}oa(t){this.Yc.push(t)}na(t){const e=this.zc.indexOf(t);this.zc.splice(e,1)}}class Dl extends Il{constructor(t,e,n,r){super(t,e,n,r),this.type="firestore",this._queue=new Nl,this._persistenceKey=(null==r?void 0:r.name)||"[DEFAULT]"}_terminate(){return this._firestoreClient||Rl(this),this._firestoreClient.terminate()}}function kl(t){return t._firestoreClient||Rl(t),t._firestoreClient.verifyNotTerminated(),t._firestoreClient}function Rl(t){var e,n,r;const i=t._freezeSettings(),s=function(t,e,n,r){return new io(t,e,n,r.host,r.ssl,r.experimentalForceLongPolling,r.experimentalAutoDetectLongPolling,gl(r.experimentalLongPollingOptions),r.useFetchStreams)}(t._databaseId,(null===(e=t._app)||void 0===e?void 0:e.options.appId)||"",t._persistenceKey,i);t._firestoreClient=new hl(t._authCredentials,t._appCheckCredentials,t._queue,s),(null===(n=i.cache)||void 0===n?void 0:n._offlineComponentProvider)&&(null===(r=i.cache)||void 0===r?void 0:r._onlineComponentProvider)&&(t._firestoreClient._uninitializedComponentsProvider={_offlineKind:i.cache.kind,_offline:i.cache._offlineComponentProvider,_online:i.cache._onlineComponentProvider})}class Ol{constructor(t){this._byteString=t}static fromBase64String(t){try{return new Ol(Ys.fromBase64String(t))}catch(t){throw new hs(us.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+t)}}static fromUint8Array(t){return new Ol(Ys.fromUint8Array(t))}toBase64(){return this._byteString.toBase64()}toUint8Array(){return this._byteString.toUint8Array()}toString(){return"Bytes(base64: "+this.toBase64()+")"}isEqual(t){return this._byteString.isEqual(t._byteString)}}class Ml{constructor(...t){for(let e=0;e<t.length;++e)if(0===t[e].length)throw new hs(us.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new xs(t)}isEqual(t){return this._internalPath.isEqual(t._internalPath)}}class Ll{constructor(t){this._methodName=t}}class Pl{constructor(t,e){if(!isFinite(t)||t<-90||t>90)throw new hs(us.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+t);if(!isFinite(e)||e<-180||e>180)throw new hs(us.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+e);this._lat=t,this._long=e}get latitude(){return this._lat}get longitude(){return this._long}isEqual(t){return this._lat===t._lat&&this._long===t._long}toJSON(){return{latitude:this._lat,longitude:this._long}}_compareTo(t){return Es(this._lat,t._lat)||Es(this._long,t._long)}}const Fl=/^__.*__$/;class Vl{constructor(t,e,n){this.data=t,this.fieldMask=e,this.fieldTransforms=n}toMutation(t,e){return new Ja(t,this.data,this.fieldMask,e,this.fieldTransforms)}}function Ul(t){switch(t){case 0:case 2:case 1:return!0;case 3:case 4:return!1;default:throw os()}}class Bl{constructor(t,e,n,r,i,s){this.settings=t,this.databaseId=e,this.serializer=n,this.ignoreUndefinedProperties=r,void 0===i&&this.ua(),this.fieldTransforms=i||[],this.fieldMask=s||[]}get path(){return this.settings.path}get ca(){return this.settings.ca}aa(t){return new Bl(Object.assign(Object.assign({},this.settings),t),this.databaseId,this.serializer,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)}ha(t){var e;const n=null===(e=this.path)||void 0===e?void 0:e.child(t),r=this.aa({path:n,la:!1});return r.fa(t),r}da(t){var e;const n=null===(e=this.path)||void 0===e?void 0:e.child(t),r=this.aa({path:n,la:!1});return r.ua(),r}wa(t){return this.aa({path:void 0,la:!0})}_a(t){return Yl(t,this.settings.methodName,this.settings.ma||!1,this.path,this.settings.ga)}contains(t){return void 0!==this.fieldMask.find((e=>t.isPrefixOf(e)))||void 0!==this.fieldTransforms.find((e=>t.isPrefixOf(e.field)))}ua(){if(this.path)for(let t=0;t<this.path.length;t++)this.fa(this.path.get(t))}fa(t){if(0===t.length)throw this._a("Document fields must not be empty");if(Ul(this.ca)&&Fl.test(t))throw this._a('Document fields cannot begin and end with "__"')}}class jl{constructor(t,e,n){this.databaseId=t,this.ignoreUndefinedProperties=e,this.serializer=n||$u(t)}ya(t,e,n,r=!1){return new Bl({ca:t,methodName:e,ga:n,path:xs.emptyPath(),la:!1,ma:r},this.databaseId,this.serializer,this.ignoreUndefinedProperties)}}function ql(t){const e=t._freezeSettings(),n=$u(t._databaseId);return new jl(t._databaseId,!!e.ignoreUndefinedProperties,n)}class $l extends Ll{_toFieldTransform(t){if(2!==t.ca)throw 1===t.ca?t._a(`${this._methodName}() can only appear at the top level of your update data`):t._a(`${this._methodName}() cannot be used with set() unless you pass {merge:true}`);return t.fieldMask.push(t.path),null}isEqual(t){return t instanceof $l}}function zl(t,e){if(Gl(t=m(t)))return Kl("Unsupported field value:",e,t),function(t,e){const n={};return qs(t)?e.path&&e.path.length>0&&e.fieldMask.push(e.path):js(t,((t,r)=>{const i=zl(r,e.ha(t));null!=i&&(n[t]=i)})),{mapValue:{fields:n}}}(t,e);if(t instanceof Ll)return function(t,e){if(!Ul(e.ca))throw e._a(`${t._methodName}() can only be used with update() and set()`);if(!e.path)throw e._a(`${t._methodName}() is not currently supported inside arrays`);const n=t._toFieldTransform(e);n&&e.fieldTransforms.push(n)}(t,e),null;if(void 0===t&&e.ignoreUndefinedProperties)return null;if(e.path&&e.fieldMask.push(e.path),t instanceof Array){if(e.settings.la&&4!==e.ca)throw e._a("Nested arrays are not supported");return function(t,e){const n=[];let r=0;for(const i of t){let t=zl(i,e.wa(r));null==t&&(t={nullValue:"NULL_VALUE"}),n.push(t),r++}return{arrayValue:{values:n}}}(t,e)}return function(t,e){if(null===(t=m(t)))return{nullValue:"NULL_VALUE"};if("number"==typeof t)return Na(e.serializer,t);if("boolean"==typeof t)return{booleanValue:t};if("string"==typeof t)return{stringValue:t};if(t instanceof Date){const n=Is.fromDate(t);return{timestampValue:Rc(e.serializer,n)}}if(t instanceof Is){const n=new Is(t.seconds,1e3*Math.floor(t.nanoseconds/1e3));return{timestampValue:Rc(e.serializer,n)}}if(t instanceof Pl)return{geoPointValue:{latitude:t.latitude,longitude:t.longitude}};if(t instanceof Ol)return{bytesValue:Oc(e.serializer,t._byteString)};if(t instanceof Sl){const n=e.databaseId,r=t.firestore._databaseId;if(!r.isEqual(n))throw e._a(`Document reference is for database ${r.projectId}/${r.database} but should be for database ${n.projectId}/${n.database}`);return{referenceValue:Pc(t.firestore._databaseId||e.databaseId,t._key.path)}}throw e._a(`Unsupported field value: ${bl(t)}`)}(t,e)}function Gl(t){return!("object"!=typeof t||null===t||t instanceof Array||t instanceof Date||t instanceof Is||t instanceof Pl||t instanceof Ol||t instanceof Sl||t instanceof Ll)}function Kl(t,e,n){if(!Gl(n)||!function(t){return"object"==typeof t&&null!==t&&(Object.getPrototypeOf(t)===Object.prototype||null===Object.getPrototypeOf(t))}(n)){const r=bl(n);throw"an object"===r?e._a(t+" a custom object"):e._a(t+" "+r)}}function Hl(t,e,n){if((e=m(e))instanceof Ml)return e._internalPath;if("string"==typeof e)return Wl(t,e);throw Yl("Field path arguments must be of type string or ",t,!1,void 0,n)}const Ql=new RegExp("[~\\*/\\[\\]]");function Wl(t,e,n){if(e.search(Ql)>=0)throw Yl(`Invalid field path (${e}). Paths must not contain '~', '*', '/', '[', or ']'`,t,!1,void 0,n);try{return new Ml(...e.split("."))._internalPath}catch(r){throw Yl(`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,t,!1,void 0,n)}}function Yl(t,e,n,r,i){const s=r&&!r.isEmpty(),o=void 0!==i;let a=`Function ${e}() called with invalid data`;n&&(a+=" (via `toFirestore()`)"),a+=". ";let c="";return(s||o)&&(c+=" (found",s&&(c+=` in field ${r}`),o&&(c+=` in document ${i}`),c+=")"),new hs(us.INVALID_ARGUMENT,a+t+c)}function Xl(t,e){return t.some((t=>t.isEqual(e)))}class Jl{constructor(t,e,n,r,i){this._firestore=t,this._userDataWriter=e,this._key=n,this._document=r,this._converter=i}get id(){return this._key.path.lastSegment()}get ref(){return new Sl(this._firestore,this._converter,this._key)}exists(){return null!==this._document}data(){if(this._document){if(this._converter){const t=new Zl(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(t)}return this._userDataWriter.convertValue(this._document.data.value)}}get(t){if(this._document){const e=this._document.data.field(td("DocumentSnapshot.get",t));if(null!==e)return this._userDataWriter.convertValue(e)}}}class Zl extends Jl{data(){return super.data()}}function td(t,e){return"string"==typeof e?Wl(t,e):e instanceof Ml?e._internalPath:e._delegate._internalPath}class ed{}class nd extends ed{}function rd(t,e,...n){let r=[];e instanceof ed&&r.push(e),r=r.concat(n),function(t){const e=t.filter((t=>t instanceof od)).length,n=t.filter((t=>t instanceof id)).length;if(e>1||e>0&&n>0)throw new hs(us.INVALID_ARGUMENT,"InvalidQuery. When using composite filters, you cannot use more than one filter at the top level. Consider nesting the multiple filters within an `and(...)` statement. For example: change `query(query, where(...), or(...))` to `query(query, and(where(...), or(...)))`.")}(r);for(const e of r)t=e._apply(t);return t}class id extends nd{constructor(t,e,n){super(),this._field=t,this._op=e,this._value=n,this.type="where"}static _create(t,e,n){return new id(t,e,n)}_apply(t){const e=this._parse(t);return ud(t._query,e),new Cl(t.firestore,t.converter,oa(t._query,e))}_parse(t){const e=ql(t.firestore),n=function(t,e,n,r,i,s,o){let a;if(i.isKeyField()){if("array-contains"===s||"array-contains-any"===s)throw new hs(us.INVALID_ARGUMENT,`Invalid Query. You can't perform '${s}' queries on documentId().`);if("in"===s||"not-in"===s){cd(o,s);const e=[];for(const n of o)e.push(ad(r,t,n));a={arrayValue:{values:e}}}else a=ad(r,t,o)}else"in"!==s&&"not-in"!==s&&"array-contains-any"!==s||cd(o,s),a=function(t,e,n,r=!1){return zl(n,t.ya(r?4:3,e))}(n,"where",o,"in"===s||"not-in"===s);return Ro.create(i,s,a)}(t._query,0,e,t.firestore._databaseId,this._field,this._op,this._value);return n}}function sd(t,e,n){const r=e,i=td("where",t);return id._create(i,r,n)}class od extends ed{constructor(t,e){super(),this.type=t,this._queryConstraints=e}static _create(t,e){return new od(t,e)}_parse(t){const e=this._queryConstraints.map((e=>e._parse(t))).filter((t=>t.getFilters().length>0));return 1===e.length?e[0]:Oo.create(e,this._getOperator())}_apply(t){const e=this._parse(t);return 0===e.getFilters().length?t:(function(t,e){let n=t;const r=e.getFlattenedFilters();for(const t of r)ud(n,t),n=oa(n,t)}(t._query,e),new Cl(t.firestore,t.converter,oa(t._query,e)))}_getQueryConstraints(){return this._queryConstraints}_getOperator(){return"and"===this.type?"and":"or"}}function ad(t,e,n){if("string"==typeof(n=m(n))){if(""===n)throw new hs(us.INVALID_ARGUMENT,"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.");if(!ra(e)&&-1!==n.indexOf("/"))throw new hs(us.INVALID_ARGUMENT,`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${n}' contains a '/' character.`);const r=e.path.child(As.fromString(n));if(!Ns.isDocumentKey(r))throw new hs(us.INVALID_ARGUMENT,`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${r}' is not because it has an odd number of segments (${r.length}).`);return go(t,new Ns(r))}if(n instanceof Sl)return go(t,n._key);throw new hs(us.INVALID_ARGUMENT,`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${bl(n)}.`)}function cd(t,e){if(!Array.isArray(t)||0===t.length)throw new hs(us.INVALID_ARGUMENT,`Invalid Query. A non-empty array is required for '${e.toString()}' filters.`)}function ud(t,e){if(e.isInequality()){const n=na(t),r=e.field;if(null!==n&&!n.isEqual(r))throw new hs(us.INVALID_ARGUMENT,`Invalid query. All where filters with an inequality (<, <=, !=, not-in, >, or >=) must be on the same field. But you have inequality filters on '${n.toString()}' and '${r.toString()}'`);const i=ea(t);null!==i&&function(t,e,n){if(!n.isEqual(e))throw new hs(us.INVALID_ARGUMENT,`Invalid query. You have a where filter with an inequality (<, <=, !=, not-in, >, or >=) on field '${e.toString()}' and so you must also use '${e.toString()}' as your first argument to orderBy(), but your first orderBy() is on field '${n.toString()}' instead.`)}(0,r,i)}const n=function(t,e){for(const n of t)for(const t of n.getFlattenedFilters())if(e.indexOf(t.op)>=0)return t.op;return null}(t.filters,function(t){switch(t){case"!=":return["!=","not-in"];case"array-contains-any":case"in":return["not-in"];case"not-in":return["array-contains-any","in","not-in","!="];default:return[]}}(e.op));if(null!==n)throw n===e.op?new hs(us.INVALID_ARGUMENT,`Invalid query. You cannot use more than one '${e.op.toString()}' filter.`):new hs(us.INVALID_ARGUMENT,`Invalid query. You cannot use '${e.op.toString()}' filters with '${n.toString()}' filters.`)}class hd{convertValue(t,e="none"){switch(ao(t)){case 0:return null;case 1:return t.booleanValue;case 2:return Zs(t.integerValue||t.doubleValue);case 3:return this.convertTimestamp(t.timestampValue);case 4:return this.convertServerTimestamp(t,e);case 5:return t.stringValue;case 6:return this.convertBytes(to(t.bytesValue));case 7:return this.convertReference(t.referenceValue);case 8:return this.convertGeoPoint(t.geoPointValue);case 9:return this.convertArray(t.arrayValue,e);case 10:return this.convertObject(t.mapValue,e);default:throw os()}}convertObject(t,e){return this.convertObjectMap(t.fields,e)}convertObjectMap(t,e="none"){const n={};return js(t,((t,r)=>{n[t]=this.convertValue(r,e)})),n}convertGeoPoint(t){return new Pl(Zs(t.latitude),Zs(t.longitude))}convertArray(t,e){return(t.values||[]).map((t=>this.convertValue(t,e)))}convertServerTimestamp(t,e){switch(e){case"previous":const n=no(t);return null==n?null:this.convertValue(n,e);case"estimate":return this.convertTimestamp(ro(t));default:return null}}convertTimestamp(t){const e=Js(t);return new Is(e.seconds,e.nanos)}convertDocumentKey(t,e){const n=As.fromString(t);as(eu(n));const r=new so(n.get(1),n.get(3)),i=new Ns(n.popFirst(5));return r.isEqual(e)||rs(`Document ${i} contains a document reference within a different database (${r.projectId}/${r.database}) which is not supported. It will be treated as a reference in the current database (${e.projectId}/${e.database}) instead.`),i}}class ld{constructor(t,e){this.hasPendingWrites=t,this.fromCache=e}isEqual(t){return this.hasPendingWrites===t.hasPendingWrites&&this.fromCache===t.fromCache}}class dd extends Jl{constructor(t,e,n,r,i,s){super(t,e,n,r,s),this._firestore=t,this._firestoreImpl=t,this.metadata=i}exists(){return super.exists()}data(t={}){if(this._document){if(this._converter){const e=new fd(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(e,t)}return this._userDataWriter.convertValue(this._document.data.value,t.serverTimestamps)}}get(t,e={}){if(this._document){const n=this._document.data.field(td("DocumentSnapshot.get",t));if(null!==n)return this._userDataWriter.convertValue(n,e.serverTimestamps)}}}class fd extends dd{data(t={}){return super.data(t)}}class pd{constructor(t,e,n,r){this._firestore=t,this._userDataWriter=e,this._snapshot=r,this.metadata=new ld(r.hasPendingWrites,r.fromCache),this.query=n}get docs(){const t=[];return this.forEach((e=>t.push(e))),t}get size(){return this._snapshot.docs.size}get empty(){return 0===this.size}forEach(t,e){this._snapshot.docs.forEach((n=>{t.call(e,new fd(this._firestore,this._userDataWriter,n.key,n,new ld(this._snapshot.mutatedKeys.has(n.key),this._snapshot.fromCache),this.query.converter))}))}docChanges(t={}){const e=!!t.includeMetadataChanges;if(e&&this._snapshot.excludesMetadataChanges)throw new hs(us.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===e||(this._cachedChanges=function(t,e){if(t._snapshot.oldDocs.isEmpty()){let e=0;return t._snapshot.docChanges.map((n=>{const r=new fd(t._firestore,t._userDataWriter,n.doc.key,n.doc,new ld(t._snapshot.mutatedKeys.has(n.doc.key),t._snapshot.fromCache),t.query.converter);return n.doc,{type:"added",doc:r,oldIndex:-1,newIndex:e++}}))}{let n=t._snapshot.oldDocs;return t._snapshot.docChanges.filter((t=>e||3!==t.type)).map((e=>{const r=new fd(t._firestore,t._userDataWriter,e.doc.key,e.doc,new ld(t._snapshot.mutatedKeys.has(e.doc.key),t._snapshot.fromCache),t.query.converter);let i=-1,s=-1;return 0!==e.type&&(i=n.indexOf(e.doc.key),n=n.delete(e.doc.key)),1!==e.type&&(n=n.add(e.doc),s=n.indexOf(e.doc.key)),{type:gd(e.type),doc:r,oldIndex:i,newIndex:s}}))}}(this,e),this._cachedChangesIncludeMetadataChanges=e),this._cachedChanges}}function gd(t){switch(t){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return os()}}function md(t){t=El(t,Sl);const e=El(t.firestore,Dl);return function(t,e,n={}){const r=new ls;return t.asyncQueue.enqueueAndForget((async()=>function(t,e,n,r,i){const s=new ul({next:s=>{e.enqueueAndForget((()=>Rh(t,o)));const a=s.docs.has(n);!a&&s.fromCache?i.reject(new hs(us.UNAVAILABLE,"Failed to get document because the client is offline.")):a&&s.fromCache&&r&&"server"===r.source?i.reject(new hs(us.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):i.resolve(s)},error:t=>i.reject(t)}),o=new Ph(Zo(n.path),s,{includeMetadataChanges:!0,Ku:!0});return kh(t,o)}(await pl(t),t.asyncQueue,e,n,r))),r.promise}(kl(e),t._key).then((n=>function(t,e,n){const r=n.docs.get(e._key),i=new yd(t);return new dd(t,i,e._key,r,new ld(n.hasPendingWrites,n.fromCache),e.converter)}(e,t,n)))}class yd extends hd{constructor(t){super(),this.firestore=t}convertBytes(t){return new Ol(t)}convertReference(t){const e=this.convertDocumentKey(t,this.firestore._databaseId);return new Sl(this.firestore,null,e)}}function vd(t){t=El(t,Cl);const e=El(t.firestore,Dl),n=kl(e),r=new yd(e);return function(t){if("L"===t.limitType&&0===t.explicitOrderBy.length)throw new hs(us.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}(t._query),function(t,e,n={}){const r=new ls;return t.asyncQueue.enqueueAndForget((async()=>function(t,e,n,r,i){const s=new ul({next:n=>{e.enqueueAndForget((()=>Rh(t,o))),n.fromCache&&"server"===r.source?i.reject(new hs(us.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):i.resolve(n)},error:t=>i.reject(t)}),o=new Ph(n,s,{includeMetadataChanges:!0,Ku:!0});return kh(t,o)}(await pl(t),t.asyncQueue,e,n,r))),r.promise}(n,t._query).then((n=>new pd(e,r,t,n)))}function wd(t,e,n,...r){t=El(t,Sl);const i=El(t.firestore,Dl),s=ql(i);let o;return o="string"==typeof(e=m(e))||e instanceof Ml?function(t,e,n,r,i,s){const o=t.ya(1,e,n),a=[Hl(e,r,n)],c=[i];if(s.length%2!=0)throw new hs(us.INVALID_ARGUMENT,`Function ${e}() needs to be called with an even number of arguments that alternate between field names and values.`);for(let t=0;t<s.length;t+=2)a.push(Hl(e,s[t])),c.push(s[t+1]);const u=[],h=Io.empty();for(let t=a.length-1;t>=0;--t)if(!Xl(u,a[t])){const e=a[t];let n=c[t];n=m(n);const r=o.da(e);if(n instanceof $l)u.push(e);else{const t=zl(n,r);null!=t&&(u.push(e),h.set(e,t))}}const l=new Qs(u);return new Vl(h,l,o.fieldTransforms)}(s,"updateDoc",t._key,e,n,r):function(t,e,n,r){const i=t.ya(1,e,n);Kl("Data must be an object, but it was:",i,r);const s=[],o=Io.empty();js(r,((t,r)=>{const a=Wl(e,t,n);r=m(r);const c=i.da(a);if(r instanceof $l)s.push(a);else{const t=zl(r,c);null!=t&&(s.push(a),o.set(a,t))}}));const a=new Qs(s);return new Vl(o,a,i.fieldTransforms)}(s,"updateDoc",t._key,e),function(t,e){return function(t,e){const n=new ls;return t.asyncQueue.enqueueAndForget((async()=>async function(t,e,n){const r=ol(t);try{const t=await function(t,e){const n=cs(t),r=Is.now(),i=e.reduce(((t,e)=>t.add(e.key)),Ca());let s,o;return n.persistence.runTransaction("Locally write mutations","readwrite",(t=>{let a=ma(),c=Ca();return n.Zi.getEntries(t,i).next((t=>{a=t,a.forEach(((t,e)=>{e.isValidDocument()||(c=c.add(t))}))})).next((()=>n.localDocuments.getOverlayedDocuments(t,a))).next((i=>{s=i;const o=[];for(const t of e){const e=Wa(t,s.get(t.key).overlayedDocument);null!=e&&o.push(new Ja(t.key,e,So(e.value.mapValue),$a.exists(!0)))}return n.mutationQueue.addMutationBatch(t,r,o,e)})).next((e=>{o=e;const r=e.applyToLocalDocumentSet(s,c);return n.documentOverlayCache.saveOverlays(t,e.batchId,r)}))})).then((()=>({batchId:o.batchId,changes:wa(s)})))}(r.localStore,e);r.sharedClientState.addPendingMutation(t.batchId),function(t,e,n){let r=t.Tc[t.currentUser.toKey()];r||(r=new $s(Es)),r=r.insert(e,n),t.Tc[t.currentUser.toKey()]=r}(r,t.batchId,n),await rl(r,t.changes),await dh(r.remoteStore)}catch(t){const e=Ch(t,"Failed to persist write");n.reject(e)}}(await function(t){return fl(t).then((t=>t.syncEngine))}(t),e,n))),n.promise}(kl(t),e)}(i,[o.toMutation(t._key,$a.exists(!0))])}function bd(t){return bd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bd(t)}function Ed(t){return function(t){if(Array.isArray(t))return Td(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Td(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Td(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Td(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function Id(){Id=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function c(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(e,n,r,i){var s=n&&n.prototype instanceof d?n:d,o=Object.create(s.prototype);return c(o,"_invoke",function(e,n,r){var i=1;return function(s,o){if(3===i)throw Error("Generator is already running");if(4===i){if("throw"===s)throw o;return{value:t,done:!0}}for(r.method=s,r.arg=o;;){var a=r.delegate;if(a){var c=E(a,r);if(c){if(c===l)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===i)throw i=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=3;var u=h(e,n,r);if("normal"===u.type){if(i=r.done?4:2,u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=4,r.method="throw",r.arg=u.arg)}}}(e,r,new S(i||[])),!0),o}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var l={};function d(){}function f(){}function p(){}var g={};c(g,s,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(C([])));y&&y!==n&&r.call(y,s)&&(g=y);var v=p.prototype=d.prototype=Object.create(g);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function n(i,s,o,a){var c=h(t[i],t,s);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==bd(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,o,a)}),(function(t){n("throw",t,o,a)})):e.resolve(l).then((function(t){u.value=t,o(u)}),(function(t){return n("throw",t,o,a)}))}a(c.arg)}var i;c(this,"_invoke",(function(t,r){function s(){return new e((function(e,i){n(t,r,e,i)}))}return i=i?i.then(s,s):s()}),!0)}function E(e,n){var r=n.method,i=e.i[r];if(i===t)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),l;var s=h(i,e.i,n.arg);if("throw"===s.type)return n.method="throw",n.arg=s.arg,n.delegate=null,l;var o=s.arg;return o?o.done?(n[e.r]=o.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,l):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,l)}function T(t){this.tryEntries.push(t)}function I(e){var n=e[4]||{};n.type="normal",n.arg=t,e[4]=n}function S(t){this.tryEntries=[[-1]],t.forEach(T,this),this.reset(!0)}function C(e){if(null!=e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(bd(e)+" is not iterable")}return f.prototype=p,c(v,"constructor",p),c(p,"constructor",f),f.displayName=c(p,a,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,a,"GeneratorFunction")),t.prototype=Object.create(v),t},e.awrap=function(t){return{__await:t}},w(b.prototype),c(b.prototype,o,(function(){return this})),e.AsyncIterator=b,e.async=function(t,n,r,i,s){void 0===s&&(s=Promise);var o=new b(u(t,n,r,i),s);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},w(v),c(v,a,"Generator"),c(v,s,(function(){return this})),c(v,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.unshift(r);return function t(){for(;n.length;)if((r=n.pop())in e)return t.value=r,t.done=!1,t;return t.done=!0,t}},e.values=C,S.prototype={constructor:S,reset:function(e){if(this.prev=this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(I),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(t){o.type="throw",o.arg=e,n.next=t}for(var i=n.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],o=s[4],a=this.prev,c=s[1],u=s[2];if(-1===s[0])return r("end"),!1;if(!c&&!u)throw Error("try statement without catch or finally");if(null!=s[0]&&s[0]<=a){if(a<c)return this.method="next",this.arg=t,r(c),!0;if(a<u)return r(u),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}i&&("break"===t||"continue"===t)&&i[0]<=e&&e<=i[2]&&(i=null);var s=i?i[4]:{};return s.type=t,s.arg=e,i?(this.method="next",this.next=i[2],l):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),I(n),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[0]===t){var r=n[4];if("throw"===r.type){var i=r.arg;I(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={i:C(e),r:n,n:r},"next"===this.method&&(this.arg=t),l}},e}function Sd(t,e,n,r,i,s,o){try{var a=t[s](o),c=a.value}catch(t){return void n(t)}a.done?e(c):Promise.resolve(c).then(r,i)}function Cd(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var s=t.apply(e,n);function o(t){Sd(s,r,i,o,a,"next",t)}function a(t){Sd(s,r,i,o,a,"throw",t)}o(void 0)}))}}!function(t,e=!0){Zi="9.23.0",wt(new y("firestore",((t,{instanceIdentifier:n,options:r})=>{const i=t.getProvider("app").getImmediate(),s=new Dl(new ps(t.getProvider("auth-internal")),new vs(t.getProvider("app-check-internal")),function(t,e){if(!Object.prototype.hasOwnProperty.apply(t.options,["projectId"]))throw new hs(us.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new so(t.options.projectId,e)}(i,n),i);return r=Object.assign({useFetchStreams:e},r),s._setSettings(r),s}),"PUBLIC").setMultipleInstances(!0)),Et(Xi,"3.13.0",t),Et(Xi,"3.13.0","esm2017")}();var Ad=window.db,_d=function(){var t=Cd(Id().mark((function t(e){var n;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=xl(Ad,"tournaments",e),t.next=3,wd(n,{status:"paused",pausedAt:(new Date).toISOString()});case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),xd=function(){var t=Cd(Id().mark((function t(e){var n;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=xl(Ad,"tournaments",e),t.next=3,wd(n,{status:"live",resumedAt:(new Date).toISOString()});case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),Nd=function(){var t=Cd(Id().mark((function t(e){var n;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=xl(Ad,"tournaments",e),t.next=3,wd(n,{status:"live",startedAt:(new Date).toISOString()});case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),Dd=function(){var t=Cd(Id().mark((function t(e){var n;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=xl(Ad,"tournaments",e),t.next=3,wd(n,{status:"completed",completedAt:(new Date).toISOString()});case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),kd=function(){var t=Cd(Id().mark((function t(e){var n,r,i,s;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=xl(Ad,"tournaments",e),r=_l(Ad,"tournaments",e,"matches"),t.next=4,vd(r);case 4:return i=t.sent,s=Ad.batch(),i.docs.forEach((function(t){s.update(t.ref,{status:"pending",winnerId:null,loserId:null,score:null,completedAt:null,resolvedAt:null,resolvedBy:null})})),s.update(n,{status:"upcoming",startedAt:null,completedAt:null,pausedAt:null,resumedAt:null}),t.next=10,s.commit();case 10:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),Rd=function(){var t=Cd(Id().mark((function t(e,n,r){var i;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=xl(Ad,"tournaments",e,"matches",n),t.next=3,wd(i,{status:"completed",winnerId:r.winnerId,loserId:r.loserId,score:r.score,completedAt:(new Date).toISOString(),forceCompleted:!0,forceCompletedBy:r.adminId});case 3:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),Od=function(){var t=Cd(Id().mark((function t(e,n){var r;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=xl(Ad,"tournaments",e,"matches",n),t.next=3,wd(r,{status:"pending",winnerId:null,loserId:null,score:null,completedAt:null,resolvedAt:null,resolvedBy:null,forceCompleted:!1,forceCompletedBy:null});case 3:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),Md=function(){var t=Cd(Id().mark((function t(e,n,r){var i;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=xl(Ad,"tournaments",e,"matches",n),t.next=3,wd(i,{status:"completed",winnerId:r.winnerId,loserId:r.loserId,score:r.score,completedAt:(new Date).toISOString(),overridden:!0,overriddenBy:r.adminId,originalResult:r.originalResult});case 3:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),Ld=function(){var t=Cd(Id().mark((function t(e,n,r){var i,s,o,a,c,u;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=xl(Ad,"tournaments",e,"matches",n),t.next=3,wd(i,{status:r,statusChangedAt:(new Date).toISOString()});case 3:if("disputed"!==r){t.next=20;break}return t.prev=4,s=xl(Ad,"tournaments",e),t.next=8,md(s);case 8:if(!(o=t.sent).exists()){t.next=15;break}if(c=o.data(),!((u=null===(a=c.bracketData)||void 0===a||null===(a=a.matchesById)||void 0===a?void 0:a[n])&&c.creatorAddress&&window.notificationService)){t.next=15;break}return t.next=15,window.notificationService.createNotification(c.creatorAddress,"match_dispute","Match Dispute Requires Attention",'A match in "'.concat(c.tournamentName,'" has been disputed and needs admin review'),{tournamentId:e,tournamentName:c.tournamentName,matchId:n,matchIdentifier:u.identifier||u.id,participant1:u.participant1Name||u.participant1Id,participant2:u.participant2Name||u.participant2Id});case 15:t.next=20;break;case 17:t.prev=17,t.t0=t.catch(4),console.error("Error creating dispute notification:",t.t0);case 20:case"end":return t.stop()}}),t,null,[[4,17]])})));return function(e,n,r){return t.apply(this,arguments)}}(),Pd=function(){var t=Cd(Id().mark((function t(e,n){var r,i,s,o;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n&&"string"==typeof n&&42===n.length&&n.startsWith("0x")){t.next=2;break}throw new Error("Invalid wallet address format");case 2:return r=xl(Ad,"tournaments",e),t.next=5,md(r);case 5:if(i=t.sent,"upcoming"===(s=i.data()).status){t.next=9;break}throw new Error("Cannot add participants to a tournament that has already started or ended");case 9:if(o=n.toLowerCase(),!s.participants||!s.participants.includes(o)){t.next=12;break}throw new Error("Participant already exists in tournament");case 12:return t.next=14,wd(r,{participants:[].concat(Ed(s.participants||[]),[o]),participantCount:(s.participantCount||0)+1});case 14:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),Fd=function(){var t=Cd(Id().mark((function t(e,n){var r,i,s,o,a;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=xl(Ad,"tournaments",e),t.next=3,md(r);case 3:return i=t.sent,s=i.data(),t.next=7,wd(r,{participants:s.participants.filter((function(t){return t.id!==n}))});case 7:return o=_l(Ad,"tournaments",e,"matches"),a=rd(o,sd("participant1Id","==",n),sd("participant2Id","==",n)),t.next=11,vd(a);case 11:t.sent.docs.forEach(function(){var t=Cd(Id().mark((function t(e){var r;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("pending"!==(r=e.data()).status){t.next=6;break}return t.next=4,wd(e.ref,{status:"cancelled",cancelledAt:(new Date).toISOString(),cancelledReason:"participant_removed"});case 4:t.next=9;break;case 6:if("live"!==r.status){t.next=9;break}return t.next=9,Bd(e.ref,n);case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 13:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),Vd=function(){var t=Cd(Id().mark((function t(e,n,r){var i,s,o,a,c;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=xl(Ad,"tournaments",e),t.next=3,md(i);case 3:return s=t.sent,o=s.data(),t.next=7,wd(i,{participants:o.participants.map((function(t){return t.id===n?r:t}))});case 7:return a=_l(Ad,"tournaments",e,"matches"),c=rd(a,sd("participant1Id","==",n),sd("participant2Id","==",n)),t.next=11,vd(c);case 11:t.sent.docs.forEach(function(){var t=Cd(Id().mark((function t(e){var i;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("pending"!==(i=e.data()).status){t.next=4;break}return t.next=4,wd(e.ref,{participant1Id:i.participant1Id===n?r.id:i.participant1Id,participant2Id:i.participant2Id===n?r.id:i.participant2Id,participantReplaced:!0,replacedAt:(new Date).toISOString(),oldParticipantId:n,newParticipantId:r.id});case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 13:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),Ud=function(){var t=Cd(Id().mark((function t(e,n,r){var i,s,o,a;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=xl(Ad,"tournaments",e,"matches",n),t.next=3,md(i);case 3:return s=t.sent,o=s.data(),a=o.participant1Id===r?o.participant2Id:o.participant1Id,t.next=8,wd(i,{status:"completed",winnerId:a,loserId:r,noShow:!0,noShowParticipantId:r,completedAt:(new Date).toISOString()});case 8:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),Bd=function(){var t=Cd(Id().mark((function t(e,n){var r,i,s;return Id().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,md(e);case 2:return r=t.sent,i=r.data(),s=i.participant1Id===n?i.participant2Id:i.participant1Id,t.next=7,wd(e,{status:"completed",winnerId:s,loserId:n,cancelled:!0,cancelledAt:(new Date).toISOString(),cancelledReason:"participant_removed"});case 7:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),jd=i(72),qd=i.n(jd),$d=i(825),zd=i.n($d),Gd=i(659),Kd=i.n(Gd),Hd=i(56),Qd=i.n(Hd),Wd=i(540),Yd=i.n(Wd),Xd=i(113),Jd=i.n(Xd),Zd=i(516),tf={};function ef(t){return ef="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ef(t)}function nf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function rf(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?nf(Object(n),!0).forEach((function(e){sf(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):nf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function sf(t,e,n){return(e=function(t){var e=function(t){if("object"!=ef(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=ef(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==ef(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function of(){of=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",o=i.asyncIterator||"@@asyncIterator",a=i.toStringTag||"@@toStringTag";function c(t,e,n,r){return Object.defineProperty(t,e,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function u(e,n,r,i){var s=n&&n.prototype instanceof d?n:d,o=Object.create(s.prototype);return c(o,"_invoke",function(e,n,r){var i=1;return function(s,o){if(3===i)throw Error("Generator is already running");if(4===i){if("throw"===s)throw o;return{value:t,done:!0}}for(r.method=s,r.arg=o;;){var a=r.delegate;if(a){var c=E(a,r);if(c){if(c===l)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===i)throw i=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=3;var u=h(e,n,r);if("normal"===u.type){if(i=r.done?4:2,u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=4,r.method="throw",r.arg=u.arg)}}}(e,r,new S(i||[])),!0),o}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var l={};function d(){}function f(){}function p(){}var g={};c(g,s,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(C([])));y&&y!==n&&r.call(y,s)&&(g=y);var v=p.prototype=d.prototype=Object.create(g);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function b(t,e){function n(i,s,o,a){var c=h(t[i],t,s);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==ef(l)&&r.call(l,"__await")?e.resolve(l.__await).then((function(t){n("next",t,o,a)}),(function(t){n("throw",t,o,a)})):e.resolve(l).then((function(t){u.value=t,o(u)}),(function(t){return n("throw",t,o,a)}))}a(c.arg)}var i;c(this,"_invoke",(function(t,r){function s(){return new e((function(e,i){n(t,r,e,i)}))}return i=i?i.then(s,s):s()}),!0)}function E(e,n){var r=n.method,i=e.i[r];if(i===t)return n.delegate=null,"throw"===r&&e.i.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),l;var s=h(i,e.i,n.arg);if("throw"===s.type)return n.method="throw",n.arg=s.arg,n.delegate=null,l;var o=s.arg;return o?o.done?(n[e.r]=o.value,n.next=e.n,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,l):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,l)}function T(t){this.tryEntries.push(t)}function I(e){var n=e[4]||{};n.type="normal",n.arg=t,e[4]=n}function S(t){this.tryEntries=[[-1]],t.forEach(T,this),this.reset(!0)}function C(e){if(null!=e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(ef(e)+" is not iterable")}return f.prototype=p,c(v,"constructor",p),c(p,"constructor",f),f.displayName=c(p,a,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===f||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,c(t,a,"GeneratorFunction")),t.prototype=Object.create(v),t},e.awrap=function(t){return{__await:t}},w(b.prototype),c(b.prototype,o,(function(){return this})),e.AsyncIterator=b,e.async=function(t,n,r,i,s){void 0===s&&(s=Promise);var o=new b(u(t,n,r,i),s);return e.isGeneratorFunction(n)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},w(v),c(v,a,"Generator"),c(v,s,(function(){return this})),c(v,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.unshift(r);return function t(){for(;n.length;)if((r=n.pop())in e)return t.value=r,t.done=!1,t;return t.done=!0,t}},e.values=C,S.prototype={constructor:S,reset:function(e){if(this.prev=this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(I),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(t){o.type="throw",o.arg=e,n.next=t}for(var i=n.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],o=s[4],a=this.prev,c=s[1],u=s[2];if(-1===s[0])return r("end"),!1;if(!c&&!u)throw Error("try statement without catch or finally");if(null!=s[0]&&s[0]<=a){if(a<c)return this.method="next",this.arg=t,r(c),!0;if(a<u)return r(u),!1}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var i=r;break}}i&&("break"===t||"continue"===t)&&i[0]<=e&&e<=i[2]&&(i=null);var s=i?i[4]:{};return s.type=t,s.arg=e,i?(this.method="next",this.next=i[2],l):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[2]===t)return this.complete(n[4],n[3]),I(n),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n[0]===t){var r=n[4];if("throw"===r.type){var i=r.arg;I(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={i:C(e),r:n,n:r},"next"===this.method&&(this.arg=t),l}},e}function af(t,e,n,r,i,s,o){try{var a=t[s](o),c=a.value}catch(t){return void n(t)}a.done?e(c):Promise.resolve(c).then(r,i)}function cf(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var s=t.apply(e,n);function o(t){af(s,r,i,o,a,"next",t)}function a(t){af(s,r,i,o,a,"throw",t)}o(void 0)}))}}function uf(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,s,o,a=[],c=!0,u=!1;try{if(s=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=s.call(n)).done)&&(a.push(r.value),a.length!==e);c=!0);}catch(t){u=!0,i=t}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(u)throw i}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return hf(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?hf(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hf(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}tf.styleTagTransform=Jd(),tf.setAttributes=Qd(),tf.insert=Kd().bind(null,"head"),tf.domAPI=zd(),tf.insertStyleElement=Yd(),qd()(Zd.A,tf),Zd.A&&Zd.A.locals&&Zd.A.locals;return window.React=o(),window.ReactDOM=c(),window.AdminControlPanel=function(t){var e=t.tournament,n=t.currentUserAddress,r=uf((0,s.useState)(null),2),i=r[0],a=(r[1],uf((0,s.useState)({id:"",name:""}),2)),c=a[0],u=a[1],h=uf((0,s.useState)({id:"",name:""}),2),l=h[0],d=h[1],f=uf((0,s.useState)(null),2),p=f[0],g=f[1],m=uf((0,s.useState)(null),2),y=m[0],v=m[1],w=function(){var t=cf(of().mark((function t(n){return of().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:t.prev=0,g(null),v(null),t.t0=n,t.next="pause"===t.t0?6:"resume"===t.t0?10:"forceStart"===t.t0?14:"forceEnd"===t.t0?18:"reset"===t.t0?22:27;break;case 6:return t.next=8,_d(e.id);case 8:return v("Tournament paused successfully"),t.abrupt("break",28);case 10:return t.next=12,xd(e.id);case 12:return v("Tournament resumed successfully"),t.abrupt("break",28);case 14:return t.next=16,Nd(e.id);case 16:return v("Tournament started successfully"),t.abrupt("break",28);case 18:return t.next=20,Dd(e.id);case 20:return v("Tournament ended successfully"),t.abrupt("break",28);case 22:if(!window.confirm("Are you sure you want to reset the tournament? This will clear all match results.")){t.next=26;break}return t.next=25,kd(e.id);case 25:v("Tournament reset successfully");case 26:return t.abrupt("break",28);case 27:throw new Error("Invalid action");case 28:t.next=33;break;case 30:t.prev=30,t.t1=t.catch(0),g(t.t1.message);case 33:case"end":return t.stop()}}),t,null,[[0,30]])})));return function(e){return t.apply(this,arguments)}}(),b=function(){var t=cf(of().mark((function t(r,i){var s,o=arguments;return of().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:s=o.length>2&&void 0!==o[2]?o[2]:{},t.prev=1,g(null),v(null),t.t0=i,t.next="forceComplete"===t.t0?7:"reset"===t.t0?11:"override"===t.t0?15:"setStatus"===t.t0?19:"noShow"===t.t0?23:27;break;case 7:return t.next=9,Rd(e.id,r.id,rf(rf({},s),{},{adminId:n}));case 9:return v("Match completed successfully"),t.abrupt("break",28);case 11:return t.next=13,Od(e.id,r.id);case 13:return v("Match reset successfully"),t.abrupt("break",28);case 15:return t.next=17,Md(e.id,r.id,rf(rf({},s),{},{adminId:n}));case 17:return v("Match result overridden successfully"),t.abrupt("break",28);case 19:return t.next=21,Ld(e.id,r.id,s.status);case 21:return v("Match status updated successfully"),t.abrupt("break",28);case 23:return t.next=25,Ud(e.id,r.id,s.noShowParticipantId);case 25:return v("No-show handled successfully"),t.abrupt("break",28);case 27:throw new Error("Invalid action");case 28:t.next=33;break;case 30:t.prev=30,t.t1=t.catch(1),g(t.t1.message);case 33:case"end":return t.stop()}}),t,null,[[1,30]])})));return function(e,n){return t.apply(this,arguments)}}(),E=function(){var t=cf(of().mark((function t(n){var r,i=arguments;return of().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=i.length>1&&void 0!==i[1]?i[1]:{},t.prev=1,g(null),v(null),t.t0=n,t.next="add"===t.t0?7:"remove"===t.t0?12:"replace"===t.t0?16:21;break;case 7:return t.next=9,Pd(e.id,c.id);case 9:return u({id:"",name:""}),v("Participant added successfully"),t.abrupt("break",22);case 12:return t.next=14,Fd(e.id,r.participantId);case 14:return v("Participant removed successfully"),t.abrupt("break",22);case 16:return t.next=18,Vd(e.id,r.oldParticipantId,l.id);case 18:return d({id:"",name:""}),v("Participant replaced successfully"),t.abrupt("break",22);case 21:throw new Error("Invalid action");case 22:t.next=27;break;case 24:t.prev=24,t.t1=t.catch(1),g(t.t1.message);case 27:case"end":return t.stop()}}),t,null,[[1,24]])})));return function(e){return t.apply(this,arguments)}}();return o().createElement("div",{className:"admin-control-panel"},o().createElement("h2",null,"Tournament Admin Controls"),p&&o().createElement("div",{className:"error-message"},p),y&&o().createElement("div",{className:"success-message"},y),o().createElement("div",{className:"tournament-controls"},o().createElement("h3",null,"Tournament Controls"),o().createElement("div",{className:"control-buttons"},"live"===e.status&&o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return w("pause")}},"Pause Tournament"),"paused"===e.status&&o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return w("resume")}},"Resume Tournament"),"upcoming"===e.status&&o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return w("forceStart")}},"Force Start"),"live"===e.status&&o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return w("forceEnd")}},"Force End"),o().createElement("button",{className:"btn btn-cyber-danger",onClick:function(){return w("reset")}},"Reset Tournament"))),o().createElement("div",{className:"participant-controls"},o().createElement("h3",null,"Participant Management"),o().createElement("div",{className:"add-participant"},o().createElement("input",{type:"text",placeholder:"Participant ID",value:c.id,onChange:function(t){return u((function(e){return rf(rf({},e),{},{id:t.target.value})}))}}),o().createElement("input",{type:"text",placeholder:"Participant Name",value:c.name,onChange:function(t){return u((function(e){return rf(rf({},e),{},{name:t.target.value})}))}}),o().createElement("button",{className:"btn btn-cyber-primary",onClick:function(){return E("add")}},"Add Participant")),o().createElement("div",{className:"replace-participant"},o().createElement("input",{type:"text",placeholder:"New Participant ID",value:l.id,onChange:function(t){return d((function(e){return rf(rf({},e),{},{id:t.target.value})}))}}),o().createElement("input",{type:"text",placeholder:"New Participant Name",value:l.name,onChange:function(t){return d((function(e){return rf(rf({},e),{},{name:t.target.value})}))}}),o().createElement("button",{className:"btn btn-cyber-primary",onClick:function(){return E("replace",{oldParticipantId:null==i?void 0:i.participant1Id})}},"Replace Participant"))),o().createElement("div",{className:"match-controls"},o().createElement("h3",null,"Match Controls"),i&&o().createElement("div",{className:"selected-match-controls"},o().createElement("h4",null,"Selected Match: ",i.id),o().createElement("div",{className:"control-buttons"},o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return b(i,"forceComplete")}},"Force Complete"),o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return b(i,"reset")}},"Reset Match"),o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return b(i,"setStatus",{status:"ready"})}},"Set Ready"),o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return b(i,"noShow",{noShowParticipantId:i.participant1Id})}},"Mark P1 No-Show"),o().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return b(i,"noShow",{noShowParticipantId:i.participant2Id})}},"Mark P2 No-Show")))))},{}})()));