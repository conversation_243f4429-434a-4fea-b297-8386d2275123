import React, { useState } from 'react';
import {
    pauseTournament,
    resumeTournament,
    forceStartTournament,
    forceEndTournament,
    resetTournament,
    forceCompleteMatch,
    resetMatch,
    overrideMatchResult,
    setMatchStatus,
    addParticipant,
    removeParticipant,
    replaceParticipant,
    handleNoShow
} from '../../services/adminControls';
import './AdminControlPanel.css';

const AdminControlPanel = ({ tournament, currentUserAddress }) => {
    const [selectedMatch, setSelectedMatch] = useState(null);
    const [newParticipant, setNewParticipant] = useState({ id: '', name: '' });
    const [replacementParticipant, setReplacementParticipant] = useState({ id: '', name: '' });
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);

    const handleTournamentAction = async (action) => {
        try {
            setError(null);
            setSuccess(null);

            switch (action) {
                case 'pause':
                    await pauseTournament(tournament.id);
                    setSuccess('Tournament paused successfully');
                    break;
                case 'resume':
                    await resumeTournament(tournament.id);
                    setSuccess('Tournament resumed successfully');
                    break;
                case 'forceStart':
                    await forceStartTournament(tournament.id);
                    setSuccess('Tournament started successfully');
                    break;
                case 'forceEnd':
                    await forceEndTournament(tournament.id);
                    setSuccess('Tournament ended successfully');
                    break;
                case 'reset':
                    if (window.confirm('Are you sure you want to reset the tournament? This will clear all match results.')) {
                        await resetTournament(tournament.id);
                        setSuccess('Tournament reset successfully');
                    }
                    break;
                default:
                    throw new Error('Invalid action');
            }
        } catch (error) {
            setError(error.message);
        }
    };

    const handleMatchAction = async (match, action, data = {}) => {
        try {
            setError(null);
            setSuccess(null);

            switch (action) {
                case 'forceComplete':
                    await forceCompleteMatch(tournament.id, match.id, {
                        ...data,
                        adminId: currentUserAddress
                    });
                    setSuccess('Match completed successfully');
                    break;
                case 'reset':
                    await resetMatch(tournament.id, match.id);
                    setSuccess('Match reset successfully');
                    break;
                case 'override':
                    await overrideMatchResult(tournament.id, match.id, {
                        ...data,
                        adminId: currentUserAddress
                    });
                    setSuccess('Match result overridden successfully');
                    break;
                case 'setStatus':
                    await setMatchStatus(tournament.id, match.id, data.status);
                    setSuccess('Match status updated successfully');
                    break;
                case 'noShow':
                    await handleNoShow(tournament.id, match.id, data.noShowParticipantId);
                    setSuccess('No-show handled successfully');
                    break;
                default:
                    throw new Error('Invalid action');
            }
        } catch (error) {
            setError(error.message);
        }
    };

    const handleParticipantAction = async (action, data = {}) => {
        try {
            setError(null);
            setSuccess(null);

            switch (action) {
                case 'add':
                    await addParticipant(tournament.id, newParticipant.id);
                    setNewParticipant({ id: '', name: '' });
                    setSuccess('Participant added successfully');
                    break;
                case 'remove':
                    await removeParticipant(tournament.id, data.participantId);
                    setSuccess('Participant removed successfully');
                    break;
                case 'replace':
                    await replaceParticipant(tournament.id, data.oldParticipantId, replacementParticipant.id);
                    setReplacementParticipant({ id: '', name: '' });
                    setSuccess('Participant replaced successfully');
                    break;
                default:
                    throw new Error('Invalid action');
            }
        } catch (error) {
            setError(error.message);
        }
    };

    return (
        <div className="admin-control-panel">
            <h2>Tournament Admin Controls</h2>

            {error && (
                <div className="error-message">
                    {error}
                </div>
            )}

            {success && (
                <div className="success-message">
                    {success}
                </div>
            )}

            <div className="tournament-controls">
                <h3>Tournament Controls</h3>
                <div className="control-buttons">
                    {tournament.status === 'live' && (
                        <button
                            className="btn btn-cyber-secondary"
                            onClick={() => handleTournamentAction('pause')}
                        >
                            Pause Tournament
                        </button>
                    )}
                    {tournament.status === 'paused' && (
                        <button
                            className="btn btn-cyber-secondary"
                            onClick={() => handleTournamentAction('resume')}
                        >
                            Resume Tournament
                        </button>
                    )}
                    {tournament.status === 'upcoming' && (
                        <button
                            className="btn btn-cyber-secondary"
                            onClick={() => handleTournamentAction('forceStart')}
                        >
                            Force Start
                        </button>
                    )}
                    {tournament.status === 'live' && (
                        <button
                            className="btn btn-cyber-secondary"
                            onClick={() => handleTournamentAction('forceEnd')}
                        >
                            Force End
                        </button>
                    )}
                    <button
                        className="btn btn-cyber-danger"
                        onClick={() => handleTournamentAction('reset')}
                    >
                        Reset Tournament
                    </button>
                </div>
            </div>

            <div className="participant-controls">
                <h3>Participant Management</h3>
                <div className="add-participant">
                    <input
                        type="text"
                        placeholder="Participant ID"
                        value={newParticipant.id}
                        onChange={(e) => setNewParticipant(prev => ({ ...prev, id: e.target.value }))}
                    />
                    <input
                        type="text"
                        placeholder="Participant Name"
                        value={newParticipant.name}
                        onChange={(e) => setNewParticipant(prev => ({ ...prev, name: e.target.value }))}
                    />
                    <button
                        className="btn btn-cyber-primary"
                        onClick={() => handleParticipantAction('add')}
                    >
                        Add Participant
                    </button>
                </div>

                <div className="replace-participant">
                    <input
                        type="text"
                        placeholder="New Participant ID"
                        value={replacementParticipant.id}
                        onChange={(e) => setReplacementParticipant(prev => ({ ...prev, id: e.target.value }))}
                    />
                    <input
                        type="text"
                        placeholder="New Participant Name"
                        value={replacementParticipant.name}
                        onChange={(e) => setReplacementParticipant(prev => ({ ...prev, name: e.target.value }))}
                    />
                    <button
                        className="btn btn-cyber-primary"
                        onClick={() => handleParticipantAction('replace', { oldParticipantId: selectedMatch?.participant1Id })}
                    >
                        Replace Participant
                    </button>
                </div>
            </div>

            <div className="match-controls">
                <h3>Match Controls</h3>
                {selectedMatch && (
                    <div className="selected-match-controls">
                        <h4>Selected Match: {selectedMatch.id}</h4>
                        <div className="control-buttons">
                            <button
                                className="btn btn-cyber-secondary"
                                onClick={() => handleMatchAction(selectedMatch, 'forceComplete')}
                            >
                                Force Complete
                            </button>
                            <button
                                className="btn btn-cyber-secondary"
                                onClick={() => handleMatchAction(selectedMatch, 'reset')}
                            >
                                Reset Match
                            </button>
                            <button
                                className="btn btn-cyber-secondary"
                                onClick={() => handleMatchAction(selectedMatch, 'setStatus', { status: 'ready' })}
                            >
                                Set Ready
                            </button>
                            <button
                                className="btn btn-cyber-secondary"
                                onClick={() => handleMatchAction(selectedMatch, 'noShow', {
                                    noShowParticipantId: selectedMatch.participant1Id
                                })}
                            >
                                Mark P1 No-Show
                            </button>
                            <button
                                className="btn btn-cyber-secondary"
                                onClick={() => handleMatchAction(selectedMatch, 'noShow', {
                                    noShowParticipantId: selectedMatch.participant2Id
                                })}
                            >
                                Mark P2 No-Show
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AdminControlPanel;