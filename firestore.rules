rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /tournaments/{tournamentId} {
      // Anyone can read tournaments
      allow read: if true;

      // Anyone can create tournaments (with basic validation)
      allow create: if request.resource.data.keys().hasAll(['tournamentName', 'gameName', 'creatorAddress', 'createdAt', 'status', 'participants', 'participantCount'])
        && request.resource.data.creatorAddress is string
        && request.resource.data.createdAt is timestamp
        && request.resource.data.status == 'upcoming'
        && request.resource.data.participants is list
        && request.resource.data.participantCount == 0;

      // Allow updates for authenticated users with specific restrictions
      allow update: if request.auth != null && (
        // Authenticated users can join tournaments (update participants/teamParticipants and related fields)
        (request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['participants', 'participantCount']) ||
         request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['participants', 'participantCount', 'transactionHashes']) ||
         request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['teamParticipants', 'participantCount']) ||
         request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['teamParticipants', 'participantCount', 'transactionHashes']))
        ||
        // Any authenticated user can update match results in bracketData
        (request.resource.data.diff(resource.data).affectedKeys()
           .hasOnly(['bracketData']))
        ||
        // Allow status updates for tournament progression (registration closing, going live, etc.)
        (request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['status', 'registrationClosedAt', 'bracketData']))
      );
    }

    match /notifications/{notificationId} {
      // Allow authenticated users to read all notifications
      // (Client-side filtering by wallet address handles user-specific access)
      allow read: if request.auth != null;

      // Allow authenticated users to update notifications
      // (Client-side validation ensures users only update their own notifications)
      allow update: if request.auth != null &&
        request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['isRead', 'readAt']);

      // Allow authenticated users to delete notifications
      // (Client-side validation ensures users only delete their own notifications)
      allow delete: if request.auth != null;

      // System can create notifications (via Cloud Functions and authenticated users)
      allow create: if request.auth != null;
    }
  }
}