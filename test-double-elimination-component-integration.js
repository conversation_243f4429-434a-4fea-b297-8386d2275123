// Test Double Elimination Component Integration
// Verifies that the generated bracket data is compatible with the React components
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== DOUBLE ELIMINATION COMPONENT INTEGRATION TEST ===\n');

// Simulate the data structures that would be passed to the React components
function testComponentIntegration(participants, testName, isTeamTournament = false) {
    console.log(`\n=== TESTING: ${testName} ===`);
    
    try {
        // Generate bracket data
        const bracketData = generateBracket(participants, 'double-elimination', { matchFormat: 'bo3' });
        
        // Simulate tournament data as it would exist in Firestore
        const tournamentData = {
            tournamentName: testName,
            gameName: 'Axie Infinity: Origins',
            tournamentFormat: isTeamTournament ? '2v2' : 'single-elim',
            bracketFormat: isTeamTournament ? 'double-elim' : undefined,
            matchFormat: 'bo3',
            creatorAddress: '******************************************',
            status: 'active',
            bracketData: bracketData
        };
        
        // Test data structure compatibility with DoubleEliminationBracket component
        console.log('Testing component data compatibility...');
        
        // Verify required props for DoubleEliminationBracket component
        const requiredProps = {
            data: bracketData,
            tournamentId: 'test-tournament-id',
            currentUserAddress: '******************************************',
            isAdmin: true
        };
        
        // Test bracket data structure
        if (!bracketData.rounds || !Array.isArray(bracketData.rounds)) {
            throw new Error('Missing or invalid rounds array');
        }
        
        if (!bracketData.matchesById || typeof bracketData.matchesById !== 'object') {
            throw new Error('Missing or invalid matchesById object');
        }
        
        if (!bracketData.participants || !Array.isArray(bracketData.participants)) {
            throw new Error('Missing or invalid participants array');
        }
        
        // Test getTournamentWinner logic compatibility
        console.log('Testing tournament winner detection...');
        const grandFinalMatch = Object.values(bracketData.matchesById).find(match =>
            match.identifier === "Grand Final" || match.identifier === "Grand Final Reset"
        );
        
        if (!grandFinalMatch) {
            throw new Error('No Grand Final match found for winner detection');
        }
        
        // Test bracket section separation (upper vs lower bracket)
        console.log('Testing bracket section separation...');
        const upperBracketRounds = bracketData.rounds.filter(round => 
            round.matches.some(match => match.isUpperBracket)
        );
        
        const lowerBracketRounds = bracketData.rounds.filter(round => 
            round.matches.some(match => !match.isUpperBracket && !match.identifier?.includes('Grand Final'))
        );
        
        const grandFinalRounds = bracketData.rounds.filter(round => 
            round.matches.some(match => match.identifier?.includes('Grand Final'))
        );
        
        console.log(`  Upper Bracket Rounds: ${upperBracketRounds.length}`);
        console.log(`  Lower Bracket Rounds: ${lowerBracketRounds.length}`);
        console.log(`  Grand Final Rounds: ${grandFinalRounds.length}`);
        
        if (upperBracketRounds.length === 0) {
            throw new Error('No upper bracket rounds found');
        }
        
        if (lowerBracketRounds.length === 0) {
            throw new Error('No lower bracket rounds found');
        }
        
        if (grandFinalRounds.length === 0) {
            throw new Error('No grand final rounds found');
        }
        
        // Test match rendering compatibility
        console.log('Testing match rendering compatibility...');
        let matchesWithParticipants = 0;
        let matchesWithProgression = 0;
        
        Object.values(bracketData.matchesById).forEach(match => {
            // Check if match has the required properties for rendering
            const requiredMatchProps = ['id', 'participant1Id', 'participant2Id', 'status'];
            requiredMatchProps.forEach(prop => {
                if (!(prop in match)) {
                    throw new Error(`Match ${match.id} missing required property: ${prop}`);
                }
            });
            
            if (match.participant1Id || match.participant2Id) {
                matchesWithParticipants++;
            }
            
            if (match.nextMatchId || match.nextLoserMatchId) {
                matchesWithProgression++;
            }
        });
        
        console.log(`  Matches with participants: ${matchesWithParticipants}`);
        console.log(`  Matches with progression: ${matchesWithProgression}`);
        
        // Test admin functionality compatibility
        console.log('Testing admin functionality compatibility...');
        const adminCapableMatches = Object.values(bracketData.matchesById).filter(match => 
            match.status === 'PENDING' && (match.participant1Id && match.participant2Id)
        );
        
        console.log(`  Admin-manageable matches: ${adminCapableMatches.length}`);
        
        // Test team tournament specific features
        if (isTeamTournament) {
            console.log('Testing team tournament specific features...');
            const teamParticipants = bracketData.participants.filter(p => p.members && Array.isArray(p.members));
            console.log(`  Team participants: ${teamParticipants.length}`);
            
            if (teamParticipants.length === 0) {
                throw new Error('No team participants found in team tournament');
            }
        }
        
        console.log('✅ Component integration test passed!');
        return true;
        
    } catch (error) {
        console.log(`❌ Component integration test failed: ${error.message}`);
        return false;
    }
}

// Test data
const individualParticipants = [
    { id: '******************************************', name: 'Player Alpha', walletAddress: '******************************************', seed: 1 },
    { id: '******************************************', name: 'Player Beta', walletAddress: '******************************************', seed: 2 },
    { id: '******************************************', name: 'Player Gamma', walletAddress: '******************************************', seed: 3 },
    { id: '0x456789013def123456789013def12345678abcd', name: 'Player Delta', walletAddress: '0x456789013def123456789013def12345678abcd', seed: 4 }
];

const teamParticipants = [
    { 
        id: 'team_alpha', 
        name: 'Team Alpha', 
        members: ['******************************************', '******************************************'],
        captain: '******************************************',
        seed: 1 
    },
    { 
        id: 'team_beta', 
        name: 'Team Beta', 
        members: ['******************************************', '0x456789013def123456789013def12345678abcd'],
        captain: '******************************************',
        seed: 2 
    },
    { 
        id: 'team_gamma', 
        name: 'Team Gamma', 
        members: ['0x567890124ef123456789014ef12345678abcde', '0x6789012345f123456789015f12345678abcdef'],
        captain: '0x567890124ef123456789014ef12345678abcde',
        seed: 3 
    },
    { 
        id: 'team_delta', 
        name: 'Team Delta', 
        members: ['0x789012346f123456789016f12345678abcdef0', '0x89012347f123456789017f12345678abcdef01'],
        captain: '0x789012346f123456789016f12345678abcdef0',
        seed: 4 
    }
];

// Run component integration tests
const testResults = {
    'Individual Tournament Component Integration': testComponentIntegration(
        individualParticipants, 
        'Individual Double Elimination Tournament', 
        false
    ),
    'Team Tournament Component Integration': testComponentIntegration(
        teamParticipants, 
        '2v2 Team Double Elimination Tournament', 
        true
    )
};

// Test Summary
console.log('\n=== COMPONENT INTEGRATION TEST SUMMARY ===');
const passed = Object.values(testResults).filter(Boolean).length;
const total = Object.keys(testResults).length;

console.log(`\nPassed: ${passed}/${total}`);
Object.entries(testResults).forEach(([testName, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${testName}`);
});

if (passed === total) {
    console.log('\n🎉 All component integration tests passed!');
    console.log('✅ Bracket data is compatible with DoubleEliminationBracket component');
    console.log('✅ Tournament winner detection will work correctly');
    console.log('✅ Upper/Lower bracket separation works correctly');
    console.log('✅ Match rendering will work correctly');
    console.log('✅ Admin functionality will work correctly');
    console.log('✅ Team tournament features are preserved');
    console.log('✅ All required data structures are present and valid');
} else {
    console.log(`\n⚠️  ${total - passed} test(s) failed - Component integration needs attention`);
    process.exit(1);
}
