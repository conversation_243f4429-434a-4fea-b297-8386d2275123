.bracket-loading,
.bracket-error {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-family: 'Orbitron', sans-serif;
    background-color: var(--bg-element-dark);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    margin: 1rem 0;
}

.bracket-error {
    color: #ff8080;
    border-color: rgba(255, 80, 80, 0.5);
}

/* Bracket Container */
.bracket-container {
    background-color: var(--bg-element-dark);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3), 0 0 10px rgba(var(--accent-blue-rgb), 0.15);
}

/* Match Box */
.match-box {
    background-color: var(--bg-element-medium);
    border: 1px solid var(--border-cyber);
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 0.5rem;
    transition: all 0.3s ease;
}

.match-box:hover {
    border-color: var(--accent-blue);
    box-shadow: 0 0 15px rgba(var(--accent-blue-rgb), 0.3);
}

/* Player Names */
.player-name {
    font-family: 'Orbitron', sans-serif;
    font-size: 0.9rem;
    color: var(--text-primary);
    margin: 0.25rem 0;
}

.player-name.winner {
    color: var(--accent-cyan);
    text-shadow: 0 0 8px rgba(var(--accent-cyan-rgb), 0.6);
}

.player-name.loser {
    color: var(--text-secondary);
    opacity: 0.7;
}

/* Score Display */
.score-display {
    font-family: 'Orbitron', sans-serif;
    font-weight: 700;
    color: var(--accent-magenta);
    text-shadow: 0 0 8px rgba(var(--accent-magenta-rgb), 0.6);
    margin: 0.5rem 0;
}

/* Round Labels */
.round-label {
    font-family: 'Orbitron', sans-serif;
    color: var(--accent-cyan);
    text-transform: uppercase;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    text-shadow: 0 0 8px rgba(var(--accent-cyan-rgb), 0.6);
}

/* Match Status */
.match-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    margin-top: 0.5rem;
    display: inline-block;
}

.match-status.upcoming {
    background-color: rgba(var(--accent-blue-rgb), 0.2);
    color: var(--accent-blue);
    border: 1px solid rgba(var(--accent-blue-rgb), 0.5);
}

.match-status.live {
    background-color: rgba(255, 80, 80, 0.2);
    color: #ff8080;
    border: 1px solid rgba(255, 80, 80, 0.5);
}

.match-status.completed {
    background-color: rgba(60, 200, 130, 0.2);
    color: #80FFC0;
    border: 1px solid rgba(60, 200, 130, 0.5);
}

/* Bracket Lines */
.bracket-line {
    position: absolute;
    background-color: var(--border-cyber);
    z-index: 1;
}

.bracket-line.vertical {
    width: 2px;
    background: linear-gradient(to bottom, var(--accent-cyan), var(--accent-magenta));
}

.bracket-line.horizontal {
    height: 2px;
    background: linear-gradient(to right, var(--accent-cyan), var(--accent-magenta));
}

/* Responsive Design */
@media (max-width: 768px) {
    .bracket-container {
        padding: 1rem;
        overflow-x: auto;
    }

    .match-box {
        padding: 0.75rem;
        margin: 0.25rem;
    }

    .player-name {
        font-size: 0.8rem;
    }
} 