// Test file for match format score validation
// Tests the validateMatchFormatScore function for all match formats and edge cases

// Import the validation function (simulated for testing)
function validateMatchFormatScore(score, winnerId, matchFormat, participant1Id, participant2Id) {
    // Check if required parameters are provided
    if (!score || typeof score.participant1 !== 'number' || typeof score.participant2 !== 'number') {
        return { isValid: false, error: 'Valid scores for both participants are required' };
    }

    if (!winnerId || !matchFormat || !participant1Id || !participant2Id) {
        return { isValid: false, error: 'Missing required match information for validation' };
    }

    // Normalize match format
    const format = matchFormat.toLowerCase();
    const validFormats = ['bo1', 'bo3', 'bo5', 'bo7'];
    
    if (!validFormats.includes(format)) {
        return { isValid: false, error: `Invalid match format: ${matchFormat}. Must be one of: ${validFormats.join(', ')}` };
    }

    const participant1Score = score.participant1;
    const participant2Score = score.participant2;
    const totalGames = participant1Score + participant2Score;

    // Extract max games from format (e.g., 'bo3' -> 3)
    const maxGames = parseInt(format.substring(2));
    const firstToWin = Math.ceil(maxGames / 2); // First to win majority

    // Check for tied scores first (no clear winner possible)
    if (participant1Score === participant2Score) {
        return { isValid: false, error: `Tied scores are not allowed in ${format.toUpperCase()} matches. One participant must have a clear majority.` };
    }

    // Determine winner based on scores
    const scoreBasedWinner = participant1Score > participant2Score ? participant1Id : participant2Id;

    // Check if declared winner matches score-based winner
    if (winnerId !== scoreBasedWinner) {
        return { isValid: false, error: 'Declared winner does not match the scores provided' };
    }

    // Validate based on match format
    switch (format) {
        case 'bo1':
            // Best of 1: Exactly 1 game, winner has 1, loser has 0
            if (totalGames !== 1) {
                return { isValid: false, error: 'Best of 1 matches must have exactly 1 game played' };
            }
            if (Math.max(participant1Score, participant2Score) !== 1 || Math.min(participant1Score, participant2Score) !== 0) {
                return { isValid: false, error: 'Best of 1 matches must have a score of 1-0' };
            }
            break;

        case 'bo3':
            // Best of 3: At least 2 games, winner has at least 2, max 3 total games
            if (totalGames < 2 || totalGames > 3) {
                return { isValid: false, error: 'Best of 3 matches must have 2-3 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 2) {
                return { isValid: false, error: 'Best of 3 matches require the winner to win at least 2 games' };
            }
            break;

        case 'bo5':
            // Best of 5: At least 3 games, winner has at least 3, max 5 total games
            if (totalGames < 3 || totalGames > 5) {
                return { isValid: false, error: 'Best of 5 matches must have 3-5 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 3) {
                return { isValid: false, error: 'Best of 5 matches require the winner to win at least 3 games' };
            }
            break;

        case 'bo7':
            // Best of 7: At least 4 games, winner has at least 4, max 7 total games
            if (totalGames < 4 || totalGames > 7) {
                return { isValid: false, error: 'Best of 7 matches must have 4-7 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 4) {
                return { isValid: false, error: 'Best of 7 matches require the winner to win at least 4 games' };
            }
            break;
    }

    // Additional validation: ensure winner has won the majority
    const winnerScore = winnerId === participant1Id ? participant1Score : participant2Score;
    if (winnerScore < firstToWin) {
        return { isValid: false, error: `Winner must have won at least ${firstToWin} games in a ${format.toUpperCase()} match` };
    }

    return { isValid: true };
}

console.log('=== MATCH FORMAT SCORE VALIDATION TESTS ===\n');

// Test data
const participant1Id = '0x1234567890abcdef1234567890abcdef12345678';
const participant2Id = '0x2345678901bcdef12345678901bcdef123456789';

// Test helper function
function runTest(testName, score, winnerId, matchFormat, expectedValid, expectedErrorContains = null) {
    console.log(`Testing: ${testName}`);
    const result = validateMatchFormatScore(score, winnerId, matchFormat, participant1Id, participant2Id);
    
    if (result.isValid === expectedValid) {
        if (expectedValid) {
            console.log('✅ PASS - Valid score accepted');
        } else {
            if (expectedErrorContains && result.error.includes(expectedErrorContains)) {
                console.log(`✅ PASS - Invalid score rejected: ${result.error}`);
            } else if (!expectedErrorContains) {
                console.log(`✅ PASS - Invalid score rejected: ${result.error}`);
            } else {
                console.log(`❌ FAIL - Wrong error message. Expected to contain "${expectedErrorContains}", got: ${result.error}`);
            }
        }
    } else {
        console.log(`❌ FAIL - Expected ${expectedValid ? 'valid' : 'invalid'}, got ${result.isValid ? 'valid' : 'invalid'}`);
        if (result.error) console.log(`   Error: ${result.error}`);
    }
    console.log('');
}

// === BEST OF 1 TESTS ===
console.log('--- BEST OF 1 TESTS ---');

// Valid BO1 scenarios
runTest('BO1: Valid 1-0 score (P1 wins)', { participant1: 1, participant2: 0 }, participant1Id, 'bo1', true);
runTest('BO1: Valid 0-1 score (P2 wins)', { participant1: 0, participant2: 1 }, participant2Id, 'bo1', true);

// Invalid BO1 scenarios
runTest('BO1: Invalid 2-0 score (too many games)', { participant1: 2, participant2: 0 }, participant1Id, 'bo1', false, 'exactly 1 game');
runTest('BO1: Invalid 1-1 score (tie)', { participant1: 1, participant2: 1 }, participant1Id, 'bo1', false, 'Tied scores are not allowed');
runTest('BO1: Invalid 0-0 score (no games)', { participant1: 0, participant2: 0 }, participant1Id, 'bo1', false, 'Tied scores are not allowed');

// === BEST OF 3 TESTS ===
console.log('--- BEST OF 3 TESTS ---');

// Valid BO3 scenarios
runTest('BO3: Valid 2-0 score (P1 wins)', { participant1: 2, participant2: 0 }, participant1Id, 'bo3', true);
runTest('BO3: Valid 2-1 score (P1 wins)', { participant1: 2, participant2: 1 }, participant1Id, 'bo3', true);
runTest('BO3: Valid 1-2 score (P2 wins)', { participant1: 1, participant2: 2 }, participant2Id, 'bo3', true);
runTest('BO3: Valid 0-2 score (P2 wins)', { participant1: 0, participant2: 2 }, participant2Id, 'bo3', true);

// Invalid BO3 scenarios
runTest('BO3: Invalid 1-0 score (not enough games)', { participant1: 1, participant2: 0 }, participant1Id, 'bo3', false, '2-3 games');
runTest('BO3: Invalid 3-1 score (too many games)', { participant1: 3, participant2: 1 }, participant1Id, 'bo3', false, '2-3 games');
runTest('BO3: Invalid 1-1 score (no winner)', { participant1: 1, participant2: 1 }, participant1Id, 'bo3', false, 'Tied scores are not allowed');

// === BEST OF 5 TESTS ===
console.log('--- BEST OF 5 TESTS ---');

// Valid BO5 scenarios
runTest('BO5: Valid 3-0 score (P1 wins)', { participant1: 3, participant2: 0 }, participant1Id, 'bo5', true);
runTest('BO5: Valid 3-1 score (P1 wins)', { participant1: 3, participant2: 1 }, participant1Id, 'bo5', true);
runTest('BO5: Valid 3-2 score (P1 wins)', { participant1: 3, participant2: 2 }, participant1Id, 'bo5', true);
runTest('BO5: Valid 2-3 score (P2 wins)', { participant1: 2, participant2: 3 }, participant2Id, 'bo5', true);

// Invalid BO5 scenarios
runTest('BO5: Invalid 2-0 score (not enough games)', { participant1: 2, participant2: 0 }, participant1Id, 'bo5', false, '3-5 games');
runTest('BO5: Invalid 4-2 score (too many games)', { participant1: 4, participant2: 2 }, participant1Id, 'bo5', false, '3-5 games');
runTest('BO5: Invalid 2-2 score (no winner)', { participant1: 2, participant2: 2 }, participant1Id, 'bo5', false, 'Tied scores are not allowed');

// === BEST OF 7 TESTS ===
console.log('--- BEST OF 7 TESTS ---');

// Valid BO7 scenarios
runTest('BO7: Valid 4-0 score (P1 wins)', { participant1: 4, participant2: 0 }, participant1Id, 'bo7', true);
runTest('BO7: Valid 4-1 score (P1 wins)', { participant1: 4, participant2: 1 }, participant1Id, 'bo7', true);
runTest('BO7: Valid 4-3 score (P1 wins)', { participant1: 4, participant2: 3 }, participant1Id, 'bo7', true);
runTest('BO7: Valid 3-4 score (P2 wins)', { participant1: 3, participant2: 4 }, participant2Id, 'bo7', true);

// Invalid BO7 scenarios
runTest('BO7: Invalid 3-0 score (not enough games)', { participant1: 3, participant2: 0 }, participant1Id, 'bo7', false, '4-7 games');
runTest('BO7: Invalid 5-3 score (too many games)', { participant1: 5, participant2: 3 }, participant1Id, 'bo7', false, '4-7 games');
runTest('BO7: Invalid 3-3 score (no winner)', { participant1: 3, participant2: 3 }, participant1Id, 'bo7', false, 'Tied scores are not allowed');

// === EDGE CASE TESTS ===
console.log('--- EDGE CASE TESTS ---');

// Wrong winner declared
runTest('Wrong winner: P1 declared winner but P2 has higher score', { participant1: 1, participant2: 2 }, participant1Id, 'bo3', false, 'does not match the scores');

// Invalid match format
runTest('Invalid format: bo2', { participant1: 2, participant2: 0 }, participant1Id, 'bo2', false, 'Invalid match format');
runTest('Invalid format: bo9', { participant1: 5, participant2: 4 }, participant1Id, 'bo9', false, 'Invalid match format');

// Missing parameters
runTest('Missing score', null, participant1Id, 'bo3', false, 'Valid scores');
runTest('Missing winner', { participant1: 2, participant2: 1 }, null, 'bo3', false, 'Missing required');
runTest('Missing match format', { participant1: 2, participant2: 1 }, participant1Id, null, false, 'Missing required');

// Invalid score types
runTest('Non-numeric scores', { participant1: 'two', participant2: 1 }, participant1Id, 'bo3', false, 'Valid scores');
runTest('Negative scores', { participant1: -1, participant2: 2 }, participant2Id, 'bo3', false, 'Best of 3 matches must have 2-3 games');

console.log('=== MATCH FORMAT VALIDATION TESTS COMPLETED ===');
