.bracket-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 1rem;
    overflow-x: auto;
    width: 100%;
}

/* Single Elimination Styles */
.bracket-container.single-elimination {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    overflow-x: auto;
    padding-bottom: 1rem;
    min-height: 400px;
    width: 100%;
}

.bracket-container.single-elimination .bracket-content {
    display: flex;
    flex-direction: row;
    gap: 2rem;
    padding: 1rem;
    min-width: min-content;
}

/* Double Elimination Styles */
.bracket-container.double-elimination {
    display: flex;
    flex-direction: column;
}

.bracket-section {
    margin-bottom: 2rem;
}

.bracket-section h2 {
    color: var(--accent-cyan);
    margin-bottom: 1rem;
    font-family: 'Orbitron', sans-serif;
}

/* Round Styles */
.round {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-width: 250px;
    margin-right: 2rem;
    flex-shrink: 0;
}

.round-header {
    text-align: center;
    margin-bottom: 0.5rem;
}

.round-header h3 {
    color: var(--accent-magenta);
    font-size: 1.1rem;
    font-family: 'Orbitron', sans-serif;
}

/* Match Styles */
.match-container {
    background-color: var(--bg-element-dark);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.match-container:hover {
    border-color: var(--accent-blue);
    box-shadow: 0 0 15px rgba(var(--accent-blue-rgb), 0.3);
}

.match-header {
    background-color: var(--bg-element-medium);
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-cyber);
}

.match-id {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-family: 'Orbitron', sans-serif;
}

.match-content {
    padding: 0.75rem;
}

.participant {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;
    background-color: var(--bg-element-medium);
}

.participant:last-child {
    margin-bottom: 0;
}

.participant.winner {
    background-color: rgba(60, 200, 130, 0.2);
    border: 1px solid rgba(60, 200, 130, 0.5);
}

.participant-name {
    font-size: 0.9rem;
    color: var(--text-primary);
}

.score {
    font-family: 'Orbitron', sans-serif;
    color: var(--accent-cyan);
    font-weight: bold;
}

/* Status-specific styles */
.match-container.pending {
    border-color: var(--border-cyber);
}

.match-container.bye {
    border-color: var(--accent-cyan);
    opacity: 0.7;
}

.match-container.participant1_win,
.match-container.participant2_win {
    border-color: var(--accent-magenta);
}

/* Round Robin specific styles */
.standings-section {
    margin-bottom: 2rem;
}

.standings-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-element-dark);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    overflow: hidden;
}

.standings-table th,
.standings-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-cyber);
}

.standings-table th {
    background-color: var(--bg-element-medium);
    color: var(--accent-cyan);
    font-family: 'Orbitron', sans-serif;
}

.standings-table tr:last-child td {
    border-bottom: none;
}

.standings-table tr:hover {
    background-color: var(--bg-element-medium);
}

/* Loading and Error States */
.bracket-loading,
.bracket-error {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.bracket-error {
    color: #ff6b6b;
}

/* Match Result Submission Styles */
.match-result-submission {
    background-color: var(--bg-element-dark);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.match-result-submission h4 {
    color: var(--accent-cyan);
    margin-bottom: 1rem;
}

.score-inputs {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.score-inputs input {
    width: 80px;
    text-align: center;
}

.winner-selection {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.winner-selection button {
    width: 100%;
    text-align: center;
}

.admin-resolve {
    border-top: 1px solid var(--border-cyber);
    padding-top: 1rem;
    margin-top: 1rem;
}

.admin-resolve h5 {
    color: var(--accent-magenta);
    margin-bottom: 0.5rem;
}

.error-message {
    background-color: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
    padding: 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

.warning-message {
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    padding: 0.75rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    color: #ffc107;
    font-size: 0.9rem;
}

.warning-message h3 {
    margin: 0 0 0.5rem 0;
    color: #ffc107;
    font-size: 1rem;
}

.warning-message p {
    margin: 0.25rem 0;
    line-height: 1.4;
}

/* Match Details Panel */
.match-details-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-element-dark);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    z-index: 1000;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

.close-panel-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    line-height: 1;
}

.close-panel-btn:hover {
    color: var(--accent-cyan);
}

/* Match Status Badges */
.dispute-badge {
    background-color: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 1px solid rgba(255, 107, 107, 0.3);
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

.match-container.selected {
    border-color: var(--accent-cyan);
    box-shadow: 0 0 15px rgba(var(--accent-cyan-rgb), 0.3);
}

.match-actions {
    padding: 0.75rem;
    border-top: 1px solid var(--border-cyber);
    display: flex;
    justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .bracket-container {
        padding: 0.5rem;
    }

    .round {
        min-width: 200px;
        margin-right: 1rem;
    }

    .participant-name {
        font-size: 0.8rem;
    }

    .standings-table {
        font-size: 0.9rem;
    }

    .match-details-panel {
        width: 95%;
        padding: 1.5rem;
    }

    .score-inputs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .admin-resolve .flex {
        flex-direction: column;
    }
}

/* Tournament Winner Display */
.tournament-winner {
    background: linear-gradient(135deg, var(--bg-element-dark), var(--bg-element-medium));
    border: 2px solid var(--accent-cyan);
    border-radius: 0.5rem;
    padding: 2rem;
    margin: 2rem auto;
    text-align: center;
    max-width: 600px;
    box-shadow: 0 0 20px rgba(var(--accent-cyan-rgb), 0.3);
    animation: winnerGlow 2s infinite alternate;
}

.tournament-winner h2 {
    color: var(--accent-cyan);
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
}

.tournament-winner .winner-name {
    color: var(--accent-magenta);
    font-size: 2rem;
    font-weight: bold;
    margin: 1rem 0;
    text-shadow: 0 0 10px rgba(var(--accent-magenta-rgb), 0.6);
}

.tournament-winner .winner-trophy {
    font-size: 3rem;
    margin: 1rem 0;
    color: gold;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
}

@keyframes winnerGlow {
    from {
        box-shadow: 0 0 20px rgba(var(--accent-cyan-rgb), 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(var(--accent-cyan-rgb), 0.5);
    }
} 