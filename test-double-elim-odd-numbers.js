// Test Double Elimination with odd numbers of participants
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== TESTING DOUBLE ELIMINATION WITH ODD NUMBERS ===\n');

// Test with 3 players
console.log('=== 3 PLAYERS ===');
const participants3 = [
    { id: 'alpha', name: 'Alpha', seed: 1 },
    { id: 'bravo', name: '<PERSON>', seed: 2 },
    { id: 'charlie', name: '<PERSON>', seed: 3 }
];

try {
    const bracket3 = generateBracket(participants3, 'double-elimination');
    console.log(`Participants: ${bracket3.participants.length}`);
    console.log(`Rounds: ${bracket3.rounds.length}`);
    console.log(`Total Matches: ${Object.keys(bracket3.matchesById).length}`);
    console.log(`Upper Rounds: ${bracket3.metadata.numUpperRounds}`);
    console.log(`Lower Rounds: ${bracket3.metadata.numLowerRounds}`);
    
    console.log('\nRounds breakdown:');
    bracket3.rounds.forEach((round, index) => {
        console.log(`  Round ${index + 1}: ${round.name} - ${round.matches.length} matches`);
        round.matches.forEach(match => {
            const p1Name = bracket3.participants.find(p => p.id === match.participant1Id)?.name || match.participant1Id || 'TBD';
            const p2Name = bracket3.participants.find(p => p.id === match.participant2Id)?.name || match.participant2Id || 'TBD';
            console.log(`    ${match.id}: ${p1Name} vs ${p2Name} (${match.status})`);
        });
    });
} catch (error) {
    console.log(`❌ Error with 3 players: ${error.message}`);
}

console.log('\n' + '='.repeat(50) + '\n');

// Test with 5 players
console.log('=== 5 PLAYERS ===');
const participants5 = [
    { id: 'alpha', name: 'Alpha', seed: 1 },
    { id: 'bravo', name: 'Bravo', seed: 2 },
    { id: 'charlie', name: 'Charlie', seed: 3 },
    { id: 'delta', name: 'Delta', seed: 4 },
    { id: 'echo', name: 'Echo', seed: 5 }
];

try {
    const bracket5 = generateBracket(participants5, 'double-elimination');
    console.log(`Participants: ${bracket5.participants.length}`);
    console.log(`Rounds: ${bracket5.rounds.length}`);
    console.log(`Total Matches: ${Object.keys(bracket5.matchesById).length}`);
    console.log(`Upper Rounds: ${bracket5.metadata.numUpperRounds}`);
    console.log(`Lower Rounds: ${bracket5.metadata.numLowerRounds}`);
    
    console.log('\nRounds breakdown:');
    bracket5.rounds.forEach((round, index) => {
        console.log(`  Round ${index + 1}: ${round.name} - ${round.matches.length} matches`);
        round.matches.forEach(match => {
            const p1Name = bracket5.participants.find(p => p.id === match.participant1Id)?.name || match.participant1Id || 'TBD';
            const p2Name = bracket5.participants.find(p => p.id === match.participant2Id)?.name || match.participant2Id || 'TBD';
            console.log(`    ${match.id}: ${p1Name} vs ${p2Name} (${match.status})`);
        });
    });
} catch (error) {
    console.log(`❌ Error with 5 players: ${error.message}`);
}

console.log('\n' + '='.repeat(50) + '\n');

// Test with 7 players
console.log('=== 7 PLAYERS ===');
const participants7 = [
    { id: 'alpha', name: 'Alpha', seed: 1 },
    { id: 'bravo', name: 'Bravo', seed: 2 },
    { id: 'charlie', name: 'Charlie', seed: 3 },
    { id: 'delta', name: 'Delta', seed: 4 },
    { id: 'echo', name: 'Echo', seed: 5 },
    { id: 'foxtrot', name: 'Foxtrot', seed: 6 },
    { id: 'golf', name: 'Golf', seed: 7 }
];

try {
    const bracket7 = generateBracket(participants7, 'double-elimination');
    console.log(`Participants: ${bracket7.participants.length}`);
    console.log(`Rounds: ${bracket7.rounds.length}`);
    console.log(`Total Matches: ${Object.keys(bracket7.matchesById).length}`);
    console.log(`Upper Rounds: ${bracket7.metadata.numUpperRounds}`);
    console.log(`Lower Rounds: ${bracket7.metadata.numLowerRounds}`);
    
    console.log('\nRounds breakdown:');
    bracket7.rounds.forEach((round, index) => {
        console.log(`  Round ${index + 1}: ${round.name} - ${round.matches.length} matches`);
        round.matches.forEach(match => {
            const p1Name = bracket7.participants.find(p => p.id === match.participant1Id)?.name || match.participant1Id || 'TBD';
            const p2Name = bracket7.participants.find(p => p.id === match.participant2Id)?.name || match.participant2Id || 'TBD';
            console.log(`    ${match.id}: ${p1Name} vs ${p2Name} (${match.status})`);
        });
    });
} catch (error) {
    console.log(`❌ Error with 7 players: ${error.message}`);
}

console.log('\n=== SUMMARY ===');
console.log('Testing double elimination brackets with odd numbers of participants');
console.log('Expected behavior: Should handle byes properly and create valid brackets');
