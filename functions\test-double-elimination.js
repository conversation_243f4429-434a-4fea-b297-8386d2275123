const { generateBracket } = require('./bracketGenerator');

// Test double elimination bracket generation
function testDoubleElimination() {
    console.log('=== TESTING DOUBLE ELIMINATION BRACKET ===\n');
    
    // Test with 4 participants
    const testParticipants = [
        { id: 'player1', name: 'Player 1', seed: 1 },
        { id: 'player2', name: 'Player 2', seed: 2 },
        { id: 'player3', name: 'Player 3', seed: 3 },
        { id: 'player4', name: 'Player 4', seed: 4 }
    ];
    
    try {
        const bracket = generateBracket(testParticipants, 'double-elimination');
        
        console.log('Bracket Format:', bracket.format);
        console.log('Total Participants:', bracket.participants.length);
        console.log('Total Rounds:', bracket.rounds.length);
        console.log('Total Matches:', Object.keys(bracket.matchesById).length);
        console.log('Metadata:', bracket.metadata);
        console.log('\n');
        
        // Display bracket structure
        console.log('=== BRACKET STRUCTURE ===');
        bracket.rounds.forEach((round, index) => {
            console.log(`\nRound ${index + 1}: ${round.name} (${round.id})`);
            round.matches.forEach(match => {
                console.log(`  Match ${match.id}:`);
                console.log(`    Participant 1: ${match.participant1Id || 'TBD'}`);
                console.log(`    Participant 2: ${match.participant2Id || 'TBD'}`);
                console.log(`    Status: ${match.status}`);
                console.log(`    Upper Bracket: ${match.isUpperBracket}`);
                console.log(`    Next Match: ${match.nextMatchId || 'None'}`);
                console.log(`    Next Loser Match: ${match.nextLoserMatchId || 'None'}`);
                console.log(`    Identifier: ${match.identifier || 'None'}`);
            });
        });
        
        // Test linking validation
        console.log('\n=== LINK VALIDATION ===');
        let linkErrors = 0;
        
        Object.values(bracket.matchesById).forEach(match => {
            // Check if nextMatchId exists
            if (match.nextMatchId && !bracket.matchesById[match.nextMatchId]) {
                console.error(`ERROR: Match ${match.id} links to non-existent match ${match.nextMatchId}`);
                linkErrors++;
            }
            
            // Check if nextLoserMatchId exists
            if (match.nextLoserMatchId && !bracket.matchesById[match.nextLoserMatchId]) {
                console.error(`ERROR: Match ${match.id} links to non-existent loser match ${match.nextLoserMatchId}`);
                linkErrors++;
            }
        });
        
        if (linkErrors === 0) {
            console.log('✅ All match links are valid');
        } else {
            console.log(`❌ Found ${linkErrors} link errors`);
        }
        
        // Test with 8 participants
        console.log('\n=== TESTING WITH 8 PARTICIPANTS ===');
        const testParticipants8 = [
            { id: 'player1', name: 'Player 1', seed: 1 },
            { id: 'player2', name: 'Player 2', seed: 2 },
            { id: 'player3', name: 'Player 3', seed: 3 },
            { id: 'player4', name: 'Player 4', seed: 4 },
            { id: 'player5', name: 'Player 5', seed: 5 },
            { id: 'player6', name: 'Player 6', seed: 6 },
            { id: 'player7', name: 'Player 7', seed: 7 },
            { id: 'player8', name: 'Player 8', seed: 8 }
        ];
        
        const bracket8 = generateBracket(testParticipants8, 'double-elimination');
        console.log('8-Player Bracket:');
        console.log('  Total Rounds:', bracket8.rounds.length);
        console.log('  Total Matches:', Object.keys(bracket8.matchesById).length);
        console.log('  Upper Bracket Rounds:', bracket8.metadata.numUpperRounds);
        console.log('  Lower Bracket Rounds:', bracket8.metadata.numLowerRounds);
        
        // Count matches by bracket
        let upperMatches = 0;
        let lowerMatches = 0;
        let grandFinalMatches = 0;
        
        Object.values(bracket8.matchesById).forEach(match => {
            if (match.identifier && match.identifier.includes('Grand Final')) {
                grandFinalMatches++;
            } else if (match.isUpperBracket) {
                upperMatches++;
            } else {
                lowerMatches++;
            }
        });
        
        console.log(`  Upper Bracket Matches: ${upperMatches}`);
        console.log(`  Lower Bracket Matches: ${lowerMatches}`);
        console.log(`  Grand Final Matches: ${grandFinalMatches}`);
        
    } catch (error) {
        console.error('Error generating double elimination bracket:', error);
    }
}

// Run the test
testDoubleElimination();
