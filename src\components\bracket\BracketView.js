import React, { useEffect, useState } from 'react';
import SingleEliminationBracket from './SingleEliminationBracket';
import DoubleEliminationBracket from './DoubleEliminationBracket';
import RoundRobinBracket from './RoundRobinBracket';
import './BracketView.css';

const BracketView = ({ tournamentId, currentUserAddress, bracketData, tournamentData }) => {
    const [localBracketData, setLocalBracketData] = useState(bracketData);

    // Update local state when props change
    useEffect(() => {
        setLocalBracketData(bracketData);
    }, [bracketData]);

    // Debug logging
    useEffect(() => {
        console.log('BracketView mounted/updated:', {
            tournamentId,
            currentUserAddress,
            bracketDataFormat: bracketData?.format,
            hasRounds: !!bracketData?.rounds,
            hasMatchesById: !!bracketData?.matchesById,
            hasParticipants: !!bracketData?.participants
        });
    }, [tournamentId, currentUserAddress, bracketData]);

    if (!localBracketData) {
        console.warn('BracketView: No bracket data available');
        return <div className="bracket-error">No bracket data available</div>;
    }

    if (!localBracketData.format) {
        console.error('BracketView: Missing format field in bracket data:', localBracketData);
        return <div className="bracket-error">Invalid bracket data: missing format</div>;
    }

    const isAdmin = currentUserAddress === tournamentData.creatorAddress;

    // Render the appropriate bracket type
    console.log('BracketView: Rendering bracket with format:', localBracketData.format);

    switch (localBracketData.format) {
        case 'single-elimination':
            console.log('BracketView: Rendering SingleEliminationBracket');
            return (
                <SingleEliminationBracket
                    data={localBracketData}
                    tournamentId={tournamentId}
                    currentUserAddress={currentUserAddress}
                    isAdmin={isAdmin}
                    onMatchUpdate={(updatedMatch) => {
                        // Update the local state with the new match data
                        setLocalBracketData(prevData => ({
                            ...prevData,
                            matchesById: {
                                ...prevData.matchesById,
                                [updatedMatch.id]: updatedMatch
                            }
                        }));
                    }}
                />
            );
        case 'double-elimination':
            console.log('BracketView: Rendering DoubleEliminationBracket');
            return (
                <DoubleEliminationBracket
                    data={localBracketData}
                    tournamentId={tournamentId}
                    currentUserAddress={currentUserAddress}
                    isAdmin={isAdmin}
                    onMatchUpdate={(updatedMatch) => {
                        setLocalBracketData(prevData => ({
                            ...prevData,
                            matchesById: {
                                ...prevData.matchesById,
                                [updatedMatch.id]: updatedMatch
                            }
                        }));
                    }}
                />
            );
        case 'round-robin':
            console.log('BracketView: Rendering RoundRobinBracket');
            return (
                <RoundRobinBracket
                    data={localBracketData}
                    tournamentId={tournamentId}
                    currentUserAddress={currentUserAddress}
                    isAdmin={isAdmin}
                    onMatchUpdate={(updatedMatch) => {
                        setLocalBracketData(prevData => ({
                            ...prevData,
                            matchesById: {
                                ...prevData.matchesById,
                                [updatedMatch.id]: updatedMatch
                            }
                        }));
                    }}
                />
            );
        default:
            console.error('BracketView: Unsupported bracket format:', localBracketData.format);
            console.error('BracketView: Available formats: single-elimination, double-elimination, round-robin');
            console.error('BracketView: Full bracket data:', localBracketData);
            return <div className="bracket-error">Unsupported bracket format: {localBracketData.format}</div>;
    }
};

export default BracketView; 