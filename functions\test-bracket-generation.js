// Test script for bracket generation
const { generateBracket } = require('./bracketGenerator');

// Test function to verify bracket generation
function testBracketGeneration() {
    console.log('Testing bracket generation...\n');

    // Test data with odd number of participants
    const testParticipants = [
        { id: 'player1', name: 'Player 1', seed: 1 },
        { id: 'player2', name: 'Player 2', seed: 2 },
        { id: 'player3', name: 'Player 3', seed: 3 },
        { id: 'player4', name: 'Player 4', seed: 4 },
        { id: 'player5', name: 'Player 5', seed: 5 }
    ];

    console.log(`Testing with ${testParticipants.length} participants (odd number):`);
    testParticipants.forEach(p => console.log(`- ${p.name} (${p.id})`));
    console.log('');

    // Test Single Elimination
    console.log('=== SINGLE ELIMINATION ===');
    try {
        const singleElim = generateBracket(testParticipants, 'single-elimination');
        console.log(`Format: ${singleElim.format}`);
        console.log(`Participants in bracket: ${singleElim.participants.length}`);
        console.log(`Rounds: ${singleElim.rounds.length}`);
        
        singleElim.rounds.forEach((round, index) => {
            console.log(`\nRound ${index + 1}: ${round.name}`);
            round.matches.forEach(match => {
                const p1 = singleElim.participants.find(p => p.id === match.participant1Id);
                const p2 = singleElim.participants.find(p => p.id === match.participant2Id);
                console.log(`  ${match.id}: ${p1?.name || 'TBD'} vs ${p2?.name || 'TBD'} (${match.status})`);
            });
        });
        
        // Check if all participants are included
        const participantIds = new Set(testParticipants.map(p => p.id));
        const bracketParticipantIds = new Set();
        
        singleElim.rounds.forEach(round => {
            round.matches.forEach(match => {
                if (match.participant1Id) bracketParticipantIds.add(match.participant1Id);
                if (match.participant2Id) bracketParticipantIds.add(match.participant2Id);
            });
        });
        
        console.log(`\nParticipant check:`);
        console.log(`Original participants: ${participantIds.size}`);
        console.log(`Participants in bracket: ${bracketParticipantIds.size}`);
        
        const missingParticipants = [...participantIds].filter(id => !bracketParticipantIds.has(id));
        if (missingParticipants.length > 0) {
            console.log(`❌ Missing participants: ${missingParticipants.join(', ')}`);
        } else {
            console.log(`✅ All participants included in bracket`);
        }
        
    } catch (error) {
        console.error('❌ Single elimination failed:', error.message);
    }

    // Test Double Elimination
    console.log('\n=== DOUBLE ELIMINATION ===');
    try {
        const doubleElim = generateBracket(testParticipants, 'double-elimination');
        console.log(`Format: ${doubleElim.format}`);
        console.log(`Participants in bracket: ${doubleElim.participants.length}`);
        console.log(`Rounds: ${doubleElim.rounds.length}`);
        
        // Check if all participants are included
        const participantIds = new Set(testParticipants.map(p => p.id));
        const bracketParticipantIds = new Set();
        
        doubleElim.rounds.forEach(round => {
            round.matches.forEach(match => {
                if (match.participant1Id) bracketParticipantIds.add(match.participant1Id);
                if (match.participant2Id) bracketParticipantIds.add(match.participant2Id);
            });
        });
        
        console.log(`\nParticipant check:`);
        console.log(`Original participants: ${participantIds.size}`);
        console.log(`Participants in bracket: ${bracketParticipantIds.size}`);
        
        const missingParticipants = [...participantIds].filter(id => !bracketParticipantIds.has(id));
        if (missingParticipants.length > 0) {
            console.log(`❌ Missing participants: ${missingParticipants.join(', ')}`);
        } else {
            console.log(`✅ All participants included in bracket`);
        }
        
    } catch (error) {
        console.error('❌ Double elimination failed:', error.message);
    }

    // Test Round Robin
    console.log('\n=== ROUND ROBIN ===');
    try {
        const roundRobin = generateBracket(testParticipants, 'round-robin');
        console.log(`Format: ${roundRobin.format}`);
        console.log(`Participants in bracket: ${roundRobin.participants.length}`);
        console.log(`Total matches: ${roundRobin.metadata.totalMatches}`);
        console.log(`Expected matches: ${testParticipants.length * (testParticipants.length - 1) / 2}`);
        
        // Check if all participants are included
        const participantIds = new Set(testParticipants.map(p => p.id));
        const bracketParticipantIds = new Set();
        
        roundRobin.rounds.forEach(round => {
            round.matches.forEach(match => {
                if (match.participant1Id) bracketParticipantIds.add(match.participant1Id);
                if (match.participant2Id) bracketParticipantIds.add(match.participant2Id);
            });
        });
        
        console.log(`\nParticipant check:`);
        console.log(`Original participants: ${participantIds.size}`);
        console.log(`Participants in bracket: ${bracketParticipantIds.size}`);
        
        const missingParticipants = [...participantIds].filter(id => !bracketParticipantIds.has(id));
        if (missingParticipants.length > 0) {
            console.log(`❌ Missing participants: ${missingParticipants.join(', ')}`);
        } else {
            console.log(`✅ All participants included in bracket`);
        }
        
    } catch (error) {
        console.error('❌ Round robin failed:', error.message);
    }
}

// Run the test
if (require.main === module) {
    testBracketGeneration();
}

module.exports = { testBracketGeneration };
