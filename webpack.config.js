const path = require('path');

module.exports = [
  // Bracket bundle
  {
    entry: './src/bracket-entry.js',
    output: {
      path: path.resolve(__dirname, 'src'),
      filename: 'bracket-bundle.js',
      library: {
        type: 'umd',
        name: 'BracketComponents'
      },
      globalObject: 'this'
    },
    externals: {
      'react': 'React',
      'react-dom': 'ReactDOM'
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react']
            }
          }
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    resolve: {
      extensions: ['.js', '.jsx']
    }
  },
  // Admin bundle
  {
    entry: './src/admin-entry.js',
    output: {
      path: path.resolve(__dirname, 'src'),
      filename: 'admin-bundle.js',
      library: {
        type: 'umd',
        name: 'AdminComponents'
      },
      globalObject: 'this'
    },
    externals: {
      'react': 'React',
      'react-dom': 'ReactDOM'
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react']
            }
          }
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    resolve: {
      extensions: ['.js', '.jsx']
    }
  },
  // Notification bundle
  {
    entry: './src/notification-entry.js',
    output: {
      path: path.resolve(__dirname, 'src'),
      filename: 'notification-bundle.js',
      library: {
        type: 'umd',
        name: 'NotificationComponents'
      },
      globalObject: 'this'
    },
    externals: {
      'react': 'React',
      'react-dom': 'ReactDOM'
    },
    module: {
      rules: [
        {
          test: /\.(js|jsx)$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: ['@babel/preset-env', '@babel/preset-react']
            }
          }
        },
        {
          test: /\.css$/,
          use: ['style-loader', 'css-loader']
        }
      ]
    },
    resolve: {
      extensions: ['.js', '.jsx']
    }
  }
];