import React, { useState } from 'react';
import './BracketStyles.css';
import MatchResultSubmission from './MatchResultSubmission';
import TournamentWinner from './TournamentWinner';

const DoubleEliminationBracket = ({ data, tournamentId, currentUserAddress, isAdmin }) => {
    const { rounds, matchesById, participants } = data;
    const [selectedMatch, setSelectedMatch] = useState(null);

    // Find the tournament winner
    const getTournamentWinner = () => {
        if (!rounds || rounds.length === 0) return null;

        // In double elimination, the winner is determined by the Grand Final match
        const grandFinalMatch = Object.values(matchesById).find(match =>
            match.identifier === "Grand Final" || match.identifier === "Grand Final Reset"
        );

        if (!grandFinalMatch || !grandFinalMatch.winnerId) return null;

        return participants.find(p => p.id === grandFinalMatch.winnerId);
    };

    const renderMatch = (match) => {
        const participant1 = participants.find(p => p.id === match.participant1Id);
        const participant2 = participants.find(p => p.id === match.participant2Id);
        const winner = participants.find(p => p.id === match.winnerId);
        const isParticipant = currentUserAddress &&
            (match.participant1Id === currentUserAddress || match.participant2Id === currentUserAddress);

        const canSubmitResult = isParticipant &&
            (match.status.toUpperCase() === 'PENDING' || match.status.toLowerCase() === 'ready') &&
            !match.resultSubmissions?.some(sub => sub.submittedBy === currentUserAddress);

        return (
            <div
                className={`match-container ${match.status.toLowerCase()} ${match.isUpperBracket ? 'upper-bracket' : 'lower-bracket'} ${selectedMatch?.id === match.id ? 'selected' : ''}`}
                key={match.id}
                onClick={() => setSelectedMatch(match)}
            >
                <div className="match-header">
                    <span className="match-id">{match.identifier || match.id}</span>
                    {match.matchFormat && (
                        <span className="match-format">{match.matchFormat.toUpperCase()}</span>
                    )}
                    {match.status.toUpperCase() === 'DISPUTED' && (
                        <span className="dispute-badge">Disputed</span>
                    )}
                </div>
                <div className="match-content">
                    <div className={`participant ${match.winnerId === match.participant1Id ? 'winner' : ''}`}>
                        <span className="participant-name">
                            {participant1 ? participant1.name : 'TBD'}
                        </span>
                        {match.score && <span className="score">{match.score.participant1}</span>}
                    </div>
                    <div className={`participant ${match.winnerId === match.participant2Id ? 'winner' : ''}`}>
                        <span className="participant-name">
                            {participant2 ? participant2.name : 'TBD'}
                        </span>
                        {match.score && <span className="score">{match.score.participant2}</span>}
                    </div>
                </div>
                {canSubmitResult && (
                    <div className="match-actions">
                        <button
                            className="btn btn-cyber-secondary text-sm"
                            onClick={(e) => {
                                e.stopPropagation();
                                setSelectedMatch(match);
                            }}
                        >
                            Submit Result
                        </button>
                    </div>
                )}
            </div>
        );
    };

    // Separate rounds into upper bracket, lower bracket, and grand finals
    const upperBracketRounds = rounds.filter(round =>
        round.matches.some(match => match.isUpperBracket)
    );
    const lowerBracketRounds = rounds.filter(round =>
        round.matches.some(match => !match.isUpperBracket && !match.identifier?.includes('Grand Final'))
    );
    const grandFinalRounds = rounds.filter(round =>
        round.matches.some(match => match.identifier?.includes('Grand Final'))
    );

    return (
        <div className="bracket-container double-elimination">
            <TournamentWinner winner={getTournamentWinner()} />
            <div className="bracket-content">
                <div className="bracket-section upper-bracket">
                    <h2>Winners Bracket</h2>
                    {upperBracketRounds.map((round, roundIndex) => (
                        <div className="round" key={round.id}>
                            <div className="round-header">
                                <h3>{round.name}</h3>
                            </div>
                            <div className="round-matches">
                                {round.matches.map(match => renderMatch(match))}
                            </div>
                        </div>
                    ))}
                </div>

                <div className="bracket-section lower-bracket">
                    <h2>Losers Bracket</h2>
                    {lowerBracketRounds.map((round, roundIndex) => (
                        <div className="round" key={round.id}>
                            <div className="round-header">
                                <h3>{round.name}</h3>
                            </div>
                            <div className="round-matches">
                                {round.matches.map(match => renderMatch(match))}
                            </div>
                        </div>
                    ))}
                </div>

                <div className="bracket-section grand-finals">
                    <h2>Grand Final</h2>
                    {grandFinalRounds.map((round, roundIndex) => (
                        <div className="round" key={round.id}>
                            <div className="round-header">
                                <h3>{round.name}</h3>
                            </div>
                            <div className="round-matches">
                                {round.matches.map(match => renderMatch(match))}
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {selectedMatch && (
                <div className="match-details-panel">
                    <button
                        className="close-panel-btn"
                        onClick={() => setSelectedMatch(null)}
                    >
                        ×
                    </button>
                    <MatchResultSubmission
                        match={selectedMatch}
                        tournamentId={tournamentId}
                        currentUserAddress={currentUserAddress}
                        isAdmin={isAdmin}
                        bracketData={data}
                    />
                </div>
            )}
        </div>
    );
};

export default DoubleEliminationBracket;