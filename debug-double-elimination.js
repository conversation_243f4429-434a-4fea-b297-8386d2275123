// Debug Double Elimination Bracket Generation
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== DEBUGGING DOUBLE ELIMINATION BRACKET ===\n');

// Test with 8 players to verify more complex bracket
const testParticipants = [
    { id: 'player1', name: 'Player 1', seed: 1 },
    { id: 'player2', name: 'Player 2', seed: 2 },
    { id: 'player3', name: 'Player 3', seed: 3 },
    { id: 'player4', name: 'Player 4', seed: 4 },
    { id: 'player5', name: 'Player 5', seed: 5 },
    { id: 'player6', name: 'Player 6', seed: 6 },
    { id: 'player7', name: 'Player 7', seed: 7 },
    { id: 'player8', name: 'Player 8', seed: 8 }
];

try {
    console.log('Generating double elimination bracket for 8 players...');
    const bracket = generateBracket(testParticipants, 'double-elimination');
    
    console.log('\n=== BRACKET STRUCTURE ===');
    console.log('Format:', bracket.format);
    console.log('Participants:', bracket.participants.length);
    console.log('Rounds:', bracket.rounds.length);
    console.log('Total Matches:', Object.keys(bracket.matchesById).length);
    console.log('Metadata:', bracket.metadata);
    
    console.log('\n=== ROUNDS BREAKDOWN ===');
    bracket.rounds.forEach((round, index) => {
        console.log(`Round ${index + 1}: ${round.name} (${round.id})`);
        console.log(`  Matches: ${round.matches.length}`);
        round.matches.forEach(match => {
            console.log(`    ${match.id}: ${match.participant1Id || 'TBD'} vs ${match.participant2Id || 'TBD'}`);
            if (match.nextMatchId) {
                console.log(`      -> Next: ${match.nextMatchId}`);
            }
            if (match.nextLoserMatchId) {
                console.log(`      -> Loser: ${match.nextLoserMatchId}`);
            }
        });
    });
    
    console.log('\n=== MATCH VALIDATION ===');
    let validationErrors = [];
    
    Object.values(bracket.matchesById).forEach(match => {
        // Check if nextMatchId exists
        if (match.nextMatchId && !bracket.matchesById[match.nextMatchId]) {
            validationErrors.push(`${match.id}: Invalid nextMatchId ${match.nextMatchId}`);
        }
        
        // Check if nextLoserMatchId exists
        if (match.nextLoserMatchId && !bracket.matchesById[match.nextLoserMatchId]) {
            validationErrors.push(`${match.id}: Invalid nextLoserMatchId ${match.nextLoserMatchId}`);
        }
    });
    
    if (validationErrors.length === 0) {
        console.log('✅ All match links are valid!');
    } else {
        console.log('❌ Validation errors found:');
        validationErrors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log('\n=== ALL MATCHES BY ID ===');
    Object.keys(bracket.matchesById).sort().forEach(matchId => {
        const match = bracket.matchesById[matchId];
        console.log(`${matchId}: ${match.participant1Id || 'TBD'} vs ${match.participant2Id || 'TBD'} (${match.isUpperBracket ? 'UB' : 'LB'})`);
    });
    
} catch (error) {
    console.error('❌ Error generating bracket:', error.message);
    console.error(error.stack);
}
