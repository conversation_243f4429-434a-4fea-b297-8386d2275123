const functions = require('firebase-functions/v1');
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}

const { generateBracket } = require('./bracketGenerator');
const { scheduledTournamentStatusUpdate } = require('./scheduledStatusUpdate');
const { verifyPayment } = require('./verifyPayment');
const { generateBracketForTournament } = require('./generateBracketForTournament');

// Helper function to create notifications
const createNotificationForUsers = async (userIds, type, title, message, data) => {
    const db = admin.firestore();
    const batch = db.batch();

    userIds.forEach(userId => {
        const notificationRef = db.collection('notifications').doc();
        batch.set(notificationRef, {
            userId: userId.toLowerCase(),
            type,
            title,
            message,
            data,
            isRead: false,
            createdAt: admin.firestore.Timestamp.now(),
            expiresAt: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
        });
    });

    await batch.commit();
    console.log(`Created ${userIds.length} notifications of type: ${type}`);
};

exports.generateBracketAfterRegistrationClosed = functions.firestore
    .document('tournaments/{tournamentId}')
    .onUpdate(async (change, context) => {
        const newData = change.after.data();
        const previousData = change.before.data();
        const tournamentId = context.params.tournamentId;

        // Check if the status changed to 'registrationClosed'
        if (newData.status === 'registrationClosed' && previousData.status !== 'registrationClosed') {
            console.log(`Registration closed for tournament: ${tournamentId}. Generating bracket...`);

            // Create notifications for participants
            const participants = newData.participants || [];
            if (participants.length > 0) {
                await createNotificationForUsers(
                    participants,
                    'tournament_registration_closed',
                    'Tournament Registration Closed',
                    `Registration has closed for "${newData.tournamentName}"`,
                    {
                        tournamentId: tournamentId,
                        tournamentName: newData.tournamentName,
                        tournamentFormat: newData.tournamentFormat || newData.bracketFormat,
                        gameName: newData.gameName
                    }
                );
            }

            // Generate bracket if there are participants (individual) or team participants (team tournaments)
            const hasIndividualParticipants = newData.participants && newData.participants.length > 0;
            const hasTeamParticipants = newData.teamParticipants && newData.teamParticipants.length > 0;
            const isTeamTournament = newData.tournamentFormat === '2v2' || newData.tournamentFormat === '3v3';

            if (hasIndividualParticipants || (isTeamTournament && hasTeamParticipants)) {
                try {
                    console.log(`Generating bracket for tournament ${tournamentId}:`, {
                        isTeamTournament,
                        hasIndividualParticipants,
                        hasTeamParticipants,
                        individualCount: newData.participants?.length || 0,
                        teamCount: newData.teamParticipants?.length || 0
                    });

                    // Use the dedicated bracket generation function that handles both individual and team tournaments
                    const bracketData = await generateBracketForTournament(tournamentId);

                    console.log(`✅ Bracket generated successfully for tournament ${tournamentId}:`, {
                        format: bracketData.format,
                        rounds: bracketData.rounds?.length,
                        matches: Object.keys(bracketData.matchesById || {}).length,
                        participants: bracketData.participants?.length
                    });

                } catch (error) {
                    console.error(`❌ Error generating bracket for tournament ${tournamentId}:`, error);
                }
            } else {
                console.log(`No participants for tournament: ${tournamentId}`);
            }
        }

        // Check if the status changed to 'live'
        if (newData.status === 'live' && previousData.status !== 'live') {
            console.log(`Tournament went live: ${tournamentId}. Creating notifications...`);

            // Create notifications for participants
            const participants = newData.participants || [];
            if (participants.length > 0) {
                await createNotificationForUsers(
                    participants,
                    'tournament_live',
                    'Tournament Started',
                    `"${newData.tournamentName}" is now live!`,
                    {
                        tournamentId: tournamentId,
                        tournamentName: newData.tournamentName,
                        tournamentFormat: newData.tournamentFormat || newData.bracketFormat,
                        gameName: newData.gameName
                    }
                );
            }
        }

        return null;
    });

// Function to manually update tournament status
exports.regenerateBracket = functions.https.onRequest(async (req, res) => {
    const db = admin.firestore();
    const tournamentsRef = db.collection('tournaments');

    try {
        // Get tournaments that are in 'live' status
        const snapshot = await tournamentsRef
            .where('status', '==', 'live')
            .get();

        const batch = db.batch();
        const now = admin.firestore.Timestamp.now();

        snapshot.forEach(doc => {
            // First update to registrationClosed
            batch.update(doc.ref, {
                status: 'registrationClosed',
                registrationClosedAt: now
            });
        });

        // Commit the batch to update all tournaments to registrationClosed
        await batch.commit();

        // Wait a moment for the bracket generation to complete
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Now update back to live
        const batch2 = db.batch();
        snapshot.forEach(doc => {
            batch2.update(doc.ref, {
                status: 'live',
                startedAt: now
            });
        });

        // Commit the second batch to update all tournaments back to live
        await batch2.commit();

        res.json({
            message: 'Successfully triggered bracket regeneration for live tournaments',
            count: snapshot.size
        });
    } catch (error) {
        console.error('Error updating tournaments:', error);
        res.status(500).json({ error: error.message });
    }
});

// Function to check tournament data and debug status updates
exports.checkTournamentData = functions.https.onRequest(async (req, res) => {
    const db = admin.firestore();
    const tournamentsRef = db.collection('tournaments');

    try {
        // Get all tournaments to debug status issues
        const allSnapshot = await tournamentsRef.get();
        const now = admin.firestore.Timestamp.now();

        const tournaments = [];
        allSnapshot.forEach(doc => {
            const data = doc.data();
            const startDate = data.startDate;
            const registrationClosedAt = data.registrationClosedAt;

            tournaments.push({
                id: doc.id,
                status: data.status,
                startDate: startDate?.toDate(),
                registrationClosedAt: registrationClosedAt?.toDate(),
                shouldCloseRegistration: startDate && startDate.seconds <= now.seconds,
                shouldStartTournament: registrationClosedAt && (now.seconds - registrationClosedAt.seconds) >= (30 * 60),
                hasBracketData: !!data.bracketData,
                bracketFormat: data.bracketData?.format,
                numRounds: data.bracketData?.rounds?.length,
                numParticipants: data.participants?.length || 0,
                startedAt: data.startedAt?.toDate()
            });
        });

        res.json({
            currentTime: now.toDate(),
            count: tournaments.length,
            tournaments: tournaments
        });
    } catch (error) {
        console.error('Error fetching tournaments:', error);
        res.status(500).json({ error: error.message });
    }
});

// Manual function to force tournament status updates for debugging
exports.manualTournamentStatusUpdate = functions.https.onRequest(async (req, res) => {
    const db = admin.firestore();
    const now = admin.firestore.Timestamp.now();
    const tournamentsRef = db.collection('tournaments');

    try {
        let updatedCount = 0;
        const results = [];

        // First, handle tournaments that need to close registration
        const registrationSnapshot = await tournamentsRef
            .where('status', '==', 'upcoming')
            .where('startDate', '<=', now)
            .get();

        if (!registrationSnapshot.empty) {
            const registrationBatch = db.batch();

            for (const doc of registrationSnapshot.docs) {
                const tournament = doc.data();

                // Generate bracket if there are participants (individual) or team participants (team tournaments)
                const hasIndividualParticipants = tournament.participants && tournament.participants.length > 0;
                const hasTeamParticipants = tournament.teamParticipants && tournament.teamParticipants.length > 0;
                const isTeamTournament = tournament.tournamentFormat === '2v2' || tournament.tournamentFormat === '3v3';

                if (hasIndividualParticipants || (isTeamTournament && hasTeamParticipants)) {
                    try {
                        console.log(`Generating bracket for tournament ${doc.id}:`, {
                            isTeamTournament,
                            hasIndividualParticipants,
                            hasTeamParticipants,
                            individualCount: tournament.participants?.length || 0,
                            teamCount: tournament.teamParticipants?.length || 0
                        });

                        // Use the dedicated bracket generation function that handles both individual and team tournaments
                        const bracketData = await generateBracketForTournament(doc.id);

                        registrationBatch.update(doc.ref, {
                            status: 'registrationClosed',
                            registrationClosedAt: now,
                            bracketData: bracketData
                        });

                        const participantCount = isTeamTournament ?
                            (tournament.teamParticipants?.length || 0) :
                            (tournament.participants?.length || 0);

                        results.push({
                            id: doc.id,
                            action: 'closed_registration_with_bracket',
                            participants: participantCount
                        });
                    } catch (error) {
                        console.error(`Error generating bracket for tournament ${doc.id}:`, error);
                        // Still update status even if bracket generation fails
                        registrationBatch.update(doc.ref, {
                            status: 'registrationClosed',
                            registrationClosedAt: now
                        });

                        results.push({
                            id: doc.id,
                            action: 'closed_registration_no_bracket',
                            error: error.message
                        });
                    }
                } else {
                    // No participants, just update status
                    registrationBatch.update(doc.ref, {
                        status: 'registrationClosed',
                        registrationClosedAt: now
                    });

                    results.push({
                        id: doc.id,
                        action: 'closed_registration_no_participants'
                    });
                }

                updatedCount++;
            }

            await registrationBatch.commit();
        }

        // Then, handle tournaments that should start (30 minutes after registration closed)
        const thirtyMinutesAgo = new admin.firestore.Timestamp(
            now.seconds - (30 * 60), // 30 minutes in seconds
            now.nanoseconds
        );

        const startSnapshot = await tournamentsRef
            .where('status', '==', 'registrationClosed')
            .where('registrationClosedAt', '<=', thirtyMinutesAgo)
            .get();

        if (!startSnapshot.empty) {
            const startBatch = db.batch();
            startSnapshot.forEach(doc => {
                startBatch.update(doc.ref, {
                    status: 'live',
                    startedAt: now
                });

                results.push({
                    id: doc.id,
                    action: 'started_tournament'
                });

                updatedCount++;
            });
            await startBatch.commit();
        }

        res.json({
            message: 'Manual tournament status update completed',
            currentTime: now.toDate(),
            updatedCount,
            results
        });
    } catch (error) {
        console.error('Error in manual tournament status update:', error);
        res.status(500).json({ error: error.message });
    }
});

exports.scheduledTournamentStatusUpdate = scheduledTournamentStatusUpdate;

// Export the verifyPayment function
exports.verifyPayment = verifyPayment;

// Function to create match dispute notification
exports.createMatchDisputeNotification = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { tournamentId, matchId } = data;
    if (!tournamentId || !matchId) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    try {
        const db = admin.firestore();
        const tournamentDoc = await db.collection('tournaments').doc(tournamentId).get();

        if (!tournamentDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Tournament not found');
        }

        const tournament = tournamentDoc.data();
        const match = tournament.bracketData?.matchesById?.[matchId];

        if (!match) {
            throw new functions.https.HttpsError('not-found', 'Match not found');
        }

        // Create notification for tournament creator
        await createNotificationForUsers(
            [tournament.creatorAddress],
            'match_dispute',
            'Match Dispute Requires Attention',
            `A match in "${tournament.tournamentName}" has been disputed and needs admin review`,
            {
                tournamentId: tournamentId,
                tournamentName: tournament.tournamentName,
                matchId: matchId,
                matchIdentifier: match.identifier || match.id,
                participant1: match.participant1Name || match.participant1Id,
                participant2: match.participant2Name || match.participant2Id
            }
        );

        return { success: true };
    } catch (error) {
        console.error('Error creating match dispute notification:', error);
        throw new functions.https.HttpsError('internal', error.message);
    }
});

// Function to check for upcoming matches and create notifications
exports.checkUpcomingMatches = functions.pubsub.schedule('every 30 minutes').onRun(async (context) => {
    const db = admin.firestore();

    try {
        // Get all live tournaments
        const tournamentsSnapshot = await db.collection('tournaments')
            .where('status', '==', 'live')
            .get();

        for (const tournamentDoc of tournamentsSnapshot.docs) {
            const tournament = tournamentDoc.data();
            const tournamentId = tournamentDoc.id;

            if (!tournament.bracketData || !tournament.bracketData.matchesById) {
                continue;
            }

            // Find matches that are ready to be played
            const readyMatches = Object.values(tournament.bracketData.matchesById)
                .filter(match => match.status === 'ready');

            // Create notifications for participants in ready matches
            for (const match of readyMatches) {
                const participants = [match.participant1Id, match.participant2Id]
                    .filter(p => p && !p.startsWith('TBD_'));

                if (participants.length === 2) {
                    // Create individual notifications for each participant
                    for (const participantId of participants) {
                        const opponent = participantId === match.participant1Id ?
                            (match.participant2Name || match.participant2Id) :
                            (match.participant1Name || match.participant1Id);

                        await createNotificationForUsers(
                            [participantId],
                            'upcoming_match',
                            'Upcoming Match',
                            `You have an upcoming match in "${tournament.tournamentName}" against ${opponent}`,
                            {
                                tournamentId: tournamentId,
                                tournamentName: tournament.tournamentName,
                                matchId: match.id,
                                matchIdentifier: match.identifier || match.id,
                                opponent: opponent,
                                matchFormat: match.matchFormat
                            }
                        );
                    }
                }
            }
        }

        console.log('Upcoming match notifications check completed');
        return null;
    } catch (error) {
        console.error('Error checking upcoming matches:', error);
        return null;
    }
});

// Export the generateBracketForTournament function
exports.generateBracketForTournament = functions.https.onCall(async (data, context) => {
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { tournamentId } = data;
    if (!tournamentId) {
        throw new functions.https.HttpsError('invalid-argument', 'Tournament ID is required');
    }

    try {
        const bracketData = await generateBracketForTournament(tournamentId);
        return { success: true, bracketData };
    } catch (error) {
        console.error('Error generating bracket:', error);
        throw new functions.https.HttpsError('internal', error.message);
    }
});

// Export the regenerateBracketForLiveTournament function
exports.regenerateBracketForLiveTournament = functions.https.onCall(async (data, context) => {
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { tournamentId } = data;
    if (!tournamentId) {
        throw new functions.https.HttpsError('invalid-argument', 'Tournament ID is required');
    }

    try {
        const db = admin.firestore();
        const tournamentRef = db.collection('tournaments').doc(tournamentId);
        const tournamentDoc = await tournamentRef.get();

        if (!tournamentDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Tournament not found');
        }

        const tournament = tournamentDoc.data();

        // Verify tournament is live
        if (tournament.status !== 'live') {
            throw new functions.https.HttpsError('failed-precondition', 'Tournament must be live to regenerate bracket');
        }

        // Generate new bracket
        const bracketData = await generateBracketForTournament(tournamentId);

        return { success: true, message: 'Bracket regenerated successfully', bracketData };
    } catch (error) {
        console.error('Error regenerating bracket:', error);
        throw new functions.https.HttpsError('internal', error.message);
    }
});

// Cloud Function to update match results
exports.updateMatchResult = functions.https.onCall(async (data, context) => {
    // Check if user is authenticated
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated to update match results');
    }

    const { tournamentId, matchId, result, userWalletAddress } = data;
    if (!tournamentId || !matchId || !result || !userWalletAddress) {
        throw new functions.https.HttpsError('invalid-argument', 'Missing required fields including userWalletAddress');
    }

    const db = admin.firestore();
    const tournamentRef = db.collection('tournaments').doc(tournamentId);
    const walletAddress = userWalletAddress.toLowerCase();

    try {
        // Use a transaction to ensure atomicity
        await db.runTransaction(async (transaction) => {
            const tournamentDoc = await transaction.get(tournamentRef);
            if (!tournamentDoc.exists) {
                throw new functions.https.HttpsError('not-found', 'Tournament not found');
            }

            const tournament = tournamentDoc.data();

            // Verify user is a participant using wallet address
            let isParticipant = false;

            // Check individual tournament participants
            if (tournament.participants && tournament.participants.length > 0) {
                isParticipant = tournament.participants.some(p =>
                    (typeof p === 'string' && p.toLowerCase() === walletAddress) ||
                    (typeof p === 'object' && p.id && p.id.toLowerCase() === walletAddress)
                );
            }

            // Check team tournament participants (if not found in individual participants)
            if (!isParticipant && tournament.teamParticipants && tournament.teamParticipants.length > 0) {
                isParticipant = tournament.teamParticipants.some(team =>
                    team.members && Array.isArray(team.members) &&
                    team.members.some(member => member.toLowerCase() === walletAddress)
                );
            }

            if (!isParticipant) {
                throw new functions.https.HttpsError('permission-denied', 'User wallet address is not a participant in this tournament');
            }

            // Verify tournament is in progress
            if (tournament.status !== 'live') {
                throw new functions.https.HttpsError('failed-precondition', 'Tournament is not in progress');
            }

            // Find and update the match
            const match = tournament.bracketData.matchesById[matchId];
            if (!match) {
                throw new functions.https.HttpsError('not-found', 'Match not found');
            }

            // Validate that both participants are properly set before allowing result submission
            const participantValidation = validateMatchParticipants(match, tournament.bracketData.participants || []);
            if (!participantValidation.isValid) {
                throw new functions.https.HttpsError('failed-precondition', participantValidation.error);
            }

            // Verify the user is a participant in this match
            let isMatchParticipant = false;

            // Check direct wallet address match (individual tournaments)
            if (match.participant1Id?.toLowerCase() === walletAddress ||
                match.participant2Id?.toLowerCase() === walletAddress) {
                isMatchParticipant = true;
            }

            // If not a direct match, check team membership (team tournaments)
            if (!isMatchParticipant && tournament.bracketData && tournament.bracketData.participants) {
                const participant1 = tournament.bracketData.participants.find(p => p.id === match.participant1Id);
                const participant2 = tournament.bracketData.participants.find(p => p.id === match.participant2Id);

                // Check if user is a member of participant1's team
                if (participant1 && participant1.members && Array.isArray(participant1.members)) {
                    isMatchParticipant = participant1.members.some(member =>
                        member.toLowerCase() === walletAddress
                    );
                }

                // Check if user is a member of participant2's team
                if (!isMatchParticipant && participant2 && participant2.members && Array.isArray(participant2.members)) {
                    isMatchParticipant = participant2.members.some(member =>
                        member.toLowerCase() === walletAddress
                    );
                }
            }

            if (!isMatchParticipant) {
                throw new functions.https.HttpsError('permission-denied', 'User is not a participant in this match');
            }

            // Validate scores against match format requirements
            const matchFormat = match.matchFormat || tournament.bracketData?.metadata?.matchFormat || tournament.matchFormat || 'bo1';
            const formatValidation = validateMatchFormatScore(
                result.score,
                result.winnerId,
                matchFormat,
                match.participant1Id,
                match.participant2Id
            );

            if (!formatValidation.isValid) {
                throw new functions.https.HttpsError('invalid-argument', formatValidation.error);
            }

            // Update match result
            match.status = 'completed';
            match.winnerId = result.winnerId;
            match.loserId = result.winnerId === match.participant1Id ? match.participant2Id : match.participant1Id;
            match.score = result.score;
            match.completedAt = admin.firestore.Timestamp.now();
            match.submittedBy = walletAddress; // Use wallet address instead of Firebase UID
            match.submittedAt = admin.firestore.Timestamp.now();

            // Sync the match data in the rounds array
            tournament.bracketData.rounds.forEach(round => {
                round.matches.forEach(roundMatch => {
                    if (roundMatch.id === match.id) {
                        roundMatch.status = match.status;
                        roundMatch.winnerId = match.winnerId;
                        roundMatch.loserId = match.loserId;
                        roundMatch.score = match.score;
                        roundMatch.completedAt = match.completedAt;
                        roundMatch.submittedBy = match.submittedBy;
                        roundMatch.submittedAt = match.submittedAt;
                    }
                });
            });

            // Handle bracket progression based on tournament format
            switch (tournament.bracketData.format) {
                case 'single-elimination':
                    await handleSingleEliminationProgression(transaction, tournament, match, tournamentRef);
                    break;
                case 'double-elimination':
                    await handleDoubleEliminationProgression(transaction, tournament, match, tournamentRef);
                    break;
                case 'round-robin':
                    await handleRoundRobinProgression(transaction, tournament, match, tournamentRef);
                    break;
            }

            // Update the tournament document
            transaction.update(tournamentRef, {
                bracketData: tournament.bracketData
            });
        });

        return { success: true };
    } catch (error) {
        console.error('Error updating match result:', error);
        throw new functions.https.HttpsError('internal', error.message);
    }
});

/**
 * Validates that both participants in a match are properly set and ready for result submission
 * @param {Object} match - The match object to validate
 * @param {Array} participants - Array of tournament participants
 * @returns {Object} Validation result with isValid boolean and error message if invalid
 */
function validateMatchParticipants(match, participants = []) {
    // Check if match exists
    if (!match) {
        return { isValid: false, error: 'Match not found' };
    }

    // Check if both participant IDs are set
    if (!match.participant1Id || !match.participant2Id) {
        return { isValid: false, error: 'Both participants must be assigned before submitting results' };
    }

    // Check if match already has a result (check this before status to give more specific error)
    if (match.winnerId || match.status === 'completed') {
        return { isValid: false, error: 'Match result has already been submitted' };
    }

    // Check if either participant is a TBD placeholder
    if (match.participant1Id.startsWith('TBD_') || match.participant2Id.startsWith('TBD_')) {
        return { isValid: false, error: 'Cannot submit results while participants are still being determined (TBD)' };
    }

    // Check if match status allows result submission
    const validStatuses = ['PENDING', 'ready', 'pending'];
    if (!validStatuses.includes(match.status)) {
        return { isValid: false, error: `Cannot submit results for match with status: ${match.status}` };
    }

    // If participants array is provided, validate that both participants exist
    if (participants.length > 0) {
        const participant1Exists = participants.some(p => p.id === match.participant1Id);
        const participant2Exists = participants.some(p => p.id === match.participant2Id);

        if (!participant1Exists || !participant2Exists) {
            return { isValid: false, error: 'One or both participants are not valid tournament participants' };
        }
    }

    return { isValid: true };
}

/**
 * Validates that submitted match scores align with the tournament's "Best of X" format requirements
 * @param {Object} score - Score object with participant1 and participant2 scores
 * @param {string} winnerId - ID of the declared winner
 * @param {string} matchFormat - Match format (bo1, bo3, bo5, bo7)
 * @param {string} participant1Id - ID of participant 1
 * @param {string} participant2Id - ID of participant 2
 * @returns {Object} Validation result with isValid boolean and error message if invalid
 */
function validateMatchFormatScore(score, winnerId, matchFormat, participant1Id, participant2Id) {
    // Check if required parameters are provided
    if (!score || typeof score.participant1 !== 'number' || typeof score.participant2 !== 'number') {
        return { isValid: false, error: 'Valid scores for both participants are required' };
    }

    if (!winnerId || !matchFormat || !participant1Id || !participant2Id) {
        return { isValid: false, error: 'Missing required match information for validation' };
    }

    // Normalize match format
    const format = matchFormat.toLowerCase();
    const validFormats = ['bo1', 'bo3', 'bo5', 'bo7'];

    if (!validFormats.includes(format)) {
        return { isValid: false, error: `Invalid match format: ${matchFormat}. Must be one of: ${validFormats.join(', ')}` };
    }

    const participant1Score = score.participant1;
    const participant2Score = score.participant2;
    const totalGames = participant1Score + participant2Score;

    // Extract max games from format (e.g., 'bo3' -> 3)
    const maxGames = parseInt(format.substring(2));
    const firstToWin = Math.ceil(maxGames / 2); // First to win majority

    // Check for tied scores first (no clear winner possible)
    if (participant1Score === participant2Score) {
        return { isValid: false, error: `Tied scores are not allowed in ${format.toUpperCase()} matches. One participant must have a clear majority.` };
    }

    // Determine winner based on scores
    const scoreBasedWinner = participant1Score > participant2Score ? participant1Id : participant2Id;

    // Check if declared winner matches score-based winner
    if (winnerId !== scoreBasedWinner) {
        return { isValid: false, error: 'Declared winner does not match the scores provided' };
    }

    // Validate based on match format
    switch (format) {
        case 'bo1':
            // Best of 1: Exactly 1 game, winner has 1, loser has 0
            if (totalGames !== 1) {
                return { isValid: false, error: 'Best of 1 matches must have exactly 1 game played' };
            }
            if (Math.max(participant1Score, participant2Score) !== 1 || Math.min(participant1Score, participant2Score) !== 0) {
                return { isValid: false, error: 'Best of 1 matches must have a score of 1-0' };
            }
            break;

        case 'bo3':
            // Best of 3: At least 2 games, winner has at least 2, max 3 total games
            if (totalGames < 2 || totalGames > 3) {
                return { isValid: false, error: 'Best of 3 matches must have 2-3 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 2) {
                return { isValid: false, error: 'Best of 3 matches require the winner to win at least 2 games' };
            }
            break;

        case 'bo5':
            // Best of 5: At least 3 games, winner has at least 3, max 5 total games
            if (totalGames < 3 || totalGames > 5) {
                return { isValid: false, error: 'Best of 5 matches must have 3-5 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 3) {
                return { isValid: false, error: 'Best of 5 matches require the winner to win at least 3 games' };
            }
            break;

        case 'bo7':
            // Best of 7: At least 4 games, winner has at least 4, max 7 total games
            if (totalGames < 4 || totalGames > 7) {
                return { isValid: false, error: 'Best of 7 matches must have 4-7 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 4) {
                return { isValid: false, error: 'Best of 7 matches require the winner to win at least 4 games' };
            }
            break;
    }

    // Additional validation: ensure winner has won the majority
    const winnerScore = winnerId === participant1Id ? participant1Score : participant2Score;
    if (winnerScore < firstToWin) {
        return { isValid: false, error: `Winner must have won at least ${firstToWin} games in a ${format.toUpperCase()} match` };
    }

    return { isValid: true };
}

// Helper functions for bracket progression
async function handleSingleEliminationProgression(transaction, tournament, completedMatch, tournamentRef) {
    const { matchesById, rounds } = tournament.bracketData;

    // Find the next match using nextMatchId
    const nextMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;

    if (nextMatch) {
        // Update the next match with the winner
        // Check if participant1 is null or a TBD placeholder from this match
        if (nextMatch.participant1Id === null || nextMatch.participant1Id === `TBD_${completedMatch.id}`) {
            nextMatch.participant1Id = completedMatch.winnerId;
        }
        // Check if participant2 is null or a TBD placeholder from this match
        else if (nextMatch.participant2Id === null || nextMatch.participant2Id === `TBD_${completedMatch.id}`) {
            nextMatch.participant2Id = completedMatch.winnerId;
        }

        // Check if both participants are now set and neither is a TBD placeholder
        if (nextMatch.participant1Id && nextMatch.participant2Id &&
            !nextMatch.participant1Id.startsWith('TBD_') &&
            !nextMatch.participant2Id.startsWith('TBD_')) {
            nextMatch.status = 'ready';
        }
    } else {
        // If there's no next match, this was the final match
        // Update tournament status to completed
        transaction.update(tournamentRef, {
            status: 'completed',
            completedAt: admin.firestore.Timestamp.now(),
            winner: completedMatch.winnerId
        });
    }

    // Sync all changes back to rounds array structure
    rounds.forEach(round => {
        round.matches.forEach(roundMatch => {
            const updatedMatch = matchesById[roundMatch.id];
            if (updatedMatch) {
                roundMatch.participant1Id = updatedMatch.participant1Id;
                roundMatch.participant2Id = updatedMatch.participant2Id;
                roundMatch.status = updatedMatch.status;
                roundMatch.winnerId = updatedMatch.winnerId;
                roundMatch.loserId = updatedMatch.loserId;
            }
        });
    });
}

async function handleDoubleEliminationProgression(transaction, tournament, completedMatch, tournamentRef) {
    const { matchesById, rounds } = tournament.bracketData;

    // Handle winner's bracket progression
    if (completedMatch.isUpperBracket) {
        // Find next upper bracket match using nextMatchId
        const nextUpperMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;

        if (nextUpperMatch) {
            // Advance winner to next upper bracket match
            // Check if participant1 is null or a TBD placeholder from this match
            if (nextUpperMatch.participant1Id === null || nextUpperMatch.participant1Id === `TBD_${completedMatch.id}`) {
                nextUpperMatch.participant1Id = completedMatch.winnerId;
            }
            // Check if participant2 is null or a TBD placeholder from this match
            else if (nextUpperMatch.participant2Id === null || nextUpperMatch.participant2Id === `TBD_${completedMatch.id}`) {
                nextUpperMatch.participant2Id = completedMatch.winnerId;
            }

            // Update status if both participants are now set and neither is a TBD placeholder
            if (nextUpperMatch.participant1Id && nextUpperMatch.participant2Id &&
                !nextUpperMatch.participant1Id.startsWith('TBD_') &&
                !nextUpperMatch.participant2Id.startsWith('TBD_')) {
                nextUpperMatch.status = 'PENDING';
            }
        }

        // Handle loser's bracket progression using nextLoserMatchId
        const loserNextMatch = completedMatch.nextLoserMatchId ? matchesById[completedMatch.nextLoserMatchId] : null;

        if (loserNextMatch && completedMatch.loserId) {
            // Send loser to lower bracket
            if (loserNextMatch.participant1Id === null) {
                loserNextMatch.participant1Id = completedMatch.loserId;
            } else if (loserNextMatch.participant2Id === null) {
                loserNextMatch.participant2Id = completedMatch.loserId;
            }

            // Update status if both participants are now set and neither is a TBD placeholder
            if (loserNextMatch.participant1Id && loserNextMatch.participant2Id &&
                !loserNextMatch.participant1Id.startsWith('TBD_') &&
                !loserNextMatch.participant2Id.startsWith('TBD_')) {
                loserNextMatch.status = 'PENDING';
            }
        }
    } else {
        // Handle loser's bracket progression
        const nextLowerMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;

        if (nextLowerMatch) {
            // Advance winner to next lower bracket match
            // Check if participant1 is null or a TBD placeholder from this match
            if (nextLowerMatch.participant1Id === null || nextLowerMatch.participant1Id === `TBD_${completedMatch.id}`) {
                nextLowerMatch.participant1Id = completedMatch.winnerId;
            }
            // Check if participant2 is null or a TBD placeholder from this match
            else if (nextLowerMatch.participant2Id === null || nextLowerMatch.participant2Id === `TBD_${completedMatch.id}`) {
                nextLowerMatch.participant2Id = completedMatch.winnerId;
            }

            // Update status if both participants are now set and neither is a TBD placeholder
            if (nextLowerMatch.participant1Id && nextLowerMatch.participant2Id &&
                !nextLowerMatch.participant1Id.startsWith('TBD_') &&
                !nextLowerMatch.participant2Id.startsWith('TBD_')) {
                nextLowerMatch.status = 'PENDING';
            }
        } else {
            // Check if this is a grand final match
            if (completedMatch.identifier === "Grand Final") {
                // Handle grand final logic
                const resetMatch = Object.values(matchesById).find(match =>
                    match.identifier === "Grand Final Reset"
                );

                if (resetMatch) {
                    // In Grand Final: participant1 = upper bracket winner, participant2 = lower bracket winner
                    // If lower bracket winner (participant2) wins, trigger reset match
                    const lowerBracketWinnerWon = completedMatch.winnerId === completedMatch.participant2Id;

                    if (lowerBracketWinnerWon) {
                        // Set up reset match with same participants
                        resetMatch.participant1Id = completedMatch.participant1Id;
                        resetMatch.participant2Id = completedMatch.participant2Id;
                        resetMatch.status = 'PENDING';
                    } else {
                        // Upper bracket winner wins, tournament is complete
                        transaction.update(tournamentRef, {
                            status: 'completed',
                            completedAt: admin.firestore.Timestamp.now(),
                            winner: completedMatch.winnerId
                        });
                    }
                } else {
                    // No reset match, tournament is complete
                    transaction.update(tournamentRef, {
                        status: 'completed',
                        completedAt: admin.firestore.Timestamp.now(),
                        winner: completedMatch.winnerId
                    });
                }
            } else if (completedMatch.identifier === "Grand Final Reset") {
                // Reset match completed, tournament is over
                transaction.update(tournamentRef, {
                    status: 'completed',
                    completedAt: admin.firestore.Timestamp.now(),
                    winner: completedMatch.winnerId
                });
            } else {
                // Regular lower bracket match with no next match - shouldn't happen
                console.warn('Lower bracket match completed with no next match and no grand final identifier');
            }
        }
    }

    // Sync changes back to rounds array structure
    rounds.forEach(round => {
        round.matches.forEach(roundMatch => {
            const updatedMatch = matchesById[roundMatch.id];
            if (updatedMatch) {
                roundMatch.participant1Id = updatedMatch.participant1Id;
                roundMatch.participant2Id = updatedMatch.participant2Id;
                roundMatch.status = updatedMatch.status;
                roundMatch.winnerId = updatedMatch.winnerId;
                roundMatch.loserId = updatedMatch.loserId;
            }
        });
    });

    // Update match status to ready if both participants are set
    Object.values(matchesById).forEach(match => {
        if (match.status === 'PENDING' && match.participant1Id && match.participant2Id) {
            match.status = 'ready';
        }
    });
}

async function handleRoundRobinProgression(transaction, tournament, completedMatch, tournamentRef) {
    const { matchesById, rounds } = tournament.bracketData;

    // Sync changes back to rounds array structure
    rounds.forEach(round => {
        round.matches.forEach(roundMatch => {
            const updatedMatch = matchesById[roundMatch.id];
            if (updatedMatch) {
                roundMatch.participant1Id = updatedMatch.participant1Id;
                roundMatch.participant2Id = updatedMatch.participant2Id;
                roundMatch.status = updatedMatch.status;
                roundMatch.winnerId = updatedMatch.winnerId;
                roundMatch.loserId = updatedMatch.loserId;
                roundMatch.score = updatedMatch.score;
                roundMatch.completedAt = updatedMatch.completedAt;
                roundMatch.submittedBy = updatedMatch.submittedBy;
                roundMatch.submittedAt = updatedMatch.submittedAt;
            }
        });
    });

    // Check if all matches are completed
    const allMatchesCompleted = Object.values(matchesById).every(match => match.status === 'completed');

    if (allMatchesCompleted) {
        // Calculate final standings to determine winner
        const participants = tournament.bracketData.participants;
        const standings = participants.map(participant => ({
            id: participant.id,
            name: participant.name,
            wins: 0,
            losses: 0,
            points: 0
        }));

        // Calculate wins and losses
        Object.values(matchesById).forEach(match => {
            if (match.winnerId) {
                const winnerStanding = standings.find(s => s.id === match.winnerId);
                const loserStanding = standings.find(s => s.id === match.loserId);

                if (winnerStanding) {
                    winnerStanding.wins++;
                    winnerStanding.points += 3; // 3 points for a win
                }
                if (loserStanding) {
                    loserStanding.losses++;
                }
            }
        });

        // Sort by points, then wins to find winner
        standings.sort((a, b) => {
            if (b.points !== a.points) return b.points - a.points;
            return b.wins - a.wins;
        });

        const winner = standings.length > 0 ? standings[0].id : null;

        // Update tournament status to completed
        transaction.update(tournamentRef, {
            status: 'completed',
            completedAt: admin.firestore.Timestamp.now(),
            winner: winner
        });
    }
}