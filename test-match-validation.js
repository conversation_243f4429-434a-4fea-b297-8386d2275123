/**
 * Test script for match submission validation sanity checks
 * Tests the new validateMatchParticipants function with various scenarios
 */

// Import the validation function (simulated for testing)
function validateMatchParticipants(match, participants = []) {
    // Check if match exists
    if (!match) {
        return { isValid: false, error: 'Match not found' };
    }

    // Check if both participant IDs are set
    if (!match.participant1Id || !match.participant2Id) {
        return { isValid: false, error: 'Both participants must be assigned before submitting results' };
    }

    // Check if match already has a result (check this before status to give more specific error)
    if (match.winnerId || match.status === 'completed') {
        return { isValid: false, error: 'Match result has already been submitted' };
    }

    // Check if either participant is a TBD placeholder
    if (match.participant1Id.startsWith('TBD_') || match.participant2Id.startsWith('TBD_')) {
        return { isValid: false, error: 'Cannot submit results while participants are still being determined (TBD)' };
    }

    // Check if match status allows result submission
    const validStatuses = ['PENDING', 'ready', 'pending'];
    if (!validStatuses.includes(match.status)) {
        return { isValid: false, error: `Cannot submit results for match with status: ${match.status}` };
    }

    // If participants array is provided, validate that both participants exist
    if (participants.length > 0) {
        const participant1Exists = participants.some(p => p.id === match.participant1Id);
        const participant2Exists = participants.some(p => p.id === match.participant2Id);
        
        if (!participant1Exists || !participant2Exists) {
            return { isValid: false, error: 'One or both participants are not valid tournament participants' };
        }
    }

    return { isValid: true };
}

// Test data
const validParticipants = [
    { id: '0x1234567890abcdef1234567890abcdef12345678', name: 'Player 1' },
    { id: '0xabcdef1234567890abcdef1234567890abcdef12', name: 'Player 2' },
    { id: '0x9876543210fedcba9876543210fedcba98765432', name: 'Player 3' },
    { id: '0xfedcba9876543210fedcba9876543210fedcba98', name: 'Player 4' }
];

// Test scenarios
const testScenarios = [
    {
        name: "Valid match ready for submission",
        match: {
            id: 'R1M1',
            participant1Id: '0x1234567890abcdef1234567890abcdef12345678',
            participant2Id: '0xabcdef1234567890abcdef1234567890abcdef12',
            status: 'PENDING',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: true
    },
    {
        name: "Match with ready status",
        match: {
            id: 'R1M2',
            participant1Id: '0x1234567890abcdef1234567890abcdef12345678',
            participant2Id: '0xabcdef1234567890abcdef1234567890abcdef12',
            status: 'ready',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: true
    },
    {
        name: "Match with TBD participant1",
        match: {
            id: 'R2M1',
            participant1Id: 'TBD_R1M1',
            participant2Id: '0xabcdef1234567890abcdef1234567890abcdef12',
            status: 'PENDING',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'Cannot submit results while participants are still being determined (TBD)'
    },
    {
        name: "Match with TBD participant2",
        match: {
            id: 'R2M2',
            participant1Id: '0x1234567890abcdef1234567890abcdef12345678',
            participant2Id: 'TBD_R1M2',
            status: 'PENDING',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'Cannot submit results while participants are still being determined (TBD)'
    },
    {
        name: "Match with null participant1",
        match: {
            id: 'R1M3',
            participant1Id: null,
            participant2Id: '0xabcdef1234567890abcdef1234567890abcdef12',
            status: 'PENDING',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'Both participants must be assigned before submitting results'
    },
    {
        name: "Match with null participant2",
        match: {
            id: 'R1M4',
            participant1Id: '0x1234567890abcdef1234567890abcdef12345678',
            participant2Id: null,
            status: 'PENDING',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'Both participants must be assigned before submitting results'
    },
    {
        name: "Match already completed",
        match: {
            id: 'R1M5',
            participant1Id: '0x1234567890abcdef1234567890abcdef12345678',
            participant2Id: '0xabcdef1234567890abcdef1234567890abcdef12',
            status: 'completed',
            winnerId: '0x1234567890abcdef1234567890abcdef12345678'
        },
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'Match result has already been submitted'
    },
    {
        name: "Match with invalid status",
        match: {
            id: 'R1M6',
            participant1Id: '0x1234567890abcdef1234567890abcdef12345678',
            participant2Id: '0xabcdef1234567890abcdef1234567890abcdef12',
            status: 'waiting',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'Cannot submit results for match with status: waiting'
    },
    {
        name: "Match with non-existent participant",
        match: {
            id: 'R1M7',
            participant1Id: '0x1111111111111111111111111111111111111111',
            participant2Id: '0xabcdef1234567890abcdef1234567890abcdef12',
            status: 'PENDING',
            winnerId: null
        },
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'One or both participants are not valid tournament participants'
    },
    {
        name: "Null match",
        match: null,
        participants: validParticipants,
        expectedValid: false,
        expectedError: 'Match not found'
    }
];

// Run tests
console.log('🧪 Testing Match Validation Sanity Checks\n');

let passedTests = 0;
let totalTests = testScenarios.length;

testScenarios.forEach((scenario, index) => {
    console.log(`Test ${index + 1}: ${scenario.name}`);
    
    const result = validateMatchParticipants(scenario.match, scenario.participants);
    
    if (result.isValid === scenario.expectedValid) {
        if (!scenario.expectedValid && result.error === scenario.expectedError) {
            console.log('✅ PASS - Validation correctly rejected with expected error');
            passedTests++;
        } else if (scenario.expectedValid) {
            console.log('✅ PASS - Validation correctly accepted valid match');
            passedTests++;
        } else {
            console.log(`❌ FAIL - Expected error: "${scenario.expectedError}", got: "${result.error}"`);
        }
    } else {
        console.log(`❌ FAIL - Expected valid: ${scenario.expectedValid}, got: ${result.isValid}`);
        if (result.error) {
            console.log(`   Error: ${result.error}`);
        }
    }
    console.log('');
});

console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);

if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Match validation sanity checks are working correctly.');
} else {
    console.log('⚠️  Some tests failed. Please review the validation logic.');
}
