// Comprehensive Double Elimination Tournament Test
// Tests both individual and team tournaments with various scenarios
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== COMPREHENSIVE DOUBLE ELIMINATION TESTING ===\n');

// Test data for individual tournaments
const individualParticipants4 = [
    { id: '******************************************', name: 'Player Alpha', walletAddress: '******************************************', seed: 1 },
    { id: '******************************************', name: 'Player Beta', walletAddress: '******************************************', seed: 2 },
    { id: '******************************************', name: 'Player <PERSON>', walletAddress: '******************************************', seed: 3 },
    { id: '0x456789013def123456789013def12345678abcd', name: 'Player Delta', walletAddress: '0x456789013def123456789013def12345678abcd', seed: 4 }
];

const individualParticipants8 = [
    ...individualParticipants4,
    { id: '0x567890124ef123456789014ef12345678abcde', name: 'Player Echo', walletAddress: '0x567890124ef123456789014ef12345678abcde', seed: 5 },
    { id: '0x6789012345f123456789015f12345678abcdef', name: 'Player Foxtrot', walletAddress: '0x6789012345f123456789015f12345678abcdef', seed: 6 },
    { id: '0x789012346f123456789016f12345678abcdef0', name: 'Player Golf', walletAddress: '0x789012346f123456789016f12345678abcdef0', seed: 7 },
    { id: '0x89012347f123456789017f12345678abcdef01', name: 'Player Hotel', walletAddress: '0x89012347f123456789017f12345678abcdef01', seed: 8 }
];

// Test data for team tournaments
const teamParticipants2v2 = [
    { 
        id: 'team_alpha', 
        name: 'Team Alpha', 
        members: ['******************************************', '******************************************'],
        captain: '******************************************',
        seed: 1 
    },
    { 
        id: 'team_beta', 
        name: 'Team Beta', 
        members: ['******************************************', '0x456789013def123456789013def12345678abcd'],
        captain: '******************************************',
        seed: 2 
    },
    { 
        id: 'team_gamma', 
        name: 'Team Gamma', 
        members: ['0x567890124ef123456789014ef12345678abcde', '0x6789012345f123456789015f12345678abcdef'],
        captain: '0x567890124ef123456789014ef12345678abcde',
        seed: 3 
    },
    { 
        id: 'team_delta', 
        name: 'Team Delta', 
        members: ['0x789012346f123456789016f12345678abcdef0', '0x89012347f123456789017f12345678abcdef01'],
        captain: '0x789012346f123456789016f12345678abcdef0',
        seed: 4 
    }
];

const teamParticipants3v3 = [
    {
        id: 'team_alpha_3v3',
        name: 'Team Alpha 3v3',
        members: ['******************************************', '******************************************', '******************************************'],
        captain: '******************************************',
        seed: 1
    },
    {
        id: 'team_beta_3v3',
        name: 'Team Beta 3v3',
        members: ['0x456789013def123456789013def12345678abcd', '0x567890124ef123456789014ef12345678abcde', '0x6789012345f123456789015f12345678abcdef'],
        captain: '0x456789013def123456789013def12345678abcd',
        seed: 2
    },
    {
        id: 'team_gamma_3v3',
        name: 'Team Gamma 3v3',
        members: ['0x789012346f123456789016f12345678abcdef0', '0x89012347f123456789017f12345678abcdef01', '0x9012348f123456789018f12345678abcdef012'],
        captain: '0x789012346f123456789016f12345678abcdef0',
        seed: 3
    }
];

function validateDoubleEliminationStructure(bracket, testName) {
    console.log(`\n--- Validating ${testName} ---`);
    
    const { rounds, matchesById, metadata } = bracket;
    let errors = [];
    let warnings = [];
    
    // Basic structure validation
    if (bracket.format !== 'double-elimination') {
        errors.push(`Expected format 'double-elimination', got '${bracket.format}'`);
    }
    
    if (!metadata) {
        errors.push('Missing metadata');
        return { errors, warnings };
    }
    
    // Check for required metadata fields
    const requiredMetadata = ['numUpperRounds', 'numLowerRounds', 'totalMatches'];
    requiredMetadata.forEach(field => {
        if (metadata[field] === undefined) {
            errors.push(`Missing metadata field: ${field}`);
        }
    });
    
    // Validate bracket structure
    let upperBracketMatches = 0;
    let lowerBracketMatches = 0;
    let grandFinalMatches = 0;
    let matchesWithLoserLinks = 0;
    
    Object.values(matchesById).forEach(match => {
        if (match.identifier && match.identifier.includes('Grand Final')) {
            grandFinalMatches++;
        } else if (match.isUpperBracket) {
            upperBracketMatches++;
        } else {
            lowerBracketMatches++;
        }
        
        if (match.nextLoserMatchId) {
            matchesWithLoserLinks++;
        }
        
        // Validate match progression links
        if (match.nextMatchId && !matchesById[match.nextMatchId]) {
            errors.push(`Invalid nextMatchId: ${match.id} -> ${match.nextMatchId}`);
        }
        
        if (match.nextLoserMatchId && !matchesById[match.nextLoserMatchId]) {
            errors.push(`Invalid nextLoserMatchId: ${match.id} -> ${match.nextLoserMatchId}`);
        }
    });
    
    // Validate Grand Finals
    if (grandFinalMatches === 0) {
        errors.push('No Grand Final match found');
    } else if (grandFinalMatches > 2) {
        errors.push(`Too many Grand Final matches: ${grandFinalMatches}`);
    }
    
    // Validate loser bracket links
    if (matchesWithLoserLinks === 0) {
        warnings.push('No loser bracket progression links found');
    }
    
    console.log(`Upper Bracket Matches: ${upperBracketMatches}`);
    console.log(`Lower Bracket Matches: ${lowerBracketMatches}`);
    console.log(`Grand Final Matches: ${grandFinalMatches}`);
    console.log(`Matches with Loser Links: ${matchesWithLoserLinks}`);
    console.log(`Total Matches: ${Object.keys(matchesById).length}`);
    
    return { errors, warnings };
}

function testDoubleEliminationFormat(participants, testName, options = {}) {
    console.log(`\n=== TESTING ${testName} ===`);
    
    try {
        const bracket = generateBracket(participants, 'double-elimination', options);
        
        console.log(`✅ ${testName} bracket generated successfully!`);
        console.log(`Participants: ${bracket.participants.length}`);
        console.log(`Rounds: ${bracket.rounds.length}`);
        console.log(`Total Matches: ${Object.keys(bracket.matchesById).length}`);
        
        if (options.matchFormat) {
            console.log(`Match Format: ${options.matchFormat}`);
        }
        
        // Validate structure
        const validation = validateDoubleEliminationStructure(bracket, testName);
        
        if (validation.errors.length > 0) {
            console.error(`❌ Validation Errors:`);
            validation.errors.forEach(error => console.error(`  - ${error}`));
            return false;
        }
        
        if (validation.warnings.length > 0) {
            console.warn(`⚠️  Validation Warnings:`);
            validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
        }
        
        console.log(`✅ ${testName} validation passed!`);
        return true;
        
    } catch (error) {
        console.error(`❌ Error testing ${testName}:`, error.message);
        console.error(error.stack);
        return false;
    }
}

// Run all tests
console.log('Starting Double Elimination Tournament Tests...\n');

const testResults = {
    'Individual 4 Players (BO1)': testDoubleEliminationFormat(
        individualParticipants4, 
        'Individual 4 Players (BO1)', 
        { matchFormat: 'bo1' }
    ),
    'Individual 4 Players (BO3)': testDoubleEliminationFormat(
        individualParticipants4, 
        'Individual 4 Players (BO3)', 
        { matchFormat: 'bo3' }
    ),
    'Individual 8 Players (BO5)': testDoubleEliminationFormat(
        individualParticipants8, 
        'Individual 8 Players (BO5)', 
        { matchFormat: 'bo5' }
    ),
    '2v2 Teams (BO3)': testDoubleEliminationFormat(
        teamParticipants2v2, 
        '2v2 Teams (BO3)', 
        { matchFormat: 'bo3' }
    ),
    '3v3 Teams (BO7)': testDoubleEliminationFormat(
        teamParticipants3v3, 
        '3v3 Teams (BO7)', 
        { matchFormat: 'bo7' }
    )
};

// Test Summary
console.log('\n=== DOUBLE ELIMINATION TEST SUMMARY ===');
const passed = Object.values(testResults).filter(Boolean).length;
const total = Object.keys(testResults).length;

console.log(`\nPassed: ${passed}/${total}`);
Object.entries(testResults).forEach(([testName, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${testName}`);
});

if (passed === total) {
    console.log('\n🎉 All Double Elimination tests passed!');
    console.log('✅ Individual tournaments work correctly');
    console.log('✅ Team tournaments (2v2 and 3v3) work correctly');
    console.log('✅ Various match formats (BO1, BO3, BO5, BO7) are supported');
    console.log('✅ Bracket structure and progression links are valid');
    console.log('✅ Wallet addresses are properly used as participant IDs');
} else {
    console.log(`\n⚠️  ${total - passed} test(s) failed - Double Elimination needs attention`);
    process.exit(1);
}
