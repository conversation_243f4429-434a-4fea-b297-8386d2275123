// End-to-End Double Elimination Tournament Test
// Tests the complete tournament flow for both individual and team tournaments
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== END-TO-END DOUBLE ELIMINATION TOURNAMENT TEST ===\n');

// Simulate tournament creation data structures as they would come from the frontend
function createIndividualTournamentData(participants, bracketFormat = 'double-elim', matchFormat = 'bo3') {
    return {
        tournamentName: 'Test Individual Tournament',
        gameName: 'Axie Infinity: Origins',
        tournamentFormat: bracketFormat, // For individual tournaments, this IS the bracket format
        matchFormat: matchFormat,
        participants: participants,
        status: 'registrationClosed'
    };
}

function createTeamTournamentData(teamParticipants, teamSize, bracketFormat = 'double-elim', matchFormat = 'bo3') {
    return {
        tournamentName: `Test ${teamSize} Tournament`,
        gameName: 'Axie Infinity: Origins',
        tournamentFormat: teamSize, // '2v2' or '3v3'
        bracketFormat: bracketFormat, // The actual bracket format for team tournaments
        matchFormat: matchFormat,
        teamParticipants: teamParticipants,
        participants: [], // Individual participants list (empty for team tournaments)
        status: 'registrationClosed'
    };
}

// Test data
const individualParticipants = [
    '0x1234567890abcdef1234567890abcdef12345678',
    '0x2345678901bcdef12345678901bcdef123456789',
    '0x3456789012cdef123456789012cdef12345678ab',
    '0x456789013def123456789013def12345678abcd'
];

const teamParticipants2v2 = [
    {
        id: 'team_alpha',
        name: 'Team Alpha',
        members: ['0x1234567890abcdef1234567890abcdef12345678', '0x2345678901bcdef12345678901bcdef123456789'],
        captain: '0x1234567890abcdef1234567890abcdef12345678'
    },
    {
        id: 'team_beta',
        name: 'Team Beta',
        members: ['0x3456789012cdef123456789012cdef12345678ab', '0x456789013def123456789013def12345678abcd'],
        captain: '0x3456789012cdef123456789012cdef12345678ab'
    },
    {
        id: 'team_gamma',
        name: 'Team Gamma',
        members: ['0x567890124ef123456789014ef12345678abcde', '0x6789012345f123456789015f12345678abcdef'],
        captain: '0x567890124ef123456789014ef12345678abcde'
    },
    {
        id: 'team_delta',
        name: 'Team Delta',
        members: ['0x789012346f123456789016f12345678abcdef0', '0x89012347f123456789017f12345678abcdef01'],
        captain: '0x789012346f123456789016f12345678abcdef0'
    }
];

function formatWalletAddress(address) {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
}

// Simulate the bracket generation logic from generateBracketForTournament.js
function simulateBracketGeneration(tournamentData) {
    console.log(`\n=== SIMULATING: ${tournamentData.tournamentName} ===`);
    console.log(`Game: ${tournamentData.gameName}`);
    console.log(`Tournament Format: ${tournamentData.tournamentFormat}`);
    if (tournamentData.bracketFormat) {
        console.log(`Bracket Format: ${tournamentData.bracketFormat}`);
    }
    console.log(`Match Format: ${tournamentData.matchFormat}`);
    
    let participants = [];
    let bracketFormat = tournamentData.tournamentFormat;
    
    // Handle team tournaments (2v2, 3v3)
    if (tournamentData.tournamentFormat === '2v2' || tournamentData.tournamentFormat === '3v3') {
        console.log(`Processing team tournament...`);
        
        // Convert team participants to bracket participants
        participants = tournamentData.teamParticipants.map((team, index) => ({
            id: team.id,
            name: team.name,
            members: team.members,
            captain: team.captain,
            seed: index + 1
        }));
        
        // Use the bracketFormat field for team tournaments
        bracketFormat = tournamentData.bracketFormat || 'single-elim';
        console.log(`Teams: ${participants.length}`);
        participants.forEach(team => {
            console.log(`  - ${team.name}: ${team.members.map(formatWalletAddress).join(', ')}`);
        });
    } else {
        // Individual tournaments
        console.log(`Processing individual tournament...`);
        participants = tournamentData.participants.map((address, index) => ({
            id: address.toLowerCase(),
            name: formatWalletAddress(address),
            walletAddress: address.toLowerCase(),
            seed: index + 1
        }));
        
        console.log(`Players: ${participants.length}`);
        participants.forEach(player => {
            console.log(`  - ${player.name} (${player.walletAddress})`);
        });
    }
    
    try {
        // Generate the bracket
        const bracketData = generateBracket(participants, bracketFormat, {
            matchFormat: tournamentData.matchFormat
        });
        
        console.log(`\n✅ Bracket generated successfully!`);
        console.log(`Format: ${bracketData.format}`);
        console.log(`Participants: ${bracketData.participants.length}`);
        console.log(`Rounds: ${bracketData.rounds.length}`);
        console.log(`Total Matches: ${Object.keys(bracketData.matchesById).length}`);
        console.log(`Metadata:`, bracketData.metadata);
        
        // Validate bracket structure
        let validationErrors = [];
        Object.values(bracketData.matchesById).forEach(match => {
            if (match.nextMatchId && !bracketData.matchesById[match.nextMatchId]) {
                validationErrors.push(`Invalid nextMatchId: ${match.id} -> ${match.nextMatchId}`);
            }
            if (match.nextLoserMatchId && !bracketData.matchesById[match.nextLoserMatchId]) {
                validationErrors.push(`Invalid nextLoserMatchId: ${match.id} -> ${match.nextLoserMatchId}`);
            }
        });
        
        if (validationErrors.length === 0) {
            console.log(`✅ Bracket validation passed!`);
            return true;
        } else {
            console.log(`❌ Bracket validation failed:`);
            validationErrors.forEach(error => console.log(`  - ${error}`));
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Bracket generation failed: ${error.message}`);
        return false;
    }
}

// Run end-to-end tests
console.log('Testing tournament creation and bracket generation flow...\n');

const testResults = {
    'Individual Double Elimination (BO1)': simulateBracketGeneration(
        createIndividualTournamentData(individualParticipants, 'double-elim', 'bo1')
    ),
    'Individual Double Elimination (BO3)': simulateBracketGeneration(
        createIndividualTournamentData(individualParticipants, 'double-elim', 'bo3')
    ),
    'Individual Double Elimination (BO5)': simulateBracketGeneration(
        createIndividualTournamentData(individualParticipants, 'double-elim', 'bo5')
    ),
    '2v2 Team Double Elimination (BO3)': simulateBracketGeneration(
        createTeamTournamentData(teamParticipants2v2, '2v2', 'double-elim', 'bo3')
    ),
    '2v2 Team Double Elimination (BO5)': simulateBracketGeneration(
        createTeamTournamentData(teamParticipants2v2, '2v2', 'double-elim', 'bo5')
    )
};

// Test Summary
console.log('\n=== END-TO-END TEST SUMMARY ===');
const passed = Object.values(testResults).filter(Boolean).length;
const total = Object.keys(testResults).length;

console.log(`\nPassed: ${passed}/${total}`);
Object.entries(testResults).forEach(([testName, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${testName}`);
});

if (passed === total) {
    console.log('\n🎉 All end-to-end Double Elimination tests passed!');
    console.log('✅ Individual tournaments work correctly');
    console.log('✅ Team tournaments work correctly');
    console.log('✅ Tournament data structures are handled properly');
    console.log('✅ Bracket generation integrates correctly with tournament formats');
    console.log('✅ Match formats are applied correctly');
    console.log('✅ Wallet addresses are used as participant IDs');
    console.log('✅ Team structures are preserved in brackets');
} else {
    console.log(`\n⚠️  ${total - passed} test(s) failed - End-to-end flow needs attention`);
    process.exit(1);
}
