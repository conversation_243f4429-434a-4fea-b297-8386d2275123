const admin = require('firebase-admin');
const { generateBracket } = require('./bracketGenerator');

async function generateBracketForTournament(tournamentId) {
    console.log(`Generating bracket for tournament: ${tournamentId}`);
    const db = admin.firestore();
    const tournamentRef = db.collection('tournaments').doc(tournamentId);
    const tournament = await tournamentRef.get();
    const data = tournament.data();

    if (!data) {
        throw new Error('Tournament not found');
    }

    // Check for participants (individual tournaments) or team participants (team tournaments)
    const hasIndividualParticipants = data.participants && data.participants.length > 0;
    const hasTeamParticipants = data.teamParticipants && data.teamParticipants.length > 0;
    const isTeamTournament = data.tournamentFormat === '2v2' || data.tournamentFormat === '3v3';

    if (!hasIndividualParticipants && !(isTeamTournament && hasTeamParticipants)) {
        throw new Error('No participants in tournament');
    }

    // For individual tournaments, validate that all participants are wallet addresses
    let validParticipants = [];
    if (!isTeamTournament && hasIndividualParticipants) {
        validParticipants = data.participants.filter(participant => {
            // Wallet addresses are 42 characters long and start with 0x
            return typeof participant === 'string' &&
                   participant.length === 42 &&
                   participant.startsWith('0x');
        });

        if (validParticipants.length !== data.participants.length) {
            console.warn(`Tournament ${tournamentId}: Found ${data.participants.length - validParticipants.length} invalid participant IDs (likely Firebase UIDs). Only using ${validParticipants.length} valid wallet addresses.`);
        }

        if (validParticipants.length === 0) {
            throw new Error('No valid wallet addresses found in participants');
        }
    }

    // Helper function to format wallet address for display
    function formatWalletAddress(address) {
        if (!address) return 'Unknown Player';
        return `${address.slice(0, 6)}...${address.slice(-4)}`;
    }

    let participants;

    // Handle team tournaments (2v2, 3v3)
    if (data.tournamentFormat === '2v2' || data.tournamentFormat === '3v3') {
        const teamSize = data.tournamentFormat === '2v2' ? 2 : 3;

        // Get team participants from the new structure
        const teamParticipants = data.teamParticipants || [];
        const soloParticipants = [];
        const formedTeams = [];

        // Separate formed teams from solo players
        teamParticipants.forEach(team => {
            if (team.joinType === 'team' && team.members && team.members.length > 1) {
                // This is a formed team
                formedTeams.push({
                    id: team.id,
                    name: team.name,
                    seed: formedTeams.length + 1,
                    isTeam: true,
                    teamSize: teamSize,
                    members: team.members
                });
            } else if (team.joinType === 'solo' || (team.members && team.members.length === 1)) {
                // This is a solo player
                soloParticipants.push(...team.members);
            }
        });

        // Auto-group solo players into teams
        const autoTeams = [];
        for (let i = 0; i < soloParticipants.length; i += teamSize) {
            const teamMembers = soloParticipants.slice(i, i + teamSize);
            if (teamMembers.length === teamSize) {
                const teamNumber = formedTeams.length + autoTeams.length + 1;
                const memberNames = teamMembers.map(addr => formatWalletAddress(addr)).join(', ');

                autoTeams.push({
                    id: `auto_team_${teamNumber}`,
                    name: `Team ${teamNumber} (${memberNames})`,
                    seed: teamNumber,
                    isTeam: true,
                    teamSize: teamSize,
                    members: teamMembers.map(addr => addr.toLowerCase())
                });
            }
        }

        participants = [...formedTeams, ...autoTeams];
    } else {
        // Individual tournaments
        participants = validParticipants.map((address, index) => ({
            id: address.toLowerCase(), // Ensure consistent lowercase
            name: formatWalletAddress(address), // Use formatted wallet address as display name
            walletAddress: address.toLowerCase(),
            seed: index + 1
        }));
    }

    // Determine the bracket format for team tournaments
    let bracketFormat = data.tournamentFormat || 'single-elim';

    // For team tournaments (2v2, 3v3), use the bracket format or default to single-elimination
    if (data.tournamentFormat === '2v2' || data.tournamentFormat === '3v3') {
        // Use bracketFormat field if specified, otherwise default to single-elimination
        bracketFormat = data.bracketFormat || 'single-elim';
    }

    const bracketData = generateBracket(
        participants,
        bracketFormat,
        {
            matchFormat: data.matchFormat || 'bo1',
            customSeeding: data.customSeeding || false
        }
    );

    await tournamentRef.update({ bracketData: bracketData });
    return bracketData;
}

module.exports = { generateBracketForTournament };