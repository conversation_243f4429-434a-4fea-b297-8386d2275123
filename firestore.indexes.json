{"indexes": [{"collectionGroup": "tournaments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "<PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tournaments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "startDate", "order": "DESCENDING"}]}, {"collectionGroup": "tournaments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "tournaments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "registrationClosedAt", "order": "ASCENDING"}]}, {"collectionGroup": "tournaments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}