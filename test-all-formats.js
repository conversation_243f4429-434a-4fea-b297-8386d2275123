// Comprehensive test for all tournament formats with wallet addresses
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== COMPREHENSIVE TOURNAMENT FORMAT TESTING ===\n');

// Test participants with wallet addresses
const testParticipants = [
    { id: '******************************************', name: '0x1234...5678', walletAddress: '******************************************', seed: 1 },
    { id: '******************************************', name: '0x2345...6789', walletAddress: '******************************************', seed: 2 },
    { id: '******************************************', name: '0x3456...78ab', walletAddress: '******************************************', seed: 3 },
    { id: '0x456789013def123456789013def12345678abcd', name: '0x4567...abcd', walletAddress: '0x456789013def123456789013def12345678abcd', seed: 4 }
];

// Test team participants for 2v2 and 3v3
const teamParticipants2v2 = [
    { 
        id: 'team1', 
        name: 'Team Alpha', 
        members: ['******************************************', '******************************************'],
        seed: 1 
    },
    { 
        id: 'team2', 
        name: 'Team Beta', 
        members: ['******************************************', '0x456789013def123456789013def12345678abcd'],
        seed: 2 
    }
];

function testFormat(formatName, participants, options = {}) {
    console.log(`\n=== TESTING ${formatName.toUpperCase()} ===`);
    
    try {
        const bracket = generateBracket(participants, formatName, options);
        
        console.log(`✅ ${formatName} bracket generated successfully!`);
        console.log(`Format: ${bracket.format}`);
        console.log(`Participants: ${bracket.participants.length}`);
        console.log(`Rounds: ${bracket.rounds.length}`);
        console.log(`Total Matches: ${Object.keys(bracket.matchesById).length}`);
        
        if (bracket.metadata) {
            console.log(`Metadata:`, bracket.metadata);
        }
        
        // Test wallet address usage
        let walletAddressesUsed = 0;
        let matchFormatSet = 0;
        
        Object.values(bracket.matchesById).forEach(match => {
            if (match.participant1Id && match.participant1Id.startsWith('0x')) {
                walletAddressesUsed++;
            }
            if (match.participant2Id && match.participant2Id.startsWith('0x')) {
                walletAddressesUsed++;
            }
            if (match.matchFormat) {
                matchFormatSet++;
            }
        });
        
        console.log(`Wallet addresses in matches: ${walletAddressesUsed}`);
        if (options.matchFormat) {
            console.log(`Matches with format set: ${matchFormatSet}/${Object.keys(bracket.matchesById).length}`);
        }
        
        // Test progression links for elimination formats
        if (formatName.includes('elim')) {
            let linkErrors = 0;
            let loserLinks = 0;
            
            Object.values(bracket.matchesById).forEach(match => {
                if (match.nextMatchId && !bracket.matchesById[match.nextMatchId]) {
                    linkErrors++;
                    console.error(`❌ Invalid next match link: ${match.id} -> ${match.nextMatchId}`);
                }
                if (match.nextLoserMatchId) {
                    loserLinks++;
                    if (!bracket.matchesById[match.nextLoserMatchId]) {
                        linkErrors++;
                        console.error(`❌ Invalid loser match link: ${match.id} -> ${match.nextLoserMatchId}`);
                    }
                }
            });
            
            console.log(`Link validation: ${linkErrors === 0 ? '✅' : '❌'} ${linkErrors} errors`);
            if (formatName.includes('double')) {
                console.log(`Loser bracket links: ${loserLinks}`);
            }
        }
        
        return true;
    } catch (error) {
        console.error(`❌ Error testing ${formatName}:`, error.message);
        return false;
    }
}

// Test all formats
const results = {
    'single-elimination': testFormat('single-elimination', testParticipants, { matchFormat: 'bo3' }),
    'double-elimination': testFormat('double-elimination', testParticipants, { matchFormat: 'bo5' }),
    'round-robin': testFormat('round-robin', testParticipants, { matchFormat: 'bo1' }),
    '2v2': testFormat('2v2', teamParticipants2v2, { matchFormat: 'bo3' }),
    '3v3': testFormat('3v3', [
        { 
            id: 'team1', 
            name: 'Team Alpha', 
            members: ['******************************************', '******************************************', '******************************************'],
            seed: 1 
        },
        { 
            id: 'team2', 
            name: 'Team Beta', 
            members: ['0x456789013def123456789013def12345678abcd', '0x567890124ef123456789014ef12345678abcde', '0x6789012345f123456789015f12345678abcdef'],
            seed: 2 
        }
    ], { matchFormat: 'bo7' })
};

// Summary
console.log('\n=== TEST SUMMARY ===');
const passed = Object.values(results).filter(Boolean).length;
const total = Object.keys(results).length;

console.log(`Passed: ${passed}/${total}`);
Object.entries(results).forEach(([format, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${format}`);
});

if (passed === total) {
    console.log('\n🎉 All tournament formats are working correctly!');
    console.log('✅ Wallet addresses are being used as participant IDs');
    console.log('✅ Match formats are being applied');
    console.log('✅ Bracket progression links are valid');
    console.log('✅ Team formats are handled properly');
} else {
    console.log(`\n⚠️  ${total - passed} format(s) need attention`);
}
