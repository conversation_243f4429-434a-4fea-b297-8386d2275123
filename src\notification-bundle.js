/*! For license information please see notification-bundle.js.LICENSE.txt */
!function(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n(require("React")):"function"==typeof define&&define.amd?define(["React"],n):"object"==typeof exports?exports.NotificationComponents=n(require("React")):t.NotificationComponents=n(t.React)}(this,(t=>(()=>{"use strict";var n={56:(t,n,e)=>{t.exports=function(t){var n=e.nc;n&&t.setAttribute("nonce",n)}},72:t=>{var n=[];function e(t){for(var e=-1,r=0;r<n.length;r++)if(n[r].identifier===t){e=r;break}return e}function r(t,r){for(var i={},a=[],c=0;c<t.length;c++){var u=t[c],s=r.base?u[0]+r.base:u[0],l=i[s]||0,f="".concat(s," ").concat(l);i[s]=l+1;var d=e(f),h={css:u[1],media:u[2],sourceMap:u[3],supports:u[4],layer:u[5]};if(-1!==d)n[d].references++,n[d].updater(h);else{var p=o(h,r);r.byIndex=c,n.splice(c,0,{identifier:f,updater:p,references:1})}a.push(f)}return a}function o(t,n){var e=n.domAPI(n);return e.update(t),function(n){if(n){if(n.css===t.css&&n.media===t.media&&n.sourceMap===t.sourceMap&&n.supports===t.supports&&n.layer===t.layer)return;e.update(t=n)}else e.remove()}}t.exports=function(t,o){var i=r(t=t||[],o=o||{});return function(t){t=t||[];for(var a=0;a<i.length;a++){var c=e(i[a]);n[c].references--}for(var u=r(t,o),s=0;s<i.length;s++){var l=e(i[s]);0===n[l].references&&(n[l].updater(),n.splice(l,1))}i=u}}},113:t=>{t.exports=function(t,n){if(n.styleSheet)n.styleSheet.cssText=t;else{for(;n.firstChild;)n.removeChild(n.firstChild);n.appendChild(document.createTextNode(t))}}},314:t=>{t.exports=function(t){var n=[];return n.toString=function(){return this.map((function(n){var e="",r=void 0!==n[5];return n[4]&&(e+="@supports (".concat(n[4],") {")),n[2]&&(e+="@media ".concat(n[2]," {")),r&&(e+="@layer".concat(n[5].length>0?" ".concat(n[5]):""," {")),e+=t(n),r&&(e+="}"),n[2]&&(e+="}"),n[4]&&(e+="}"),e})).join("")},n.i=function(t,e,r,o,i){"string"==typeof t&&(t=[[null,t,void 0]]);var a={};if(r)for(var c=0;c<this.length;c++){var u=this[c][0];null!=u&&(a[u]=!0)}for(var s=0;s<t.length;s++){var l=[].concat(t[s]);r&&a[l[0]]||(void 0!==i&&(void 0===l[5]||(l[1]="@layer".concat(l[5].length>0?" ".concat(l[5]):""," {").concat(l[1],"}")),l[5]=i),e&&(l[2]?(l[1]="@media ".concat(l[2]," {").concat(l[1],"}"),l[2]=e):l[2]=e),o&&(l[4]?(l[1]="@supports (".concat(l[4],") {").concat(l[1],"}"),l[4]=o):l[4]="".concat(o)),n.push(l))}},n}},378:(t,n,e)=>{e.d(n,{A:()=>c});var r=e(601),o=e.n(r),i=e(314),a=e.n(i)()(o());a.push([t.id,"/* Notification List Styles - Following CyberField theme */\n\n.notification-list {\n    background: var(--bg-element-dark);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.5rem;\n    overflow: hidden;\n    max-height: 80vh;\n    display: flex;\n    flex-direction: column;\n    font-family: 'Inter', sans-serif;\n}\n\n.notification-header {\n    padding: 1rem;\n    border-bottom: 1px solid var(--border-cyber);\n    background: var(--bg-element-medium);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.notification-header h3 {\n    color: var(--accent-cyan);\n    font-family: 'Orbitron', sans-serif;\n    font-size: 1.2rem;\n    margin: 0;\n}\n\n.notification-actions {\n    display: flex;\n    gap: 0.5rem;\n    align-items: center;\n}\n\n.mark-all-read-btn,\n.delete-read-btn {\n    padding: 0.5rem 1rem;\n    font-size: 0.875rem;\n    border-radius: 0.25rem;\n    white-space: nowrap;\n}\n\n.delete-read-btn {\n    background: var(--bg-element-dark);\n    border: 1px solid #dc3545;\n    color: #dc3545;\n    transition: all 0.3s ease;\n}\n\n.delete-read-btn:hover {\n    background: #dc3545;\n    color: white;\n    border-color: #dc3545;\n}\n\n.notification-loading,\n.notification-error,\n.notification-empty {\n    padding: 2rem;\n    text-align: center;\n    color: var(--text-secondary);\n}\n\n.loading-spinner {\n    width: 2rem;\n    height: 2rem;\n    border: 2px solid var(--border-cyber);\n    border-top: 2px solid var(--accent-cyan);\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n    margin: 0 auto 1rem;\n}\n\n@keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n}\n\n.notification-error p {\n    color: #ff6b6b;\n    margin-bottom: 1rem;\n}\n\n.notification-empty {\n    padding: 3rem 2rem;\n}\n\n.empty-icon {\n    font-size: 3rem;\n    margin-bottom: 1rem;\n    opacity: 0.5;\n}\n\n.empty-subtitle {\n    font-size: 0.875rem;\n    color: var(--text-secondary);\n    opacity: 0.7;\n}\n\n.notification-items {\n    flex: 1;\n    overflow-y: auto;\n    max-height: calc(80vh - 80px);\n}\n\n.notification-item {\n    display: flex;\n    align-items: flex-start;\n    padding: 1rem;\n    border-bottom: 1px solid var(--border-cyber);\n    cursor: pointer;\n    transition: all 0.3s ease;\n    position: relative;\n}\n\n.notification-item:hover {\n    background: var(--bg-element-medium);\n    border-color: var(--accent-blue);\n}\n\n.notification-item:last-child {\n    border-bottom: none;\n}\n\n.notification-item.unread {\n    background: rgba(var(--accent-blue-rgb), 0.05);\n    border-left: 3px solid var(--accent-cyan);\n}\n\n.notification-item.unread:hover {\n    background: rgba(var(--accent-blue-rgb), 0.1);\n}\n\n.notification-icon {\n    font-size: 1.5rem;\n    margin-right: 1rem;\n    flex-shrink: 0;\n    margin-top: 0.25rem;\n}\n\n.notification-content {\n    flex: 1;\n    min-width: 0;\n}\n\n.notification-title {\n    color: var(--text-primary);\n    font-weight: 600;\n    font-size: 0.95rem;\n    margin-bottom: 0.25rem;\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n}\n\n.unread-dot {\n    width: 6px;\n    height: 6px;\n    background: var(--accent-cyan);\n    border-radius: 50%;\n    flex-shrink: 0;\n}\n\n.notification-message {\n    color: var(--text-secondary);\n    font-size: 0.875rem;\n    line-height: 1.4;\n    margin-bottom: 0.5rem;\n    word-wrap: break-word;\n}\n\n.notification-time {\n    color: var(--text-secondary);\n    font-size: 0.75rem;\n    opacity: 0.7;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n    .notification-list {\n        max-height: 70vh;\n        border-radius: 0;\n        border-left: none;\n        border-right: none;\n    }\n\n    .notification-header {\n        padding: 0.75rem;\n    }\n\n    .notification-header h3 {\n        font-size: 1.1rem;\n    }\n\n    .mark-all-read-btn {\n        padding: 0.375rem 0.75rem;\n        font-size: 0.8rem;\n    }\n\n    .notification-item {\n        padding: 0.75rem;\n    }\n\n    .notification-icon {\n        font-size: 1.25rem;\n        margin-right: 0.75rem;\n    }\n\n    .notification-title {\n        font-size: 0.9rem;\n    }\n\n    .notification-message {\n        font-size: 0.8rem;\n    }\n\n    .notification-items {\n        max-height: calc(70vh - 70px);\n    }\n}\n\n/* Scrollbar Styling */\n.notification-items::-webkit-scrollbar {\n    width: 6px;\n}\n\n.notification-items::-webkit-scrollbar-track {\n    background: var(--bg-element-dark);\n}\n\n.notification-items::-webkit-scrollbar-thumb {\n    background: var(--border-cyber);\n    border-radius: 3px;\n}\n\n.notification-items::-webkit-scrollbar-thumb:hover {\n    background: var(--accent-blue);\n}\n\n/* Animation for new notifications */\n@keyframes slideInFromTop {\n    from {\n        opacity: 0;\n        transform: translateY(-10px);\n    }\n    to {\n        opacity: 1;\n        transform: translateY(0);\n    }\n}\n\n.notification-item.new {\n    animation: slideInFromTop 0.3s ease-out;\n}\n",""]);const c=a},540:t=>{t.exports=function(t){var n=document.createElement("style");return t.setAttributes(n,t.attributes),t.insert(n,t.options),n}},601:t=>{t.exports=function(t){return t[1]}},659:t=>{var n={};t.exports=function(t,e){var r=function(t){if(void 0===n[t]){var e=document.querySelector(t);if(window.HTMLIFrameElement&&e instanceof window.HTMLIFrameElement)try{e=e.contentDocument.head}catch(t){e=null}n[t]=e}return n[t]}(t);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(e)}},825:t=>{t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var n=t.insertStyleElement(t);return{update:function(e){!function(t,n,e){var r="";e.supports&&(r+="@supports (".concat(e.supports,") {")),e.media&&(r+="@media ".concat(e.media," {"));var o=void 0!==e.layer;o&&(r+="@layer".concat(e.layer.length>0?" ".concat(e.layer):""," {")),r+=e.css,o&&(r+="}"),e.media&&(r+="}"),e.supports&&(r+="}");var i=e.sourceMap;i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),n.styleTagTransform(r,t,n.options)}(n,t,e)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(n)}}}},883:n=>{n.exports=t}},e={};function r(t){var o=e[t];if(void 0!==o)return o.exports;var i=e[t]={id:t,exports:{}};return n[t](i,i.exports,r),i.exports}r.n=t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return r.d(n,{a:n}),n},r.d=(t,n)=>{for(var e in n)r.o(n,e)&&!r.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:n[e]})},r.o=(t,n)=>Object.prototype.hasOwnProperty.call(t,n),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0;var o={};r.r(o),r.d(o,{NotificationBadge:()=>d,NotificationList:()=>z});var i=r(883),a=r.n(i);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){u=function(){return n};var t,n={},e=Object.prototype,r=e.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",s=o.toStringTag||"@@toStringTag";function l(t,n,e,r){return Object.defineProperty(t,n,{value:e,enumerable:!r,configurable:!r,writable:!r})}try{l({},"")}catch(t){l=function(t,n,e){return t[n]=e}}function f(n,e,r,o){var i=e&&e.prototype instanceof p?e:p,a=Object.create(i.prototype);return l(a,"_invoke",function(n,e,r){var o=1;return function(i,a){if(3===o)throw Error("Generator is already running");if(4===o){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=k(c,r);if(u){if(u===h)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===o)throw o=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=3;var s=d(n,e,r);if("normal"===s.type){if(o=r.done?4:2,s.arg===h)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=4,r.method="throw",r.arg=s.arg)}}}(n,r,new j(o||[])),!0),a}function d(t,n,e){try{return{type:"normal",arg:t.call(n,e)}}catch(t){return{type:"throw",arg:t}}}n.wrap=f;var h={};function p(){}function m(){}function v(){}var y={};l(y,i,(function(){return this}));var g=Object.getPrototypeOf,b=g&&g(g(O([])));b&&b!==e&&r.call(b,i)&&(y=b);var w=v.prototype=p.prototype=Object.create(y);function x(t){["next","throw","return"].forEach((function(n){l(t,n,(function(t){return this._invoke(n,t)}))}))}function E(t,n){function e(o,i,a,u){var s=d(t[o],t,i);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==c(f)&&r.call(f,"__await")?n.resolve(f.__await).then((function(t){e("next",t,a,u)}),(function(t){e("throw",t,a,u)})):n.resolve(f).then((function(t){l.value=t,a(l)}),(function(t){return e("throw",t,a,u)}))}u(s.arg)}var o;l(this,"_invoke",(function(t,r){function i(){return new n((function(n,o){e(t,r,n,o)}))}return o=o?o.then(i,i):i()}),!0)}function k(n,e){var r=e.method,o=n.i[r];if(o===t)return e.delegate=null,"throw"===r&&n.i.return&&(e.method="return",e.arg=t,k(n,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var i=d(o,n.i,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,h;var a=i.arg;return a?a.done?(e[n.r]=a.value,e.next=n.n,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,h):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function S(t){this.tryEntries.push(t)}function N(n){var e=n[4]||{};e.type="normal",e.arg=t,n[4]=e}function j(t){this.tryEntries=[[-1]],t.forEach(S,this),this.reset(!0)}function O(n){if(null!=n){var e=n[i];if(e)return e.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var o=-1,a=function e(){for(;++o<n.length;)if(r.call(n,o))return e.value=n[o],e.done=!1,e;return e.value=t,e.done=!0,e};return a.next=a}}throw new TypeError(c(n)+" is not iterable")}return m.prototype=v,l(w,"constructor",v),l(v,"constructor",m),m.displayName=l(v,s,"GeneratorFunction"),n.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===m||"GeneratorFunction"===(n.displayName||n.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,s,"GeneratorFunction")),t.prototype=Object.create(w),t},n.awrap=function(t){return{__await:t}},x(E.prototype),l(E.prototype,a,(function(){return this})),n.AsyncIterator=E,n.async=function(t,e,r,o,i){void 0===i&&(i=Promise);var a=new E(f(t,e,r,o),i);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},x(w),l(w,s,"Generator"),l(w,i,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var n=Object(t),e=[];for(var r in n)e.unshift(r);return function t(){for(;e.length;)if((r=e.pop())in n)return t.value=r,t.done=!1,t;return t.done=!0,t}},n.values=O,j.prototype={constructor:j,reset:function(n){if(this.prev=this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(N),!n)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var e=this;function r(t){a.type="throw",a.arg=n,e.next=t}for(var o=e.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i[4],c=this.prev,u=i[1],s=i[2];if(-1===i[0])return r("end"),!1;if(!u&&!s)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=c){if(c<u)return this.method="next",this.arg=t,r(u),!0;if(c<s)return r(s),!1}}},abrupt:function(t,n){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var o=r;break}}o&&("break"===t||"continue"===t)&&o[0]<=n&&n<=o[2]&&(o=null);var i=o?o[4]:{};return i.type=t,i.arg=n,o?(this.method="next",this.next=o[2],h):this.complete(i)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),h},finish:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e[2]===t)return this.complete(e[4],e[3]),N(e),h}},catch:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e[0]===t){var r=e[4];if("throw"===r.type){var o=r.arg;N(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(n,e,r){return this.delegate={i:O(n),r:e,n:r},"next"===this.method&&(this.arg=t),h}},n}function s(t,n,e,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void e(t)}c.done?n(u):Promise.resolve(u).then(r,o)}function l(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i,a,c=[],u=!0,s=!1;try{if(i=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;u=!1}else for(;!(u=(r=i.call(e)).done)&&(c.push(r.value),c.length!==n);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,n)||function(t,n){if(t){if("string"==typeof t)return f(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?f(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}const d=function(t){var n=t.userId,e=t.className,r=void 0===e?"":e,o=l((0,i.useState)(0),2),c=o[0],f=o[1],d=l((0,i.useState)(!0),2),h=d[0],p=d[1];if((0,i.useEffect)((function(){if(!n)return f(0),void p(!1);var t=function(){var t,e=(t=u().mark((function t(){var e;return u().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,!window.notificationService){t.next=8;break}return t.next=4,window.notificationService.getUserNotifications(n,100,!0);case 4:e=t.sent,f(e.length),t.next=9;break;case 8:f(0);case 9:t.next=15;break;case 11:t.prev=11,t.t0=t.catch(0),console.error("Error loading unread notification count:",t.t0),f(0);case 15:return t.prev=15,p(!1),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[0,11,15,18]])})),function(){var n=this,e=arguments;return new Promise((function(r,o){var i=t.apply(n,e);function a(t){s(i,r,o,a,c,"next",t)}function c(t){s(i,r,o,a,c,"throw",t)}a(void 0)}))});return function(){return e.apply(this,arguments)}}();t();var e=setInterval(t,3e4),r=function(){clearInterval(e)};return function(){r&&r()}}),[n]),h||0===c)return null;var m=c>99?"99+":c.toString();return a().createElement("span",{className:"absolute top-0.5 right-0.5 block h-2.5 w-2.5 rounded-full ring-2 ring-offset-1 ring-offset-[var(--bg-deep-space)] ring-red-500 bg-red-400 ".concat(r),style:{display:"flex",alignItems:"center",justifyContent:"center",minWidth:c>9?"20px":"10px",height:c>9?"20px":"10px",fontSize:c>9?"8px":"0px",fontWeight:"bold",color:"white"},title:"".concat(c," unread notification").concat(1!==c?"s":"")},c>9?m:"")};var h=r(72),p=r.n(h),m=r(825),v=r.n(m),y=r(659),g=r.n(y),b=r(56),w=r.n(b),x=r(540),E=r.n(x),k=r(113),S=r.n(k),N=r(378),j={};function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function _(t,n){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=M(t))||n&&t&&"number"==typeof t.length){e&&(t=e);var r=0,o=function(){};return{s:o,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,c=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return a=t.done,t},e:function(t){c=!0,i=t},f:function(){try{a||null==e.return||e.return()}finally{if(c)throw i}}}}function A(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function P(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?A(Object(e),!0).forEach((function(n){I(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):A(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function I(t,n,e){return(n=function(t){var n=function(t){if("object"!=O(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var e=n.call(t,"string");if("object"!=O(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==O(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function T(){T=function(){return n};var t,n={},e=Object.prototype,r=e.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,n,e,r){return Object.defineProperty(t,n,{value:e,enumerable:!r,configurable:!r,writable:!r})}try{u({},"")}catch(t){u=function(t,n,e){return t[n]=e}}function s(n,e,r,o){var i=e&&e.prototype instanceof d?e:d,a=Object.create(i.prototype);return u(a,"_invoke",function(n,e,r){var o=1;return function(i,a){if(3===o)throw Error("Generator is already running");if(4===o){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var u=x(c,r);if(u){if(u===f)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===o)throw o=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=3;var s=l(n,e,r);if("normal"===s.type){if(o=r.done?4:2,s.arg===f)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=4,r.method="throw",r.arg=s.arg)}}}(n,r,new S(o||[])),!0),a}function l(t,n,e){try{return{type:"normal",arg:t.call(n,e)}}catch(t){return{type:"throw",arg:t}}}n.wrap=s;var f={};function d(){}function h(){}function p(){}var m={};u(m,i,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(N([])));y&&y!==e&&r.call(y,i)&&(m=y);var g=p.prototype=d.prototype=Object.create(m);function b(t){["next","throw","return"].forEach((function(n){u(t,n,(function(t){return this._invoke(n,t)}))}))}function w(t,n){function e(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==O(f)&&r.call(f,"__await")?n.resolve(f.__await).then((function(t){e("next",t,a,c)}),(function(t){e("throw",t,a,c)})):n.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return e("throw",t,a,c)}))}c(u.arg)}var o;u(this,"_invoke",(function(t,r){function i(){return new n((function(n,o){e(t,r,n,o)}))}return o=o?o.then(i,i):i()}),!0)}function x(n,e){var r=e.method,o=n.i[r];if(o===t)return e.delegate=null,"throw"===r&&n.i.return&&(e.method="return",e.arg=t,x(n,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var i=l(o,n.i,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,f;var a=i.arg;return a?a.done?(e[n.r]=a.value,e.next=n.n,"return"!==e.method&&(e.method="next",e.arg=t),e.delegate=null,f):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,f)}function E(t){this.tryEntries.push(t)}function k(n){var e=n[4]||{};e.type="normal",e.arg=t,n[4]=e}function S(t){this.tryEntries=[[-1]],t.forEach(E,this),this.reset(!0)}function N(n){if(null!=n){var e=n[i];if(e)return e.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var o=-1,a=function e(){for(;++o<n.length;)if(r.call(n,o))return e.value=n[o],e.done=!1,e;return e.value=t,e.done=!0,e};return a.next=a}}throw new TypeError(O(n)+" is not iterable")}return h.prototype=p,u(g,"constructor",p),u(p,"constructor",h),h.displayName=u(p,c,"GeneratorFunction"),n.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===h||"GeneratorFunction"===(n.displayName||n.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,u(t,c,"GeneratorFunction")),t.prototype=Object.create(g),t},n.awrap=function(t){return{__await:t}},b(w.prototype),u(w.prototype,a,(function(){return this})),n.AsyncIterator=w,n.async=function(t,e,r,o,i){void 0===i&&(i=Promise);var a=new w(s(t,e,r,o),i);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},b(g),u(g,c,"Generator"),u(g,i,(function(){return this})),u(g,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var n=Object(t),e=[];for(var r in n)e.unshift(r);return function t(){for(;e.length;)if((r=e.pop())in n)return t.value=r,t.done=!1,t;return t.done=!0,t}},n.values=N,S.prototype={constructor:S,reset:function(n){if(this.prev=this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!n)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0][4];if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var e=this;function r(t){a.type="throw",a.arg=n,e.next=t}for(var o=e.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i[4],c=this.prev,u=i[1],s=i[2];if(-1===i[0])return r("end"),!1;if(!u&&!s)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=c){if(c<u)return this.method="next",this.arg=t,r(u),!0;if(c<s)return r(s),!1}}},abrupt:function(t,n){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var o=r;break}}o&&("break"===t||"continue"===t)&&o[0]<=n&&n<=o[2]&&(o=null);var i=o?o[4]:{};return i.type=t,i.arg=n,o?(this.method="next",this.next=o[2],f):this.complete(i)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),f},finish:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e[2]===t)return this.complete(e[4],e[3]),k(e),f}},catch:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e[0]===t){var r=e[4];if("throw"===r.type){var o=r.arg;k(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(n,e,r){return this.delegate={i:N(n),r:e,n:r},"next"===this.method&&(this.arg=t),f}},n}function R(t,n,e,r,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void e(t)}c.done?n(u):Promise.resolve(u).then(r,o)}function C(t){return function(){var n=this,e=arguments;return new Promise((function(r,o){var i=t.apply(n,e);function a(t){R(i,r,o,a,c,"next",t)}function c(t){R(i,r,o,a,c,"throw",t)}a(void 0)}))}}function F(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i,a,c=[],u=!0,s=!1;try{if(i=(e=e.call(t)).next,0===n){if(Object(e)!==e)return;u=!1}else for(;!(u=(r=i.call(e)).done)&&(c.push(r.value),c.length!==n);u=!0);}catch(t){s=!0,o=t}finally{try{if(!u&&null!=e.return&&(a=e.return(),Object(a)!==a))return}finally{if(s)throw o}}return c}}(t,n)||M(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,n){if(t){if("string"==typeof t)return G(t,n);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?G(t,n):void 0}}function G(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=Array(n);e<n;e++)r[e]=t[e];return r}j.styleTagTransform=S(),j.setAttributes=w(),j.insert=g().bind(null,"head"),j.domAPI=v(),j.insertStyleElement=E(),p()(N.A,j),N.A&&N.A.locals&&N.A.locals;const z=function(t){var n=t.userId,e=t.onNotificationClick,r=F((0,i.useState)([]),2),o=r[0],c=r[1],u=F((0,i.useState)(!0),2),s=u[0],l=u[1],f=F((0,i.useState)(null),2),d=f[0],h=f[1];(0,i.useEffect)((function(){if(!n)return c([]),void l(!1);var t=function(){var t=C(T().mark((function t(){var e;return T().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,l(!0),!window.notificationService){t.next=10;break}return t.next=5,window.notificationService.getUserNotifications(n,50);case 5:e=t.sent,c(e),h(null),t.next=12;break;case 10:c([]),h("Notification service not available");case 12:t.next=19;break;case 14:t.prev=14,t.t0=t.catch(0),console.error("Error loading notifications:",t.t0),h("Failed to load notifications"),c([]);case 19:return t.prev=19,l(!1),t.finish(19);case 22:case"end":return t.stop()}}),t,null,[[0,14,19,22]])})));return function(){return t.apply(this,arguments)}}();t();var e=setInterval(t,3e4),r=function(){clearInterval(e)};return function(){r&&r()}}),[n]);var p=function(){var t=C(T().mark((function t(n){return T().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,n.isRead||!window.notificationService){t.next=5;break}return t.next=4,window.notificationService.markNotificationAsRead(n.id);case 4:c((function(t){return t.map((function(t){return t.id===n.id?P(P({},t),{},{isRead:!0}):t}))}));case 5:e&&e(n),t.next=11;break;case 8:t.prev=8,t.t0=t.catch(0),console.error("Error handling notification click:",t.t0);case 11:case"end":return t.stop()}}),t,null,[[0,8]])})));return function(n){return t.apply(this,arguments)}}(),m=function(){var t=C(T().mark((function t(){var n,e,r,i;return T().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,!window.notificationService){t.next=21;break}n=o.filter((function(t){return!t.isRead})),e=_(n),t.prev=4,e.s();case 6:if((r=e.n()).done){t.next=12;break}return i=r.value,t.next=10,window.notificationService.markNotificationAsRead(i.id);case 10:t.next=6;break;case 12:t.next=17;break;case 14:t.prev=14,t.t0=t.catch(4),e.e(t.t0);case 17:return t.prev=17,e.f(),t.finish(17);case 20:c((function(t){return t.map((function(t){return P(P({},t),{},{isRead:!0})}))}));case 21:t.next=26;break;case 23:t.prev=23,t.t1=t.catch(0),console.error("Error marking all notifications as read:",t.t1);case 26:case"end":return t.stop()}}),t,null,[[0,23],[4,14,17,20]])})));return function(){return t.apply(this,arguments)}}(),v=function(){var t=C(T().mark((function t(){var e;return T().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,!window.notificationService||!n){t.next=6;break}return t.next=4,window.notificationService.deleteReadNotifications(n);case 4:(e=t.sent).success?(c((function(t){return t.filter((function(t){return!t.isRead}))})),console.log("Successfully deleted ".concat(e.deletedCount," read notifications"))):console.error("Failed to delete read notifications:",e.error);case 6:t.next=11;break;case 8:t.prev=8,t.t0=t.catch(0),console.error("Error deleting read notifications:",t.t0);case 11:case"end":return t.stop()}}),t,null,[[0,8]])})));return function(){return t.apply(this,arguments)}}();if(s)return a().createElement("div",{className:"notification-list"},a().createElement("div",{className:"notification-header"},a().createElement("h3",null,"Notifications")),a().createElement("div",{className:"notification-loading"},a().createElement("div",{className:"loading-spinner"}),a().createElement("p",null,"Loading notifications...")));if(d)return a().createElement("div",{className:"notification-list"},a().createElement("div",{className:"notification-header"},a().createElement("h3",null,"Notifications")),a().createElement("div",{className:"notification-error"},a().createElement("p",null,d),a().createElement("button",{className:"btn btn-cyber-secondary",onClick:function(){return window.location.reload()}},"Retry")));var y=o.filter((function(t){return!t.isRead})).length,g=o.filter((function(t){return t.isRead})).length;return a().createElement("div",{className:"notification-list"},a().createElement("div",{className:"notification-header"},a().createElement("h3",null,"Notifications"),a().createElement("div",{className:"notification-actions"},y>0&&a().createElement("button",{className:"btn btn-cyber-secondary mark-all-read-btn",onClick:m,title:"Mark all as read"},"Mark All Read"),g>0&&a().createElement("button",{className:"btn btn-cyber-danger delete-read-btn",onClick:v,title:"Delete all read messages"},"Delete All Read"))),0===o.length?a().createElement("div",{className:"notification-empty"},a().createElement("div",{className:"empty-icon"},"🔔"),a().createElement("p",null,"No notifications yet"),a().createElement("span",{className:"empty-subtitle"},"You'll see tournament updates and match alerts here")):a().createElement("div",{className:"notification-items"},o.map((function(t){return a().createElement("div",{key:t.id,className:"notification-item ".concat(t.isRead?"read":"unread"),onClick:function(){return p(t)}},a().createElement("div",{className:"notification-icon"},function(t){switch(t){case"tournament_registration_closed":return"🔒";case"tournament_live":return"🚀";case"match_dispute":return"⚠️";case"upcoming_match":return"⚔️";default:return"📢"}}(t.type)),a().createElement("div",{className:"notification-content"},a().createElement("div",{className:"notification-title"},t.title,!t.isRead&&a().createElement("span",{className:"unread-dot"})),a().createElement("div",{className:"notification-message"},t.message),a().createElement("div",{className:"notification-time"},(n=t.createdAt,e=new Date,(r=Math.floor((e-n)/1e3))<60?"Just now":r<3600?"".concat(Math.floor(r/60),"m ago"):r<86400?"".concat(Math.floor(r/3600),"h ago"):r<604800?"".concat(Math.floor(r/86400),"d ago"):n.toLocaleDateString()))));var n,e,r}))))};return window.NotificationComponents={NotificationBadge:d,NotificationList:z},o})()));