/*! For license information please see bracket-bundle.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("React"),require("ReactDOM")):"function"==typeof define&&define.amd?define(["React","ReactDOM"],t):"object"==typeof exports?exports.BracketComponents=t(require("React"),require("ReactDOM")):e.BracketComponents=t(e.React,e.ReactDOM)}(this,((e,t)=>(()=>{"use strict";var n={56:(e,t,n)=>{e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},72:e=>{var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var i={},o=[],s=0;s<e.length;s++){var c=e[s],l=r.base?c[0]+r.base:c[0],u=i[l]||0,d="".concat(l," ").concat(u);i[l]=u+1;var p=n(d),m={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)t[p].references++,t[p].updater(m);else{var f=a(m,r);r.byIndex=s,t.splice(s,0,{identifier:d,updater:f,references:1})}o.push(d)}return o}function a(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,a){var i=r(e=e||[],a=a||{});return function(e){e=e||[];for(var o=0;o<i.length;o++){var s=n(i[o]);t[s].references--}for(var c=r(e,a),l=0;l<i.length;l++){var u=n(i[l]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}i=c}}},113:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},314:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,a,i){"string"==typeof e&&(e=[[null,e,void 0]]);var o={};if(r)for(var s=0;s<this.length;s++){var c=this[s][0];null!=c&&(o[c]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);r&&o[u[0]]||(void 0!==i&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=i),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),a&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=a):u[4]="".concat(a)),t.push(u))}},t}},540:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},560:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(601),a=n.n(r),i=n(314),o=n.n(i)()(a());o.push([e.id,".bracket-loading,\n.bracket-error {\n    text-align: center;\n    padding: 2rem;\n    color: var(--text-secondary);\n    font-family: 'Orbitron', sans-serif;\n    background-color: var(--bg-element-dark);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.5rem;\n    margin: 1rem 0;\n}\n\n.bracket-error {\n    color: #ff8080;\n    border-color: rgba(255, 80, 80, 0.5);\n}\n\n/* Bracket Container */\n.bracket-container {\n    background-color: var(--bg-element-dark);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.5rem;\n    padding: 1.5rem;\n    margin: 1rem 0;\n    box-shadow: 0 4px 15px rgba(0,0,0,0.3), 0 0 10px rgba(var(--accent-blue-rgb), 0.15);\n}\n\n/* Match Box */\n.match-box {\n    background-color: var(--bg-element-medium);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.375rem;\n    padding: 1rem;\n    margin: 0.5rem;\n    transition: all 0.3s ease;\n}\n\n.match-box:hover {\n    border-color: var(--accent-blue);\n    box-shadow: 0 0 15px rgba(var(--accent-blue-rgb), 0.3);\n}\n\n/* Player Names */\n.player-name {\n    font-family: 'Orbitron', sans-serif;\n    font-size: 0.9rem;\n    color: var(--text-primary);\n    margin: 0.25rem 0;\n}\n\n.player-name.winner {\n    color: var(--accent-cyan);\n    text-shadow: 0 0 8px rgba(var(--accent-cyan-rgb), 0.6);\n}\n\n.player-name.loser {\n    color: var(--text-secondary);\n    opacity: 0.7;\n}\n\n/* Score Display */\n.score-display {\n    font-family: 'Orbitron', sans-serif;\n    font-weight: 700;\n    color: var(--accent-magenta);\n    text-shadow: 0 0 8px rgba(var(--accent-magenta-rgb), 0.6);\n    margin: 0.5rem 0;\n}\n\n/* Round Labels */\n.round-label {\n    font-family: 'Orbitron', sans-serif;\n    color: var(--accent-cyan);\n    text-transform: uppercase;\n    font-size: 0.8rem;\n    margin-bottom: 1rem;\n    text-shadow: 0 0 8px rgba(var(--accent-cyan-rgb), 0.6);\n}\n\n/* Match Status */\n.match-status {\n    font-size: 0.8rem;\n    padding: 0.25rem 0.5rem;\n    border-radius: 9999px;\n    margin-top: 0.5rem;\n    display: inline-block;\n}\n\n.match-status.upcoming {\n    background-color: rgba(var(--accent-blue-rgb), 0.2);\n    color: var(--accent-blue);\n    border: 1px solid rgba(var(--accent-blue-rgb), 0.5);\n}\n\n.match-status.live {\n    background-color: rgba(255, 80, 80, 0.2);\n    color: #ff8080;\n    border: 1px solid rgba(255, 80, 80, 0.5);\n}\n\n.match-status.completed {\n    background-color: rgba(60, 200, 130, 0.2);\n    color: #80FFC0;\n    border: 1px solid rgba(60, 200, 130, 0.5);\n}\n\n/* Bracket Lines */\n.bracket-line {\n    position: absolute;\n    background-color: var(--border-cyber);\n    z-index: 1;\n}\n\n.bracket-line.vertical {\n    width: 2px;\n    background: linear-gradient(to bottom, var(--accent-cyan), var(--accent-magenta));\n}\n\n.bracket-line.horizontal {\n    height: 2px;\n    background: linear-gradient(to right, var(--accent-cyan), var(--accent-magenta));\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n    .bracket-container {\n        padding: 1rem;\n        overflow-x: auto;\n    }\n\n    .match-box {\n        padding: 0.75rem;\n        margin: 0.25rem;\n    }\n\n    .player-name {\n        font-size: 0.8rem;\n    }\n} ",""]);const s=o},601:e=>{e.exports=function(e){return e[1]}},659:e=>{var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},761:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(601),a=n.n(r),i=n(314),o=n.n(i)()(a());o.push([e.id,".bracket-container {\n    display: flex;\n    flex-direction: column;\n    gap: 2rem;\n    padding: 1rem;\n    overflow-x: auto;\n    width: 100%;\n}\n\n/* Single Elimination Styles */\n.bracket-container.single-elimination {\n    display: flex;\n    flex-direction: row;\n    justify-content: flex-start;\n    align-items: flex-start;\n    overflow-x: auto;\n    padding-bottom: 1rem;\n    min-height: 400px;\n    width: 100%;\n}\n\n.bracket-container.single-elimination .bracket-content {\n    display: flex;\n    flex-direction: row;\n    gap: 2rem;\n    padding: 1rem;\n    min-width: min-content;\n}\n\n/* Double Elimination Styles */\n.bracket-container.double-elimination {\n    display: flex;\n    flex-direction: column;\n}\n\n.bracket-section {\n    margin-bottom: 2rem;\n}\n\n.bracket-section h2 {\n    color: var(--accent-cyan);\n    margin-bottom: 1rem;\n    font-family: 'Orbitron', sans-serif;\n}\n\n/* Round Styles */\n.round {\n    display: flex;\n    flex-direction: column;\n    gap: 1rem;\n    min-width: 250px;\n    margin-right: 2rem;\n    flex-shrink: 0;\n}\n\n.round-header {\n    text-align: center;\n    margin-bottom: 0.5rem;\n}\n\n.round-header h3 {\n    color: var(--accent-magenta);\n    font-size: 1.1rem;\n    font-family: 'Orbitron', sans-serif;\n}\n\n/* Match Styles */\n.match-container {\n    background-color: var(--bg-element-dark);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.5rem;\n    overflow: hidden;\n    transition: all 0.3s ease;\n}\n\n.match-container:hover {\n    border-color: var(--accent-blue);\n    box-shadow: 0 0 15px rgba(var(--accent-blue-rgb), 0.3);\n}\n\n.match-header {\n    background-color: var(--bg-element-medium);\n    padding: 0.5rem;\n    border-bottom: 1px solid var(--border-cyber);\n}\n\n.match-id {\n    font-size: 0.8rem;\n    color: var(--text-secondary);\n    font-family: 'Orbitron', sans-serif;\n}\n\n.match-content {\n    padding: 0.75rem;\n}\n\n.participant {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 0.5rem;\n    border-radius: 0.25rem;\n    margin-bottom: 0.25rem;\n    background-color: var(--bg-element-medium);\n}\n\n.participant:last-child {\n    margin-bottom: 0;\n}\n\n.participant.winner {\n    background-color: rgba(60, 200, 130, 0.2);\n    border: 1px solid rgba(60, 200, 130, 0.5);\n}\n\n.participant-name {\n    font-size: 0.9rem;\n    color: var(--text-primary);\n}\n\n.score {\n    font-family: 'Orbitron', sans-serif;\n    color: var(--accent-cyan);\n    font-weight: bold;\n}\n\n/* Status-specific styles */\n.match-container.pending {\n    border-color: var(--border-cyber);\n}\n\n.match-container.bye {\n    border-color: var(--accent-cyan);\n    opacity: 0.7;\n}\n\n.match-container.participant1_win,\n.match-container.participant2_win {\n    border-color: var(--accent-magenta);\n}\n\n/* Round Robin specific styles */\n.standings-section {\n    margin-bottom: 2rem;\n}\n\n.standings-table {\n    width: 100%;\n    border-collapse: collapse;\n    background-color: var(--bg-element-dark);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.5rem;\n    overflow: hidden;\n}\n\n.standings-table th,\n.standings-table td {\n    padding: 0.75rem;\n    text-align: left;\n    border-bottom: 1px solid var(--border-cyber);\n}\n\n.standings-table th {\n    background-color: var(--bg-element-medium);\n    color: var(--accent-cyan);\n    font-family: 'Orbitron', sans-serif;\n}\n\n.standings-table tr:last-child td {\n    border-bottom: none;\n}\n\n.standings-table tr:hover {\n    background-color: var(--bg-element-medium);\n}\n\n/* Loading and Error States */\n.bracket-loading,\n.bracket-error {\n    text-align: center;\n    padding: 2rem;\n    color: var(--text-secondary);\n    font-size: 1.1rem;\n}\n\n.bracket-error {\n    color: #ff6b6b;\n}\n\n/* Match Result Submission Styles */\n.match-result-submission {\n    background-color: var(--bg-element-dark);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.5rem;\n    padding: 1.5rem;\n    margin-top: 1rem;\n}\n\n.match-result-submission h4 {\n    color: var(--accent-cyan);\n    margin-bottom: 1rem;\n}\n\n.score-inputs {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 1rem;\n    margin-bottom: 1rem;\n}\n\n.score-inputs input {\n    width: 80px;\n    text-align: center;\n}\n\n.winner-selection {\n    display: flex;\n    flex-direction: column;\n    gap: 0.5rem;\n}\n\n.winner-selection button {\n    width: 100%;\n    text-align: center;\n}\n\n.admin-resolve {\n    border-top: 1px solid var(--border-cyber);\n    padding-top: 1rem;\n    margin-top: 1rem;\n}\n\n.admin-resolve h5 {\n    color: var(--accent-magenta);\n    margin-bottom: 0.5rem;\n}\n\n.error-message {\n    background-color: rgba(255, 107, 107, 0.1);\n    border: 1px solid rgba(255, 107, 107, 0.3);\n    padding: 0.75rem;\n    border-radius: 0.25rem;\n    margin-bottom: 1rem;\n}\n\n.warning-message {\n    background-color: rgba(255, 193, 7, 0.1);\n    border: 1px solid rgba(255, 193, 7, 0.3);\n    padding: 0.75rem;\n    border-radius: 0.25rem;\n    margin-bottom: 1rem;\n    color: #ffc107;\n    font-size: 0.9rem;\n}\n\n.warning-message h3 {\n    margin: 0 0 0.5rem 0;\n    color: #ffc107;\n    font-size: 1rem;\n}\n\n.warning-message p {\n    margin: 0.25rem 0;\n    line-height: 1.4;\n}\n\n/* Match Details Panel */\n.match-details-panel {\n    position: fixed;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    background-color: var(--bg-element-dark);\n    border: 1px solid var(--border-cyber);\n    border-radius: 0.5rem;\n    padding: 2rem;\n    width: 90%;\n    max-width: 500px;\n    z-index: 1000;\n    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);\n}\n\n.close-panel-btn {\n    position: absolute;\n    top: 1rem;\n    right: 1rem;\n    background: none;\n    border: none;\n    color: var(--text-secondary);\n    font-size: 1.5rem;\n    cursor: pointer;\n    padding: 0.5rem;\n    line-height: 1;\n}\n\n.close-panel-btn:hover {\n    color: var(--accent-cyan);\n}\n\n/* Match Status Badges */\n.dispute-badge {\n    background-color: rgba(255, 107, 107, 0.2);\n    color: #ff6b6b;\n    border: 1px solid rgba(255, 107, 107, 0.3);\n    padding: 0.25rem 0.5rem;\n    border-radius: 9999px;\n    font-size: 0.75rem;\n    margin-left: 0.5rem;\n}\n\n.match-container.selected {\n    border-color: var(--accent-cyan);\n    box-shadow: 0 0 15px rgba(var(--accent-cyan-rgb), 0.3);\n}\n\n.match-actions {\n    padding: 0.75rem;\n    border-top: 1px solid var(--border-cyber);\n    display: flex;\n    justify-content: center;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n    .bracket-container {\n        padding: 0.5rem;\n    }\n\n    .round {\n        min-width: 200px;\n        margin-right: 1rem;\n    }\n\n    .participant-name {\n        font-size: 0.8rem;\n    }\n\n    .standings-table {\n        font-size: 0.9rem;\n    }\n\n    .match-details-panel {\n        width: 95%;\n        padding: 1.5rem;\n    }\n\n    .score-inputs {\n        flex-direction: column;\n        gap: 0.5rem;\n    }\n\n    .admin-resolve .flex {\n        flex-direction: column;\n    }\n}\n\n/* Tournament Winner Display */\n.tournament-winner {\n    background: linear-gradient(135deg, var(--bg-element-dark), var(--bg-element-medium));\n    border: 2px solid var(--accent-cyan);\n    border-radius: 0.5rem;\n    padding: 2rem;\n    margin: 2rem auto;\n    text-align: center;\n    max-width: 600px;\n    box-shadow: 0 0 20px rgba(var(--accent-cyan-rgb), 0.3);\n    animation: winnerGlow 2s infinite alternate;\n}\n\n.tournament-winner h2 {\n    color: var(--accent-cyan);\n    font-family: 'Orbitron', sans-serif;\n    font-size: 1.5rem;\n    margin-bottom: 1rem;\n    text-transform: uppercase;\n}\n\n.tournament-winner .winner-name {\n    color: var(--accent-magenta);\n    font-size: 2rem;\n    font-weight: bold;\n    margin: 1rem 0;\n    text-shadow: 0 0 10px rgba(var(--accent-magenta-rgb), 0.6);\n}\n\n.tournament-winner .winner-trophy {\n    font-size: 3rem;\n    margin: 1rem 0;\n    color: gold;\n    text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);\n}\n\n@keyframes winnerGlow {\n    from {\n        box-shadow: 0 0 20px rgba(var(--accent-cyan-rgb), 0.3);\n    }\n    to {\n        box-shadow: 0 0 30px rgba(var(--accent-cyan-rgb), 0.5);\n    }\n} ",""]);const s=o},825:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var a=void 0!==n.layer;a&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,a&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},845:e=>{e.exports=t},883:t=>{t.exports=e}},r={};function a(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={id:e,exports:{}};return n[e](i,i.exports,a),i.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var n in t)a.o(t,n)&&!a.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),a.nc=void 0;var i=a(883),o=a.n(i),s=a(845),c=a.n(s),l=a(72),u=a.n(l),d=a(825),p=a.n(d),m=a(659),f=a.n(m),h=a(56),b=a.n(h),g=a(540),v=a.n(g),y=a(113),w=a.n(y),E=a(761),I={};I.styleTagTransform=w(),I.setAttributes=b(),I.insert=f().bind(null,"head"),I.domAPI=p(),I.insertStyleElement=v(),u()(E.A,I),E.A&&E.A.locals&&E.A.locals;const x=function(e){const t=[];let n=0;for(let r=0;r<e.length;r++){let a=e.charCodeAt(r);a<128?t[n++]=a:a<2048?(t[n++]=a>>6|192,t[n++]=63&a|128):55296==(64512&a)&&r+1<e.length&&56320==(64512&e.charCodeAt(r+1))?(a=65536+((1023&a)<<10)+(1023&e.charCodeAt(++r)),t[n++]=a>>18|240,t[n++]=a>>12&63|128,t[n++]=a>>6&63|128,t[n++]=63&a|128):(t[n++]=a>>12|224,t[n++]=a>>6&63|128,t[n++]=63&a|128)}return t},k={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();const n=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let t=0;t<e.length;t+=3){const a=e[t],i=t+1<e.length,o=i?e[t+1]:0,s=t+2<e.length,c=s?e[t+2]:0,l=a>>2,u=(3&a)<<4|o>>4;let d=(15&o)<<2|c>>6,p=63&c;s||(p=64,i||(d=64)),r.push(n[l],n[u],n[d],n[p])}return r.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(x(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):function(e){const t=[];let n=0,r=0;for(;n<e.length;){const a=e[n++];if(a<128)t[r++]=String.fromCharCode(a);else if(a>191&&a<224){const i=e[n++];t[r++]=String.fromCharCode((31&a)<<6|63&i)}else if(a>239&&a<365){const i=((7&a)<<18|(63&e[n++])<<12|(63&e[n++])<<6|63&e[n++])-65536;t[r++]=String.fromCharCode(55296+(i>>10)),t[r++]=String.fromCharCode(56320+(1023&i))}else{const i=e[n++],o=e[n++];t[r++]=String.fromCharCode((15&a)<<12|(63&i)<<6|63&o)}}return t.join("")}(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();const n=t?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let t=0;t<e.length;){const a=n[e.charAt(t++)],i=t<e.length?n[e.charAt(t)]:0;++t;const o=t<e.length?n[e.charAt(t)]:64;++t;const s=t<e.length?n[e.charAt(t)]:64;if(++t,null==a||null==i||null==o||null==s)throw new S;const c=a<<2|i>>4;if(r.push(c),64!==o){const e=i<<4&240|o>>2;if(r.push(e),64!==s){const e=o<<6&192|s;r.push(e)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};class S extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}const N=function(e){return function(e){const t=x(e);return k.encodeByteArray(t,!0)}(e).replace(/\./g,"")};class A extends Error{constructor(e,t,n){super(t),this.code=e,this.customData=n,this.name="FirebaseError",Object.setPrototypeOf(this,A.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,O.prototype.create)}}class O{constructor(e,t,n){this.service=e,this.serviceName=t,this.errors=n}create(e,...t){const n=t[0]||{},r=`${this.service}/${e}`,a=this.errors[e],i=a?function(e,t){return e.replace(D,((e,n)=>{const r=t[n];return null!=r?String(r):`<${n}?>`}))}(a,n):"Error",o=`${this.serviceName}: ${i} (${r}).`;return new A(r,o,n)}}const D=/\{\$([^}]+)}/g;class _{constructor(e,t,n){this.name=e,this.instanceFactory=t,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}}const C=[];var B;!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(B||(B={}));const T={debug:B.DEBUG,verbose:B.VERBOSE,info:B.INFO,warn:B.WARN,error:B.ERROR,silent:B.SILENT},j=B.INFO,P={[B.DEBUG]:"log",[B.VERBOSE]:"log",[B.INFO]:"info",[B.WARN]:"warn",[B.ERROR]:"error"},M=(e,t,...n)=>{if(t<e.logLevel)return;const r=(new Date).toISOString(),a=P[t];if(!a)throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`);console[a](`[${r}]  ${e.name}:`,...n)};let R,L;const U=new WeakMap,V=new WeakMap,F=new WeakMap,$=new WeakMap,H=new WeakMap;let G={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return V.get(e);if("objectStoreNames"===t)return e.objectStoreNames||F.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return z(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function W(e){return"function"==typeof e?(t=e)!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(L||(L=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(q(this),e),z(U.get(this))}:function(...e){return z(t.apply(q(this),e))}:function(e,...n){const r=t.call(q(this),e,...n);return F.set(r,e.sort?e.sort():[e]),z(r)}:(e instanceof IDBTransaction&&function(e){if(V.has(e))return;const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("complete",a),e.removeEventListener("error",i),e.removeEventListener("abort",i)},a=()=>{t(),r()},i=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",a),e.addEventListener("error",i),e.addEventListener("abort",i)}));V.set(e,t)}(e),n=e,(R||(R=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])).some((e=>n instanceof e))?new Proxy(e,G):e);var t,n}function z(e){if(e instanceof IDBRequest)return function(e){const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("success",a),e.removeEventListener("error",i)},a=()=>{t(z(e.result)),r()},i=()=>{n(e.error),r()};e.addEventListener("success",a),e.addEventListener("error",i)}));return t.then((t=>{t instanceof IDBCursor&&U.set(t,e)})).catch((()=>{})),H.set(t,e),t}(e);if($.has(e))return $.get(e);const t=W(e);return t!==e&&($.set(e,t),H.set(t,e)),t}const q=e=>H.get(e),J=["get","getKey","getAll","getAllKeys","count"],Y=["put","add","delete","clear"],K=new Map;function X(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(K.get(t))return K.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,a=Y.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!a&&!J.includes(n))return;const i=async function(e,...t){const i=this.transaction(e,a?"readwrite":"readonly");let o=i.store;return r&&(o=o.index(t.shift())),(await Promise.all([o[n](...t),a&&i.done]))[0]};return K.set(t,i),i}var Z;Z=G,G={...Z,get:(e,t,n)=>X(e,t)||Z.get(e,t,n),has:(e,t)=>!!X(e,t)||Z.has(e,t)};class Q{constructor(e){this.container=e}getPlatformInfoString(){return this.container.getProviders().map((e=>{if(function(e){const t=e.getComponent();return"VERSION"===(null==t?void 0:t.type)}(e)){const t=e.getImmediate();return`${t.library}/${t.version}`}return null})).filter((e=>e)).join(" ")}}const ee="@firebase/app",te="0.9.13",ne=new class{constructor(e){this.name=e,this._logLevel=j,this._logHandler=M,this._userLogHandler=null,C.push(this)}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in B))throw new TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?T[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,B.DEBUG,...e),this._logHandler(this,B.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,B.VERBOSE,...e),this._logHandler(this,B.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,B.INFO,...e),this._logHandler(this,B.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,B.WARN,...e),this._logHandler(this,B.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,B.ERROR,...e),this._logHandler(this,B.ERROR,...e)}}("@firebase/app"),re="@firebase/app-compat",ae="@firebase/analytics-compat",ie="@firebase/analytics",oe="@firebase/app-check-compat",se="@firebase/app-check",ce="@firebase/auth",le="@firebase/auth-compat",ue="@firebase/database",de="@firebase/database-compat",pe="@firebase/functions",me="@firebase/functions-compat",fe="@firebase/installations",he="@firebase/installations-compat",be="@firebase/messaging",ge="@firebase/messaging-compat",ve="@firebase/performance",ye="@firebase/performance-compat",we="@firebase/remote-config",Ee="@firebase/remote-config-compat",Ie="@firebase/storage",xe="@firebase/storage-compat",ke="@firebase/firestore",Se="@firebase/firestore-compat",Ne="firebase",Ae={[ee]:"fire-core",[re]:"fire-core-compat",[ie]:"fire-analytics",[ae]:"fire-analytics-compat",[se]:"fire-app-check",[oe]:"fire-app-check-compat",[ce]:"fire-auth",[le]:"fire-auth-compat",[ue]:"fire-rtdb",[de]:"fire-rtdb-compat",[pe]:"fire-fn",[me]:"fire-fn-compat",[fe]:"fire-iid",[he]:"fire-iid-compat",[be]:"fire-fcm",[ge]:"fire-fcm-compat",[ve]:"fire-perf",[ye]:"fire-perf-compat",[we]:"fire-rc",[Ee]:"fire-rc-compat",[Ie]:"fire-gcs",[xe]:"fire-gcs-compat",[ke]:"fire-fst",[Se]:"fire-fst-compat","fire-js":"fire-js",[Ne]:"fire-js-all"},Oe=new Map,De=new Map;function _e(e,t){try{e.container.addComponent(t)}catch(n){ne.debug(`Component ${t.name} failed to register with FirebaseApp ${e.name}`,n)}}function Ce(e){const t=e.name;if(De.has(t))return ne.debug(`There were multiple attempts to register component ${t}.`),!1;De.set(t,e);for(const t of Oe.values())_e(t,e);return!0}const Be=new O("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}."});function Te(e,t,n){var r;let a=null!==(r=Ae[e])&&void 0!==r?r:e;n&&(a+=`-${n}`);const i=a.match(/\s|\//),o=t.match(/\s|\//);if(i||o){const e=[`Unable to register library "${a}" with version "${t}":`];return i&&e.push(`library name "${a}" contains illegal characters (whitespace or "/")`),i&&o&&e.push("and"),o&&e.push(`version name "${t}" contains illegal characters (whitespace or "/")`),void ne.warn(e.join(" "))}Ce(new _(`${a}-version`,(()=>({library:a,version:t})),"VERSION"))}const je="firebase-heartbeat-store";let Pe=null;function Me(){return Pe||(Pe=function(e,t,{blocked:n,upgrade:r,blocking:a,terminated:i}={}){const o=indexedDB.open(e,t),s=z(o);return r&&o.addEventListener("upgradeneeded",(e=>{r(z(o.result),e.oldVersion,e.newVersion,z(o.transaction),e)})),n&&o.addEventListener("blocked",(e=>n(e.oldVersion,e.newVersion,e))),s.then((e=>{i&&e.addEventListener("close",(()=>i())),a&&e.addEventListener("versionchange",(e=>a(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),s}("firebase-heartbeat-database",1,{upgrade:(e,t)=>{0===t&&e.createObjectStore(je)}}).catch((e=>{throw Be.create("idb-open",{originalErrorMessage:e.message})}))),Pe}async function Re(e,t){try{const n=(await Me()).transaction(je,"readwrite"),r=n.objectStore(je);await r.put(t,Le(e)),await n.done}catch(e){if(e instanceof A)ne.warn(e.message);else{const t=Be.create("idb-set",{originalErrorMessage:null==e?void 0:e.message});ne.warn(t.message)}}}function Le(e){return`${e.name}!${e.options.appId}`}class Ue{constructor(e){this.container=e,this._heartbeatsCache=null;const t=this.container.getProvider("app").getImmediate();this._storage=new Fe(t),this._heartbeatsCachePromise=this._storage.read().then((e=>(this._heartbeatsCache=e,e)))}async triggerHeartbeat(){const e=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),t=Ve();if(null===this._heartbeatsCache&&(this._heartbeatsCache=await this._heartbeatsCachePromise),this._heartbeatsCache.lastSentHeartbeatDate!==t&&!this._heartbeatsCache.heartbeats.some((e=>e.date===t)))return this._heartbeatsCache.heartbeats.push({date:t,agent:e}),this._heartbeatsCache.heartbeats=this._heartbeatsCache.heartbeats.filter((e=>{const t=new Date(e.date).valueOf();return Date.now()-t<=2592e6})),this._storage.overwrite(this._heartbeatsCache)}async getHeartbeatsHeader(){if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,null===this._heartbeatsCache||0===this._heartbeatsCache.heartbeats.length)return"";const e=Ve(),{heartbeatsToSend:t,unsentEntries:n}=function(e,t=1024){const n=[];let r=e.slice();for(const a of e){const e=n.find((e=>e.agent===a.agent));if(e){if(e.dates.push(a.date),$e(n)>t){e.dates.pop();break}}else if(n.push({agent:a.agent,dates:[a.date]}),$e(n)>t){n.pop();break}r=r.slice(1)}return{heartbeatsToSend:n,unsentEntries:r}}(this._heartbeatsCache.heartbeats),r=N(JSON.stringify({version:2,heartbeats:t}));return this._heartbeatsCache.lastSentHeartbeatDate=e,n.length>0?(this._heartbeatsCache.heartbeats=n,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),r}}function Ve(){return(new Date).toISOString().substring(0,10)}class Fe{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!function(){try{return"object"==typeof indexedDB}catch(e){return!1}}()&&new Promise(((e,t)=>{try{let n=!0;const r="validate-browser-context-for-indexeddb-analytics-module",a=self.indexedDB.open(r);a.onsuccess=()=>{a.result.close(),n||self.indexedDB.deleteDatabase(r),e(!0)},a.onupgradeneeded=()=>{n=!1},a.onerror=()=>{var e;t((null===(e=a.error)||void 0===e?void 0:e.message)||"")}}catch(e){t(e)}})).then((()=>!0)).catch((()=>!1))}async read(){return await this._canUseIndexedDBPromise&&await async function(e){try{const t=await Me();return await t.transaction(je).objectStore(je).get(Le(e))}catch(e){if(e instanceof A)ne.warn(e.message);else{const t=Be.create("idb-get",{originalErrorMessage:null==e?void 0:e.message});ne.warn(t.message)}}}(this.app)||{heartbeats:[]}}async overwrite(e){var t;if(await this._canUseIndexedDBPromise){const n=await this.read();return Re(this.app,{lastSentHeartbeatDate:null!==(t=e.lastSentHeartbeatDate)&&void 0!==t?t:n.lastSentHeartbeatDate,heartbeats:e.heartbeats})}}async add(e){var t;if(await this._canUseIndexedDBPromise){const n=await this.read();return Re(this.app,{lastSentHeartbeatDate:null!==(t=e.lastSentHeartbeatDate)&&void 0!==t?t:n.lastSentHeartbeatDate,heartbeats:[...n.heartbeats,...e.heartbeats]})}}}function $e(e){return N(JSON.stringify({version:2,heartbeats:e})).length}function He(e,t){const n={};for(const r in e)e.hasOwnProperty(r)&&(n[r]=t(e[r]));return n}function Ge(e){if(null==e)return null;if(e instanceof Number&&(e=e.valueOf()),"number"==typeof e&&isFinite(e))return e;if(!0===e||!1===e)return e;if("[object String]"===Object.prototype.toString.call(e))return e;if(e instanceof Date)return e.toISOString();if(Array.isArray(e))return e.map((e=>Ge(e)));if("function"==typeof e||"object"==typeof e)return He(e,(e=>Ge(e)));throw new Error("Data cannot be encoded in JSON: "+e)}function We(e){if(null==e)return e;if(e["@type"])switch(e["@type"]){case"type.googleapis.com/google.protobuf.Int64Value":case"type.googleapis.com/google.protobuf.UInt64Value":{const t=Number(e.value);if(isNaN(t))throw new Error("Data cannot be decoded from JSON: "+e);return t}default:throw new Error("Data cannot be decoded from JSON: "+e)}return Array.isArray(e)?e.map((e=>We(e))):"function"==typeof e||"object"==typeof e?He(e,(e=>We(e))):e}Ce(new _("platform-logger",(e=>new Q(e)),"PRIVATE")),Ce(new _("heartbeat",(e=>new Ue(e)),"PRIVATE")),Te(ee,te,""),Te(ee,te,"esm2017"),Te("fire-js","");const ze="functions",qe={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"};class Je extends A{constructor(e,t,n){super(`${ze}/${e}`,t||""),this.details=n}}class Ye{constructor(e,t,n){this.auth=null,this.messaging=null,this.appCheck=null,this.auth=e.getImmediate({optional:!0}),this.messaging=t.getImmediate({optional:!0}),this.auth||e.get().then((e=>this.auth=e),(()=>{})),this.messaging||t.get().then((e=>this.messaging=e),(()=>{})),this.appCheck||n.get().then((e=>this.appCheck=e),(()=>{}))}async getAuthToken(){if(this.auth)try{const e=await this.auth.getToken();return null==e?void 0:e.accessToken}catch(e){return}}async getMessagingToken(){if(this.messaging&&"Notification"in self&&"granted"===Notification.permission)try{return await this.messaging.getToken()}catch(e){return}}async getAppCheckToken(e){if(this.appCheck){const t=e?await this.appCheck.getLimitedUseToken():await this.appCheck.getToken();return t.error?null:t.token}return null}async getContext(e){return{authToken:await this.getAuthToken(),messagingToken:await this.getMessagingToken(),appCheckToken:await this.getAppCheckToken(e)}}}const Ke="us-central1";class Xe{constructor(e,t,n,r,a=Ke,i){this.app=e,this.fetchImpl=i,this.emulatorOrigin=null,this.contextProvider=new Ye(t,n,r),this.cancelAllRequests=new Promise((e=>{this.deleteService=()=>Promise.resolve(e())}));try{const e=new URL(a);this.customDomain=e.origin,this.region=Ke}catch(e){this.customDomain=null,this.region=a}}_delete(){return this.deleteService()}_url(e){const t=this.app.options.projectId;return null!==this.emulatorOrigin?`${this.emulatorOrigin}/${t}/${this.region}/${e}`:null!==this.customDomain?`${this.customDomain}/${e}`:`https://${this.region}-${t}.cloudfunctions.net/${e}`}}function Ze(e,t,n){return r=>function(e,t,n,r){const a=e._url(t);return async function(e,t,n,r){const a={data:n=Ge(n)},i={},o=await e.contextProvider.getContext(r.limitedUseAppCheckTokens);o.authToken&&(i.Authorization="Bearer "+o.authToken),o.messagingToken&&(i["Firebase-Instance-ID-Token"]=o.messagingToken),null!==o.appCheckToken&&(i["X-Firebase-AppCheck"]=o.appCheckToken);const s=function(e){let t=null;return{promise:new Promise(((n,r)=>{t=setTimeout((()=>{r(new Je("deadline-exceeded","deadline-exceeded"))}),e)})),cancel:()=>{t&&clearTimeout(t)}}}(r.timeout||7e4),c=await Promise.race([Qe(t,a,i,e.fetchImpl),s.promise,e.cancelAllRequests]);if(s.cancel(),!c)throw new Je("cancelled","Firebase Functions instance was deleted.");const l=function(e,t){let n,r=function(e){if(e>=200&&e<300)return"ok";switch(e){case 0:case 500:return"internal";case 400:return"invalid-argument";case 401:return"unauthenticated";case 403:return"permission-denied";case 404:return"not-found";case 409:return"aborted";case 429:return"resource-exhausted";case 499:return"cancelled";case 501:return"unimplemented";case 503:return"unavailable";case 504:return"deadline-exceeded"}return"unknown"}(e),a=r;try{const e=t&&t.error;if(e){const t=e.status;if("string"==typeof t){if(!qe[t])return new Je("internal","internal");r=qe[t],a=t}const i=e.message;"string"==typeof i&&(a=i),n=e.details,void 0!==n&&(n=We(n))}}catch(e){}return"ok"===r?null:new Je(r,a,n)}(c.status,c.json);if(l)throw l;if(!c.json)throw new Je("internal","Response is not valid JSON object.");let u=c.json.data;if(void 0===u&&(u=c.json.result),void 0===u)throw new Je("internal","Response is missing data field.");return{data:We(u)}}(e,a,n,r)}(e,t,r,n||{})}async function Qe(e,t,n,r){let a;n["Content-Type"]="application/json";try{a=await r(e,{method:"POST",body:JSON.stringify(t),headers:n})}catch(e){return{status:0,json:null}}let i=null;try{i=await a.json()}catch(e){}return{status:a.status,json:i}}const et="@firebase/functions",tt="0.10.0";function nt(e,t,n){return Ze((r=e)&&r._delegate?r._delegate:r,t,n);var r}function rt(e){return rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rt(e)}function at(){at=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",o=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(e,t,n,r){return Object.defineProperty(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function l(t,n,r,a){var i=n&&n.prototype instanceof p?n:p,o=Object.create(i.prototype);return c(o,"_invoke",function(t,n,r){var a=1;return function(i,o){if(3===a)throw Error("Generator is already running");if(4===a){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===d)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===a)throw a=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=3;var l=u(t,n,r);if("normal"===l.type){if(a=r.done?4:2,l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=4,r.method="throw",r.arg=l.arg)}}}(t,r,new k(a||[])),!0),o}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var d={};function p(){}function m(){}function f(){}var h={};c(h,i,(function(){return this}));var b=Object.getPrototypeOf,g=b&&b(b(S([])));g&&g!==n&&r.call(g,i)&&(h=g);var v=f.prototype=p.prototype=Object.create(h);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function n(a,i,o,s){var c=u(e[a],e,i);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==rt(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,o,s)}),(function(e){n("throw",e,o,s)})):t.resolve(d).then((function(e){l.value=e,o(l)}),(function(e){return n("throw",e,o,s)}))}s(c.arg)}var a;c(this,"_invoke",(function(e,r){function i(){return new t((function(t,a){n(e,r,t,a)}))}return a=a?a.then(i,i):i()}),!0)}function E(t,n){var r=n.method,a=t.i[r];if(a===e)return n.delegate=null,"throw"===r&&t.i.return&&(n.method="return",n.arg=e,E(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=u(a,t.i,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,d;var o=i.arg;return o?o.done?(n[t.r]=o.value,n.next=t.n,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,d):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}function I(e){this.tryEntries.push(e)}function x(t){var n=t[4]||{};n.type="normal",n.arg=e,t[4]=n}function k(e){this.tryEntries=[[-1]],e.forEach(I,this),this.reset(!0)}function S(t){if(null!=t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(rt(t)+" is not iterable")}return m.prototype=f,c(v,"constructor",f),c(f,"constructor",m),m.displayName=c(f,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,s,"GeneratorFunction")),e.prototype=Object.create(v),e},t.awrap=function(e){return{__await:e}},y(w.prototype),c(w.prototype,o,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new w(l(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},y(v),c(v,s,"Generator"),c(v,i,(function(){return this})),c(v,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.unshift(r);return function e(){for(;n.length;)if((r=n.pop())in t)return e.value=r,e.done=!1,e;return e.done=!0,e}},t.values=S,k.prototype={constructor:k,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(x),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0][4];if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(e){o.type="throw",o.arg=t,n.next=e}for(var a=n.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],o=i[4],s=this.prev,c=i[1],l=i[2];if(-1===i[0])return r("end"),!1;if(!c&&!l)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=s){if(s<c)return this.method="next",this.arg=e,r(c),!0;if(s<l)return r(l),!1}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var a=r;break}}a&&("break"===e||"continue"===e)&&a[0]<=t&&t<=a[2]&&(a=null);var i=a?a[4]:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a[2],d):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[2]===e)return this.complete(n[4],n[3]),x(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[0]===e){var r=n[4];if("throw"===r.type){var a=r.arg;x(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={i:S(t),r:n,n:r},"next"===this.method&&(this.arg=e),d}},t}function it(e,t,n,r,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,a)}function ot(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){it(i,r,a,o,s,"next",e)}function s(e){it(i,r,a,o,s,"throw",e)}o(void 0)}))}}var st;st=fetch.bind(self),Ce(new _(ze,((e,{instanceIdentifier:t})=>{const n=e.getProvider("app").getImmediate(),r=e.getProvider("auth-internal"),a=e.getProvider("messaging-internal"),i=e.getProvider("app-check-internal");return new Xe(n,r,a,i,t,st)}),"PUBLIC").setMultipleInstances(!0)),Te(et,tt,void 0),Te(et,tt,"esm2017");var ct=function(){if(!window.app)throw new Error("Firebase is not initialized. Please ensure Firebase is properly set up.");if(!window.currentUser)throw new Error("User is not authenticated. Please connect your wallet first.");return window.functions},lt=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!e)return{isValid:!1,error:"Match not found"};if(!e.participant1Id||!e.participant2Id)return{isValid:!1,error:"Both participants must be assigned before submitting results"};if(e.winnerId||"completed"===e.status)return{isValid:!1,error:"Match result has already been submitted"};if(e.participant1Id.startsWith("TBD_")||e.participant2Id.startsWith("TBD_"))return{isValid:!1,error:"Cannot submit results while participants are still being determined (TBD)"};if(!["PENDING","ready","pending"].includes(e.status))return{isValid:!1,error:"Cannot submit results for match with status: ".concat(e.status)};if(t.length>0){var n=t.some((function(t){return t.id===e.participant1Id})),r=t.some((function(t){return t.id===e.participant2Id}));if(!n||!r)return{isValid:!1,error:"One or both participants are not valid tournament participants"}}return{isValid:!0}},ut=function(e,t,n,r,a){if(!e||"number"!=typeof e.participant1||"number"!=typeof e.participant2)return{isValid:!1,error:"Valid scores for both participants are required"};if(!(t&&n&&r&&a))return{isValid:!1,error:"Missing required match information for validation"};var i=n.toLowerCase(),o=["bo1","bo3","bo5","bo7"];if(!o.includes(i))return{isValid:!1,error:"Invalid match format: ".concat(n,". Must be one of: ").concat(o.join(", "))};var s=e.participant1,c=e.participant2,l=s+c,u=parseInt(i.substring(2)),d=Math.ceil(u/2);if(s===c)return{isValid:!1,error:"Tied scores are not allowed in ".concat(i.toUpperCase()," matches. One participant must have a clear majority.")};if(t!==(s>c?r:a))return{isValid:!1,error:"Declared winner does not match the scores provided"};switch(i){case"bo1":if(1!==l)return{isValid:!1,error:"Best of 1 matches must have exactly 1 game played"};if(1!==Math.max(s,c)||0!==Math.min(s,c))return{isValid:!1,error:"Best of 1 matches must have a score of 1-0"};break;case"bo3":if(l<2||l>3)return{isValid:!1,error:"Best of 3 matches must have 2-3 games played"};if(Math.max(s,c)<2)return{isValid:!1,error:"Best of 3 matches require the winner to win at least 2 games"};break;case"bo5":if(l<3||l>5)return{isValid:!1,error:"Best of 5 matches must have 3-5 games played"};if(Math.max(s,c)<3)return{isValid:!1,error:"Best of 5 matches require the winner to win at least 3 games"};break;case"bo7":if(l<4||l>7)return{isValid:!1,error:"Best of 7 matches must have 4-7 games played"};if(Math.max(s,c)<4)return{isValid:!1,error:"Best of 7 matches require the winner to win at least 4 games"}}return(t===r?s:c)<d?{isValid:!1,error:"Winner must have won at least ".concat(d," games in a ").concat(i.toUpperCase()," match")}:{isValid:!0}},dt=function(){var e=ot(at().mark((function e(t,n,r){var a,i,o;return at().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,window.app){e.next=3;break}throw new Error("Firebase is not initialized. Please ensure Firebase is properly set up.");case 3:if(window.currentUserAddress){e.next=5;break}throw new Error("Wallet address not found. Please connect your wallet first.");case 5:return a=window.functions,i=nt(a,"updateMatchResult"),e.next=9,i({tournamentId:t,matchId:n,result:{winnerId:r.winnerId,score:{participant1:r.score.participant1,participant2:r.score.participant2}},userWalletAddress:window.currentUserAddress});case 9:return o=e.sent,e.abrupt("return",o.data);case 13:throw e.prev=13,e.t0=e.catch(0),console.error("Error updating match result:",e.t0),e.t0;case 17:case"end":return e.stop()}}),e,null,[[0,13]])})));return function(t,n,r){return e.apply(this,arguments)}}(),pt=function(){var e=ot(at().mark((function e(t,n){var r,a,i,o,s,c;return at().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=ct(),a=r.collection("tournaments").doc(t),e.next=4,a.get();case 4:if(i=e.sent,(o=i.data())&&o.bracketData){e.next=8;break}throw new Error("Tournament or bracket data not found");case 8:if(s=o.bracketData.matchesById,!(c=Object.values(s).find((function(e){return"pending"===e.status&&(e.participant1Id===n.id||e.participant2Id===n.id)})))){e.next=15;break}return c.participant1Id===n.id?c.participant1Id=n.winnerId:c.participant2Id=n.winnerId,c.participant1Id&&c.participant2Id&&(c.status="ready"),e.next=15,a.update({bracketData:o.bracketData});case 15:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),mt=function(){var e=ot(at().mark((function e(t,n){var r,a,i,o,s,c,l,u;return at().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=ct(),a=r.collection("tournaments").doc(t),e.next=4,a.get();case 4:if(i=e.sent,(o=i.data())&&o.bracketData){e.next=8;break}throw new Error("Tournament or bracket data not found");case 8:return s=o.bracketData.matchesById,n.isUpperBracket?((c=Object.values(s).find((function(e){return e.isUpperBracket&&"pending"===e.status&&(e.participant1Id===n.id||e.participant2Id===n.id)})))&&(c.participant1Id===n.id?c.participant1Id=n.winnerId:c.participant2Id=n.winnerId),(l=Object.values(s).find((function(e){return!e.isUpperBracket&&"pending"===e.status&&(e.participant1Id===n.id||e.participant2Id===n.id)})))&&(l.participant1Id===n.id?l.participant1Id=n.loserId:l.participant2Id=n.loserId)):(u=Object.values(s).find((function(e){return!e.isUpperBracket&&"pending"===e.status&&(e.participant1Id===n.id||e.participant2Id===n.id)})))&&(u.participant1Id===n.id?u.participant1Id=n.winnerId:u.participant2Id=n.winnerId),Object.values(s).forEach((function(e){"pending"===e.status&&e.participant1Id&&e.participant2Id&&(e.status="ready")})),e.next=13,a.update({bracketData:o.bracketData});case 13:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),ft=function(){var e=ot(at().mark((function e(t,n){var r,a,i,o,s;return at().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=ct(),a=r.collection("tournaments").doc(t),e.next=4,a.get();case 4:if(i=e.sent,(o=i.data())&&o.bracketData){e.next=8;break}throw new Error("Tournament or bracket data not found");case 8:if(s=o.bracketData.matchesById,!Object.values(s).every((function(e){return"completed"===e.status}))){e.next=13;break}return e.next=13,a.update({status:"completed",completedAt:(new Date).toISOString()});case 13:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),ht=function(){var e=ot(at().mark((function e(t,n,r){var a,i,o,s,c,l,u,d;return at().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,i=ct(),o=i.collection("tournaments").doc(t),e.next=5,o.get();case 5:if(s=e.sent,(c=s.data())&&c.bracketData){e.next=9;break}throw new Error("Tournament or bracket data not found");case 9:if(l=c.bracketData.matchesById[n]){e.next=12;break}throw new Error("Match not found in bracket data");case 12:if(u=l.matchFormat||(null===(a=c.bracketData)||void 0===a||null===(a=a.metadata)||void 0===a?void 0:a.matchFormat)||c.matchFormat||"bo1",(d=ut(r.score,r.winnerId,u,r.participant1Id,r.participant2Id)).isValid){e.next=16;break}throw new Error(d.error);case 16:return l.status="completed",l.winnerId=r.winnerId,l.loserId=r.winnerId===r.participant1Id?r.participant2Id:r.participant1Id,l.score=r.score,l.resolvedBy=r.resolvedBy,l.resolvedAt=(new Date).toISOString(),e.next=24,o.update({bracketData:c.bracketData});case 24:e.t0=r.tournamentFormat,e.next="single-elimination"===e.t0?27:"double-elimination"===e.t0?30:"round-robin"===e.t0?33:36;break;case 27:return e.next=29,pt(t,l);case 29:case 32:case 35:return e.abrupt("break",36);case 30:return e.next=32,mt(t,l);case 33:return e.next=35,ft(t,l);case 36:return e.abrupt("return",{success:!0});case 39:throw e.prev=39,e.t1=e.catch(0),console.error("Error resolving disputed match:",e.t1),e.t1;case 43:case"end":return e.stop()}}),e,null,[[0,39]])})));return function(t,n,r){return e.apply(this,arguments)}}();function bt(e){return bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bt(e)}function gt(){gt=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",o=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function c(e,t,n,r){return Object.defineProperty(e,t,{value:n,enumerable:!r,configurable:!r,writable:!r})}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function l(t,n,r,a){var i=n&&n.prototype instanceof p?n:p,o=Object.create(i.prototype);return c(o,"_invoke",function(t,n,r){var a=1;return function(i,o){if(3===a)throw Error("Generator is already running");if(4===a){if("throw"===i)throw o;return{value:e,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var c=E(s,r);if(c){if(c===d)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(1===a)throw a=4,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=3;var l=u(t,n,r);if("normal"===l.type){if(a=r.done?4:2,l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(a=4,r.method="throw",r.arg=l.arg)}}}(t,r,new k(a||[])),!0),o}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var d={};function p(){}function m(){}function f(){}var h={};c(h,i,(function(){return this}));var b=Object.getPrototypeOf,g=b&&b(b(S([])));g&&g!==n&&r.call(g,i)&&(h=g);var v=f.prototype=p.prototype=Object.create(h);function y(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function n(a,i,o,s){var c=u(e[a],e,i);if("throw"!==c.type){var l=c.arg,d=l.value;return d&&"object"==bt(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){n("next",e,o,s)}),(function(e){n("throw",e,o,s)})):t.resolve(d).then((function(e){l.value=e,o(l)}),(function(e){return n("throw",e,o,s)}))}s(c.arg)}var a;c(this,"_invoke",(function(e,r){function i(){return new t((function(t,a){n(e,r,t,a)}))}return a=a?a.then(i,i):i()}),!0)}function E(t,n){var r=n.method,a=t.i[r];if(a===e)return n.delegate=null,"throw"===r&&t.i.return&&(n.method="return",n.arg=e,E(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=u(a,t.i,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,d;var o=i.arg;return o?o.done?(n[t.r]=o.value,n.next=t.n,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,d):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,d)}function I(e){this.tryEntries.push(e)}function x(t){var n=t[4]||{};n.type="normal",n.arg=e,t[4]=n}function k(e){this.tryEntries=[[-1]],e.forEach(I,this),this.reset(!0)}function S(t){if(null!=t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,o=function n(){for(;++a<t.length;)if(r.call(t,a))return n.value=t[a],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(bt(t)+" is not iterable")}return m.prototype=f,c(v,"constructor",f),c(f,"constructor",m),m.displayName=c(f,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===m||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,c(e,s,"GeneratorFunction")),e.prototype=Object.create(v),e},t.awrap=function(e){return{__await:e}},y(w.prototype),c(w.prototype,o,(function(){return this})),t.AsyncIterator=w,t.async=function(e,n,r,a,i){void 0===i&&(i=Promise);var o=new w(l(e,n,r,a),i);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},y(v),c(v,s,"Generator"),c(v,i,(function(){return this})),c(v,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.unshift(r);return function e(){for(;n.length;)if((r=n.pop())in t)return e.value=r,e.done=!1,e;return e.done=!0,e}},t.values=S,k.prototype={constructor:k,reset:function(t){if(this.prev=this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(x),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0][4];if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(e){o.type="throw",o.arg=t,n.next=e}for(var a=n.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],o=i[4],s=this.prev,c=i[1],l=i[2];if(-1===i[0])return r("end"),!1;if(!c&&!l)throw Error("try statement without catch or finally");if(null!=i[0]&&i[0]<=s){if(s<c)return this.method="next",this.arg=e,r(c),!0;if(s<l)return r(l),!1}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r[0]>-1&&r[0]<=this.prev&&this.prev<r[2]){var a=r;break}}a&&("break"===e||"continue"===e)&&a[0]<=t&&t<=a[2]&&(a=null);var i=a?a[4]:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a[2],d):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[2]===e)return this.complete(n[4],n[3]),x(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n[0]===e){var r=n[4];if("throw"===r.type){var a=r.arg;x(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={i:S(t),r:n,n:r},"next"===this.method&&(this.arg=e),d}},t}function vt(e,t,n,r,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,a)}function yt(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(e){vt(i,r,a,o,s,"next",e)}function s(e){vt(i,r,a,o,s,"throw",e)}o(void 0)}))}}function wt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Et(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wt(Object(n),!0).forEach((function(t){It(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function It(e,t,n){return(t=function(e){var t=function(e){if("object"!=bt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=bt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==bt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,a=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return kt(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?kt(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function kt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}const St=function(e){var t=e.match,n=e.tournamentId,r=e.currentUserAddress,a=e.isAdmin,s=e.bracketData,c=xt((0,i.useState)(null),2),l=c[0],u=c[1],d=xt((0,i.useState)({participant1:"",participant2:""}),2),p=d[0],m=d[1],f=xt((0,i.useState)(!1),2),h=f[0],b=f[1],g=xt((0,i.useState)(null),2),v=g[0],y=g[1],w=xt((0,i.useState)(!1),2),E=w[0],I=w[1];(0,i.useEffect)((function(){!function(){var e,n,r,a=null===(e=window.currentUserAddress)||void 0===e?void 0:e.toLowerCase();if(a){var i=a===(null===(n=t.participant1Id)||void 0===n?void 0:n.toLowerCase())||a===(null===(r=t.participant2Id)||void 0===r?void 0:r.toLowerCase());if(!i&&s&&s.participants){var o=s.participants.find((function(e){return e.id===t.participant1Id})),c=s.participants.find((function(e){return e.id===t.participant2Id}));o&&o.members&&o.members.includes(a)&&(i=!0),c&&c.members&&c.members.includes(a)&&(i=!0)}I(i)}else I(!1)}()}),[t.participant1Id,t.participant2Id,s]);var x=function(e,t){m((function(n){return Et(Et({},n),{},It({},e,t))}))},k=function(){var e=yt(gt().mark((function e(){var r,i,o,c,u;return gt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,b(!0),y(null),(i=lt(t,(null==s?void 0:s.participants)||[])).isValid){e.next=6;break}throw new Error(i.error);case 6:if(E||a){e.next=8;break}throw new Error("You must be a participant in this match to submit results");case 8:if(l){e.next=10;break}throw new Error("Please select a winner");case 10:if(p.participant1&&p.participant2){e.next=12;break}throw new Error("Please enter scores for both participants");case 12:if(o={winnerId:l,score:{participant1:parseInt(p.participant1),participant2:parseInt(p.participant2)}},c=t.matchFormat||(null==s||null===(r=s.metadata)||void 0===r?void 0:r.matchFormat)||"bo1",(u=ut(o.score,l,c,t.participant1Id,t.participant2Id)).isValid){e.next=17;break}throw new Error(u.error);case 17:return e.next=19,dt(n,t.id,o);case 19:e.next=25;break;case 21:e.prev=21,e.t0=e.catch(0),y(e.t0.message),console.error("Error updating match result:",e.t0);case 25:return e.prev=25,b(!1),e.finish(25);case 28:case"end":return e.stop()}}),e,null,[[0,21,25,28]])})));return function(){return e.apply(this,arguments)}}(),S=function(){var e=yt(gt().mark((function e(){var i,o,c,u,d;return gt().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,b(!0),y(null),(o=lt(t,(null==s?void 0:s.participants)||[])).isValid){e.next=6;break}throw new Error(o.error);case 6:if(a){e.next=8;break}throw new Error("Only administrators can resolve disputes");case 8:if(l){e.next=10;break}throw new Error("Please select a winner");case 10:if(p.participant1&&p.participant2){e.next=12;break}throw new Error("Please enter scores for both participants");case 12:if(c={winnerId:l,participant1Id:t.participant1Id,participant2Id:t.participant2Id,score:{participant1:parseInt(p.participant1),participant2:parseInt(p.participant2)},resolvedBy:window.currentUserAddress||r,tournamentFormat:t.tournamentFormat},u=t.matchFormat||(null==s||null===(i=s.metadata)||void 0===i?void 0:i.matchFormat)||"bo1",(d=ut(c.score,l,u,t.participant1Id,t.participant2Id)).isValid){e.next=17;break}throw new Error(d.error);case 17:return e.next=19,ht(n,t.id,c);case 19:e.next=25;break;case 21:e.prev=21,e.t0=e.catch(0),y(e.t0.message),console.error("Error resolving dispute:",e.t0);case 25:return e.prev=25,b(!1),e.finish(25);case 28:case"end":return e.stop()}}),e,null,[[0,21,25,28]])})));return function(){return e.apply(this,arguments)}}(),N=lt(t,(null==s?void 0:s.participants)||[]);return N.isValid?E||a?o().createElement("div",{className:"match-result-submission"},o().createElement("h3",null,"Submit Match Result"),o().createElement("div",{className:"score-inputs"},o().createElement("div",{className:"score-input"},o().createElement("label",null,"Score for ",t.participant1Name||"Participant 1"),o().createElement("input",{type:"number",min:"0",value:p.participant1,onChange:function(e){return x("participant1",e.target.value)},disabled:h})),o().createElement("div",{className:"score-input"},o().createElement("label",null,"Score for ",t.participant2Name||"Participant 2"),o().createElement("input",{type:"number",min:"0",value:p.participant2,onChange:function(e){return x("participant2",e.target.value)},disabled:h}))),o().createElement("div",{className:"winner-selection"},o().createElement("h4",null,"Select Winner"),o().createElement("div",{className:"winner-buttons"},o().createElement("button",{className:"btn ".concat(l===t.participant1Id?"btn-cyber-primary":"btn-cyber-secondary"),onClick:function(){return u(t.participant1Id)},disabled:h},t.participant1Name||"Participant 1"),o().createElement("button",{className:"btn ".concat(l===t.participant2Id?"btn-cyber-primary":"btn-cyber-secondary"),onClick:function(){return u(t.participant2Id)},disabled:h},t.participant2Name||"Participant 2"))),v&&o().createElement("div",{className:"error-message"},v),"disputed"===t.status&&a?o().createElement("div",{className:"admin-resolve"},o().createElement("h4",null,"Admin Resolution"),o().createElement("button",{className:"btn btn-cyber-primary",onClick:S,disabled:h},h?"Resolving...":"Resolve Dispute")):o().createElement("button",{className:"btn btn-cyber-primary",onClick:k,disabled:h},h?"Submitting...":"Submit Result")):o().createElement("div",{className:"match-result-submission"},o().createElement("div",{className:"error-message"},"You must be a participant in this match to submit results")):o().createElement("div",{className:"match-result-submission"},o().createElement("div",{className:"warning-message"},o().createElement("h3",null,"Match Not Ready"),o().createElement("p",null,N.error),o().createElement("p",null,"Please wait for both participants to be confirmed before submitting results.")))},Nt=function(e){var t=e.winner;if(!t)return null;var n=function(e){return e?"".concat(e.slice(0,6),"...").concat(e.slice(-4)):""},r=t.name||n(t.id||t.walletAddress);return o().createElement("div",{className:"tournament-winner"},o().createElement("h2",null,"Tournament Champion"),o().createElement("div",{className:"winner-trophy"},"🏆"),o().createElement("div",{className:"winner-name"},r),t.walletAddress&&t.name&&o().createElement("div",{className:"winner-address"},n(t.walletAddress)))};function At(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}const Ot=function(e){var t,n,r=e.data,a=e.tournamentId,s=e.currentUserAddress,c=e.isAdmin,l=r.rounds,u=(r.matchesById,r.participants),d=(t=(0,i.useState)(null),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,a=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}(t,n)||function(e,t){if(e){if("string"==typeof e)return At(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?At(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),p=d[0],m=d[1];return o().createElement("div",{className:"bracket-container single-elimination"},o().createElement(Nt,{winner:function(){if(!l||0===l.length)return null;var e=l[l.length-1];if(!e||!e.matches||0===e.matches.length)return null;var t=e.matches[0];return t&&t.winnerId?u.find((function(e){return e.id===t.winnerId})):null}()}),o().createElement("div",{className:"bracket-content"},l.map((function(e,t){return o().createElement("div",{className:"round",key:e.id},o().createElement("div",{className:"round-header"},o().createElement("h3",null,e.name)),o().createElement("div",{className:"round-matches"},e.matches.map((function(e){return function(e){var t,n=u.find((function(t){return t.id===e.participant1Id})),r=u.find((function(t){return t.id===e.participant2Id})),a=s&&(e.participant1Id===s||e.participant2Id===s)&&("PENDING"===e.status.toUpperCase()||"ready"===e.status.toLowerCase())&&!(null!==(t=e.resultSubmissions)&&void 0!==t&&t.some((function(e){return e.submittedBy===s})));return o().createElement("div",{className:"match-container ".concat(e.status.toLowerCase()," ").concat((null==p?void 0:p.id)===e.id?"selected":""),key:e.id,onClick:function(){return m(e)}},o().createElement("div",{className:"match-header"},o().createElement("span",{className:"match-id"},e.identifier||e.id),e.matchFormat&&o().createElement("span",{className:"match-format"},e.matchFormat.toUpperCase()),"DISPUTED"===e.status.toUpperCase()&&o().createElement("span",{className:"dispute-badge"},"Disputed")),o().createElement("div",{className:"match-content"},o().createElement("div",{className:"participant ".concat(e.winnerId===e.participant1Id?"winner":"")},o().createElement("span",{className:"participant-name"},n?n.name:"TBD"),e.score&&o().createElement("span",{className:"score"},e.score.participant1)),o().createElement("div",{className:"participant ".concat(e.winnerId===e.participant2Id?"winner":"")},o().createElement("span",{className:"participant-name"},r?r.name:"TBD"),e.score&&o().createElement("span",{className:"score"},e.score.participant2))),a&&o().createElement("div",{className:"match-actions"},o().createElement("button",{className:"btn btn-cyber-secondary text-sm",onClick:function(t){t.stopPropagation(),m(e)}},"Submit Result")))}(e)}))))}))),p&&o().createElement("div",{className:"match-details-panel"},o().createElement("button",{className:"close-panel-btn",onClick:function(){return m(null)}},"×"),o().createElement(St,{match:p,tournamentId:a,currentUserAddress:s,isAdmin:c,bracketData:r})))};function Dt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}const _t=function(e){var t,n,r=e.data,a=e.tournamentId,s=e.currentUserAddress,c=e.isAdmin,l=r.rounds,u=r.matchesById,d=r.participants,p=(t=(0,i.useState)(null),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,a=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}(t,n)||function(e,t){if(e){if("string"==typeof e)return Dt(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Dt(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),m=p[0],f=p[1],h=function(e){var t,n=d.find((function(t){return t.id===e.participant1Id})),r=d.find((function(t){return t.id===e.participant2Id})),a=(d.find((function(t){return t.id===e.winnerId})),s&&(e.participant1Id===s||e.participant2Id===s)&&("PENDING"===e.status.toUpperCase()||"ready"===e.status.toLowerCase())&&!(null!==(t=e.resultSubmissions)&&void 0!==t&&t.some((function(e){return e.submittedBy===s}))));return o().createElement("div",{className:"match-container ".concat(e.status.toLowerCase()," ").concat(e.isUpperBracket?"upper-bracket":"lower-bracket"," ").concat((null==m?void 0:m.id)===e.id?"selected":""),key:e.id,onClick:function(){return f(e)}},o().createElement("div",{className:"match-header"},o().createElement("span",{className:"match-id"},e.identifier||e.id),e.matchFormat&&o().createElement("span",{className:"match-format"},e.matchFormat.toUpperCase()),"DISPUTED"===e.status.toUpperCase()&&o().createElement("span",{className:"dispute-badge"},"Disputed")),o().createElement("div",{className:"match-content"},o().createElement("div",{className:"participant ".concat(e.winnerId===e.participant1Id?"winner":"")},o().createElement("span",{className:"participant-name"},n?n.name:"TBD"),e.score&&o().createElement("span",{className:"score"},e.score.participant1)),o().createElement("div",{className:"participant ".concat(e.winnerId===e.participant2Id?"winner":"")},o().createElement("span",{className:"participant-name"},r?r.name:"TBD"),e.score&&o().createElement("span",{className:"score"},e.score.participant2))),a&&o().createElement("div",{className:"match-actions"},o().createElement("button",{className:"btn btn-cyber-secondary text-sm",onClick:function(t){t.stopPropagation(),f(e)}},"Submit Result")))},b=l.filter((function(e){return e.matches.some((function(e){return e.isUpperBracket}))})),g=l.filter((function(e){return e.matches.some((function(e){var t;return!(e.isUpperBracket||null!==(t=e.identifier)&&void 0!==t&&t.includes("Grand Final"))}))})),v=l.filter((function(e){return e.matches.some((function(e){var t;return null===(t=e.identifier)||void 0===t?void 0:t.includes("Grand Final")}))}));return o().createElement("div",{className:"bracket-container double-elimination"},o().createElement(Nt,{winner:function(){if(!l||0===l.length)return null;var e=Object.values(u).find((function(e){return"Grand Final"===e.identifier||"Grand Final Reset"===e.identifier}));return e&&e.winnerId?d.find((function(t){return t.id===e.winnerId})):null}()}),o().createElement("div",{className:"bracket-content"},o().createElement("div",{className:"bracket-section upper-bracket"},o().createElement("h2",null,"Winners Bracket"),b.map((function(e,t){return o().createElement("div",{className:"round",key:e.id},o().createElement("div",{className:"round-header"},o().createElement("h3",null,e.name)),o().createElement("div",{className:"round-matches"},e.matches.map((function(e){return h(e)}))))}))),o().createElement("div",{className:"bracket-section lower-bracket"},o().createElement("h2",null,"Losers Bracket"),g.map((function(e,t){return o().createElement("div",{className:"round",key:e.id},o().createElement("div",{className:"round-header"},o().createElement("h3",null,e.name)),o().createElement("div",{className:"round-matches"},e.matches.map((function(e){return h(e)}))))}))),o().createElement("div",{className:"bracket-section grand-finals"},o().createElement("h2",null,"Grand Final"),v.map((function(e,t){return o().createElement("div",{className:"round",key:e.id},o().createElement("div",{className:"round-header"},o().createElement("h3",null,e.name)),o().createElement("div",{className:"round-matches"},e.matches.map((function(e){return h(e)}))))})))),m&&o().createElement("div",{className:"match-details-panel"},o().createElement("button",{className:"close-panel-btn",onClick:function(){return f(null)}},"×"),o().createElement(St,{match:m,tournamentId:a,currentUserAddress:s,isAdmin:c,bracketData:r})))};function Ct(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}const Bt=function(e){var t,n,r=e.data,a=e.tournamentId,s=e.currentUserAddress,c=e.isAdmin,l=r.rounds,u=r.matchesById,d=r.participants,p=(t=(0,i.useState)(null),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,a=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}(t,n)||function(e,t){if(e){if("string"==typeof e)return Ct(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ct(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),m=p[0],f=p[1],h=function(){var e=d.map((function(e){return{id:e.id,name:e.name,wins:0,losses:0,points:0}}));return Object.values(u).forEach((function(t){if(t.winnerId){var n=e.find((function(e){return e.id===t.winnerId})),r=e.find((function(e){return e.id===t.loserId}));n&&(n.wins++,n.points+=3),r&&r.losses++}})),e.sort((function(e,t){return t.points!==e.points?t.points-e.points:t.wins-e.wins}))},b=h();return o().createElement("div",{className:"bracket-container round-robin"},o().createElement(Nt,{winner:function(){var e=h();if(!e||0===e.length)return null;var t=e[0];return t?d.find((function(e){return e.id===t.id})):null}()}),o().createElement("div",{className:"standings-section"},o().createElement("h2",null,"Standings"),o().createElement("table",{className:"standings-table"},o().createElement("thead",null,o().createElement("tr",null,o().createElement("th",null,"Rank"),o().createElement("th",null,"Player"),o().createElement("th",null,"W"),o().createElement("th",null,"L"),o().createElement("th",null,"Pts"))),o().createElement("tbody",null,b.map((function(e,t){return o().createElement("tr",{key:e.id},o().createElement("td",null,t+1),o().createElement("td",null,e.name),o().createElement("td",null,e.wins),o().createElement("td",null,e.losses),o().createElement("td",null,e.points))}))))),o().createElement("div",{className:"matches-section"},o().createElement("h2",null,"Matches"),l.map((function(e,t){return o().createElement("div",{className:"round",key:e.id},o().createElement("div",{className:"round-header"},o().createElement("h3",null,e.name)),o().createElement("div",{className:"round-matches"},e.matches.map((function(e){return function(e){var t,n=d.find((function(t){return t.id===e.participant1Id})),r=d.find((function(t){return t.id===e.participant2Id})),a=(d.find((function(t){return t.id===e.winnerId})),!1);s&&((a=e.participant1Id===s||e.participant2Id===s)||(n&&n.members&&Array.isArray(n.members)&&(a=n.members.includes(s)),!a&&r&&r.members&&Array.isArray(r.members)&&(a=r.members.includes(s))));var i=a&&("PENDING"===e.status.toUpperCase()||"ready"===e.status.toLowerCase())&&!(null!==(t=e.resultSubmissions)&&void 0!==t&&t.some((function(e){return e.submittedBy===s})));return o().createElement("div",{className:"match-container ".concat(e.status.toLowerCase()," ").concat((null==m?void 0:m.id)===e.id?"selected":""),key:e.id,onClick:function(){return f(e)}},o().createElement("div",{className:"match-header"},o().createElement("span",{className:"match-id"},e.id),e.matchFormat&&o().createElement("span",{className:"match-format"},e.matchFormat.toUpperCase()),"DISPUTED"===e.status.toUpperCase()&&o().createElement("span",{className:"dispute-badge"},"Disputed")),o().createElement("div",{className:"match-content"},o().createElement("div",{className:"participant ".concat(e.winnerId===e.participant1Id?"winner":"")},o().createElement("span",{className:"participant-name"},n?n.name:"TBD"),e.score&&o().createElement("span",{className:"score"},e.score.participant1)),o().createElement("div",{className:"participant ".concat(e.winnerId===e.participant2Id?"winner":"")},o().createElement("span",{className:"participant-name"},r?r.name:"TBD"),e.score&&o().createElement("span",{className:"score"},e.score.participant2))),i&&o().createElement("div",{className:"match-actions"},o().createElement("button",{className:"btn btn-cyber-secondary text-sm",onClick:function(t){t.stopPropagation(),f(e)}},"Submit Result")))}(e)}))))}))),m&&o().createElement("div",{className:"match-details-panel"},o().createElement("button",{className:"close-panel-btn",onClick:function(){return f(null)}},"×"),o().createElement(St,{match:m,tournamentId:a,currentUserAddress:s,isAdmin:c,bracketData:r})))};var Tt=a(560),jt={};function Pt(e){return Pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pt(e)}function Mt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mt(Object(n),!0).forEach((function(t){Lt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Lt(e,t,n){return(t=function(e){var t=function(e){if("object"!=Pt(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Pt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Pt(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ut(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}jt.styleTagTransform=w(),jt.setAttributes=b(),jt.insert=f().bind(null,"head"),jt.domAPI=p(),jt.insertStyleElement=v(),u()(Tt.A,jt),Tt.A&&Tt.A.locals&&Tt.A.locals;const Vt=function(e){var t,n,r=e.tournamentId,a=e.currentUserAddress,s=e.bracketData,c=e.tournamentData,l=(t=(0,i.useState)(s),n=2,function(e){if(Array.isArray(e))return e}(t)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,i,o,s=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,a=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw a}}return s}}(t,n)||function(e,t){if(e){if("string"==typeof e)return Ut(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ut(e,t):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),u=l[0],d=l[1];if((0,i.useEffect)((function(){d(s)}),[s]),(0,i.useEffect)((function(){console.log("BracketView mounted/updated:",{tournamentId:r,currentUserAddress:a,bracketDataFormat:null==s?void 0:s.format,hasRounds:!(null==s||!s.rounds),hasMatchesById:!(null==s||!s.matchesById),hasParticipants:!(null==s||!s.participants)})}),[r,a,s]),!u)return console.warn("BracketView: No bracket data available"),o().createElement("div",{className:"bracket-error"},"No bracket data available");if(!u.format)return console.error("BracketView: Missing format field in bracket data:",u),o().createElement("div",{className:"bracket-error"},"Invalid bracket data: missing format");var p=a===c.creatorAddress;switch(console.log("BracketView: Rendering bracket with format:",u.format),u.format){case"single-elimination":return console.log("BracketView: Rendering SingleEliminationBracket"),o().createElement(Ot,{data:u,tournamentId:r,currentUserAddress:a,isAdmin:p,onMatchUpdate:function(e){d((function(t){return Rt(Rt({},t),{},{matchesById:Rt(Rt({},t.matchesById),{},Lt({},e.id,e))})}))}});case"double-elimination":return console.log("BracketView: Rendering DoubleEliminationBracket"),o().createElement(_t,{data:u,tournamentId:r,currentUserAddress:a,isAdmin:p,onMatchUpdate:function(e){d((function(t){return Rt(Rt({},t),{},{matchesById:Rt(Rt({},t.matchesById),{},Lt({},e.id,e))})}))}});case"round-robin":return console.log("BracketView: Rendering RoundRobinBracket"),o().createElement(Bt,{data:u,tournamentId:r,currentUserAddress:a,isAdmin:p,onMatchUpdate:function(e){d((function(t){return Rt(Rt({},t),{},{matchesById:Rt(Rt({},t.matchesById),{},Lt({},e.id,e))})}))}});default:return console.error("BracketView: Unsupported bracket format:",u.format),console.error("BracketView: Available formats: single-elimination, double-elimination, round-robin"),console.error("BracketView: Full bracket data:",u),o().createElement("div",{className:"bracket-error"},"Unsupported bracket format: ",u.format)}};return window.React=o(),window.ReactDOM=c(),window.BracketView=function(e){return o().createElement(Vt,e)},{}})()));