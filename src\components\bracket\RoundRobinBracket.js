import React, { useState } from 'react';
import './BracketStyles.css';
import MatchResultSubmission from './MatchResultSubmission';
import TournamentWinner from './TournamentWinner';

const RoundRobinBracket = ({ data, tournamentId, currentUserAddress, isAdmin }) => {
    const { rounds, matchesById, participants } = data;
    const [selectedMatch, setSelectedMatch] = useState(null);

    const renderMatch = (match) => {
        const participant1 = participants.find(p => p.id === match.participant1Id);
        const participant2 = participants.find(p => p.id === match.participant2Id);
        const winner = participants.find(p => p.id === match.winnerId);
        // Check if current user is a participant (individual tournaments or team member)
        let isParticipant = false;
        if (currentUserAddress) {
            // Check direct participation (individual tournaments)
            isParticipant = match.participant1Id === currentUserAddress || match.participant2Id === currentUserAddress;

            // If not directly a participant, check team membership (team tournaments)
            if (!isParticipant) {
                // Check if user is a member of participant1's team
                if (participant1 && participant1.members && Array.isArray(participant1.members)) {
                    isParticipant = participant1.members.includes(currentUserAddress);
                }
                // Check if user is a member of participant2's team
                if (!isParticipant && participant2 && participant2.members && Array.isArray(participant2.members)) {
                    isParticipant = participant2.members.includes(currentUserAddress);
                }
            }
        }

        const canSubmitResult = isParticipant &&
            (match.status.toUpperCase() === 'PENDING' || match.status.toLowerCase() === 'ready') &&
            !match.resultSubmissions?.some(sub => sub.submittedBy === currentUserAddress);

        return (
            <div
                className={`match-container ${match.status.toLowerCase()} ${selectedMatch?.id === match.id ? 'selected' : ''}`}
                key={match.id}
                onClick={() => setSelectedMatch(match)}
            >
                <div className="match-header">
                    <span className="match-id">{match.id}</span>
                    {match.matchFormat && (
                        <span className="match-format">{match.matchFormat.toUpperCase()}</span>
                    )}
                    {match.status.toUpperCase() === 'DISPUTED' && (
                        <span className="dispute-badge">Disputed</span>
                    )}
                </div>
                <div className="match-content">
                    <div className={`participant ${match.winnerId === match.participant1Id ? 'winner' : ''}`}>
                        <span className="participant-name">
                            {participant1 ? participant1.name : 'TBD'}
                        </span>
                        {match.score && <span className="score">{match.score.participant1}</span>}
                    </div>
                    <div className={`participant ${match.winnerId === match.participant2Id ? 'winner' : ''}`}>
                        <span className="participant-name">
                            {participant2 ? participant2.name : 'TBD'}
                        </span>
                        {match.score && <span className="score">{match.score.participant2}</span>}
                    </div>
                </div>
                {canSubmitResult && (
                    <div className="match-actions">
                        <button
                            className="btn btn-cyber-secondary text-sm"
                            onClick={(e) => {
                                e.stopPropagation();
                                setSelectedMatch(match);
                            }}
                        >
                            Submit Result
                        </button>
                    </div>
                )}
            </div>
        );
    };

    // Create a standings table
    const calculateStandings = () => {
        const standings = participants.map(participant => ({
            id: participant.id,
            name: participant.name,
            wins: 0,
            losses: 0,
            points: 0
        }));

        // Calculate wins and losses
        Object.values(matchesById).forEach(match => {
            if (match.winnerId) {
                const winnerStanding = standings.find(s => s.id === match.winnerId);
                const loserStanding = standings.find(s => s.id === match.loserId);

                if (winnerStanding) {
                    winnerStanding.wins++;
                    winnerStanding.points += 3; // 3 points for a win
                }
                if (loserStanding) {
                    loserStanding.losses++;
                }
            }
        });

        // Sort by points, then wins
        return standings.sort((a, b) => {
            if (b.points !== a.points) return b.points - a.points;
            return b.wins - a.wins;
        });
    };

    const standings = calculateStandings();

    // Find the tournament winner
    const getTournamentWinner = () => {
        const standings = calculateStandings();
        if (!standings || standings.length === 0) return null;

        // In round robin, the winner is the participant with the most points
        const winner = standings[0];
        if (!winner) return null;

        return participants.find(p => p.id === winner.id);
    };

    return (
        <div className="bracket-container round-robin">
            <TournamentWinner winner={getTournamentWinner()} />
            <div className="standings-section">
                <h2>Standings</h2>
                <table className="standings-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Player</th>
                            <th>W</th>
                            <th>L</th>
                            <th>Pts</th>
                        </tr>
                    </thead>
                    <tbody>
                        {standings.map((standing, index) => (
                            <tr key={standing.id}>
                                <td>{index + 1}</td>
                                <td>{standing.name}</td>
                                <td>{standing.wins}</td>
                                <td>{standing.losses}</td>
                                <td>{standing.points}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            <div className="matches-section">
                <h2>Matches</h2>
                {rounds.map((round, roundIndex) => (
                    <div className="round" key={round.id}>
                        <div className="round-header">
                            <h3>{round.name}</h3>
                        </div>
                        <div className="round-matches">
                            {round.matches.map(match => renderMatch(match))}
                        </div>
                    </div>
                ))}
            </div>

            {selectedMatch && (
                <div className="match-details-panel">
                    <button
                        className="close-panel-btn"
                        onClick={() => setSelectedMatch(null)}
                    >
                        ×
                    </button>
                    <MatchResultSubmission
                        match={selectedMatch}
                        tournamentId={tournamentId}
                        currentUserAddress={currentUserAddress}
                        isAdmin={isAdmin}
                        bracketData={data}
                    />
                </div>
            )}
        </div>
    );
};

export default RoundRobinBracket;