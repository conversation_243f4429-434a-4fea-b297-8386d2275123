import { httpsCallable } from 'firebase/functions';

// Get the Firebase instance
const getDb = () => {
    if (!window.app) {
        throw new Error('Firebase is not initialized. Please ensure Firebase is properly set up.');
    }
    if (!window.currentUser) {
        throw new Error('User is not authenticated. Please connect your wallet first.');
    }

    return window.functions;
};

/**
 * Validates that both participants in a match are properly set and ready for result submission
 * @param {Object} match - The match object to validate
 * @param {Array} participants - Array of tournament participants
 * @returns {Object} Validation result with isValid boolean and error message if invalid
 */
export const validateMatchParticipants = (match, participants = []) => {
    // Check if match exists
    if (!match) {
        return { isValid: false, error: 'Match not found' };
    }

    // Check if both participant IDs are set
    if (!match.participant1Id || !match.participant2Id) {
        return { isValid: false, error: 'Both participants must be assigned before submitting results' };
    }

    // Check if match already has a result (check this before status to give more specific error)
    if (match.winnerId || match.status === 'completed') {
        return { isValid: false, error: 'Match result has already been submitted' };
    }

    // Check if either participant is a TBD placeholder
    if (match.participant1Id.startsWith('TBD_') || match.participant2Id.startsWith('TBD_')) {
        return { isValid: false, error: 'Cannot submit results while participants are still being determined (TBD)' };
    }

    // Check if match status allows result submission
    const validStatuses = ['PENDING', 'ready', 'pending'];
    if (!validStatuses.includes(match.status)) {
        return { isValid: false, error: `Cannot submit results for match with status: ${match.status}` };
    }

    // If participants array is provided, validate that both participants exist
    if (participants.length > 0) {
        const participant1Exists = participants.some(p => p.id === match.participant1Id);
        const participant2Exists = participants.some(p => p.id === match.participant2Id);

        if (!participant1Exists || !participant2Exists) {
            return { isValid: false, error: 'One or both participants are not valid tournament participants' };
        }
    }

    return { isValid: true };
};

/**
 * Validates that submitted match scores align with the tournament's "Best of X" format requirements
 * @param {Object} score - Score object with participant1 and participant2 scores
 * @param {string} winnerId - ID of the declared winner
 * @param {string} matchFormat - Match format (bo1, bo3, bo5, bo7)
 * @param {string} participant1Id - ID of participant 1
 * @param {string} participant2Id - ID of participant 2
 * @returns {Object} Validation result with isValid boolean and error message if invalid
 */
export const validateMatchFormatScore = (score, winnerId, matchFormat, participant1Id, participant2Id) => {
    // Check if required parameters are provided
    if (!score || typeof score.participant1 !== 'number' || typeof score.participant2 !== 'number') {
        return { isValid: false, error: 'Valid scores for both participants are required' };
    }

    if (!winnerId || !matchFormat || !participant1Id || !participant2Id) {
        return { isValid: false, error: 'Missing required match information for validation' };
    }

    // Normalize match format
    const format = matchFormat.toLowerCase();
    const validFormats = ['bo1', 'bo3', 'bo5', 'bo7'];

    if (!validFormats.includes(format)) {
        return { isValid: false, error: `Invalid match format: ${matchFormat}. Must be one of: ${validFormats.join(', ')}` };
    }

    const participant1Score = score.participant1;
    const participant2Score = score.participant2;
    const totalGames = participant1Score + participant2Score;

    // Extract max games from format (e.g., 'bo3' -> 3)
    const maxGames = parseInt(format.substring(2));
    const firstToWin = Math.ceil(maxGames / 2); // First to win majority

    // Check for tied scores first (no clear winner possible)
    if (participant1Score === participant2Score) {
        return { isValid: false, error: `Tied scores are not allowed in ${format.toUpperCase()} matches. One participant must have a clear majority.` };
    }

    // Determine winner based on scores
    const scoreBasedWinner = participant1Score > participant2Score ? participant1Id : participant2Id;

    // Check if declared winner matches score-based winner
    if (winnerId !== scoreBasedWinner) {
        return { isValid: false, error: 'Declared winner does not match the scores provided' };
    }

    // Validate based on match format
    switch (format) {
        case 'bo1':
            // Best of 1: Exactly 1 game, winner has 1, loser has 0
            if (totalGames !== 1) {
                return { isValid: false, error: 'Best of 1 matches must have exactly 1 game played' };
            }
            if (Math.max(participant1Score, participant2Score) !== 1 || Math.min(participant1Score, participant2Score) !== 0) {
                return { isValid: false, error: 'Best of 1 matches must have a score of 1-0' };
            }
            break;

        case 'bo3':
            // Best of 3: At least 2 games, winner has at least 2, max 3 total games
            if (totalGames < 2 || totalGames > 3) {
                return { isValid: false, error: 'Best of 3 matches must have 2-3 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 2) {
                return { isValid: false, error: 'Best of 3 matches require the winner to win at least 2 games' };
            }
            break;

        case 'bo5':
            // Best of 5: At least 3 games, winner has at least 3, max 5 total games
            if (totalGames < 3 || totalGames > 5) {
                return { isValid: false, error: 'Best of 5 matches must have 3-5 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 3) {
                return { isValid: false, error: 'Best of 5 matches require the winner to win at least 3 games' };
            }
            break;

        case 'bo7':
            // Best of 7: At least 4 games, winner has at least 4, max 7 total games
            if (totalGames < 4 || totalGames > 7) {
                return { isValid: false, error: 'Best of 7 matches must have 4-7 games played' };
            }
            if (Math.max(participant1Score, participant2Score) < 4) {
                return { isValid: false, error: 'Best of 7 matches require the winner to win at least 4 games' };
            }
            break;
    }

    // Additional validation: ensure winner has won the majority
    const winnerScore = winnerId === participant1Id ? participant1Score : participant2Score;
    if (winnerScore < firstToWin) {
        return { isValid: false, error: `Winner must have won at least ${firstToWin} games in a ${format.toUpperCase()} match` };
    }

    return { isValid: true };
};

/**
 * Updates a match with the result and handles bracket progression
 * @param {string} tournamentId - The ID of the tournament
 * @param {string} matchId - The ID of the match to update
 * @param {Object} result - The match result
 * @param {string} result.winnerId - The ID of the winning participant
 * @param {Object} result.score - The match score
 * @param {number} result.score.participant1 - Score for participant 1
 * @param {number} result.score.participant2 - Score for participant 2
 */
export const updateMatchResult = async (tournamentId, matchId, result) => {
    try {
        if (!window.app) {
            throw new Error('Firebase is not initialized. Please ensure Firebase is properly set up.');
        }
        if (!window.currentUserAddress) {
            throw new Error('Wallet address not found. Please connect your wallet first.');
        }

        const functions = window.functions;
        const updateMatchResultFn = httpsCallable(functions, 'updateMatchResult');
        
        const response = await updateMatchResultFn({
            tournamentId,
            matchId,
            result: {
                winnerId: result.winnerId,
                score: {
                    participant1: result.score.participant1,
                    participant2: result.score.participant2
                }
            },
            userWalletAddress: window.currentUserAddress
        });

        return response.data;
    } catch (error) {
        console.error('Error updating match result:', error);
        throw error;
    }
};

/**
 * Handles progression for single elimination brackets
 */
const handleSingleEliminationProgression = async (tournamentId, completedMatch) => {
    const db = getDb();
    const tournamentRef = db.collection('tournaments').doc(tournamentId);
    const tournamentDoc = await tournamentRef.get();
    const tournament = tournamentDoc.data();

    if (!tournament || !tournament.bracketData) {
        throw new Error('Tournament or bracket data not found');
    }

    const { matchesById } = tournament.bracketData;

    // Find the next match that needs to be updated
    const nextMatch = Object.values(matchesById).find(match => 
        match.status === 'pending' &&
        (match.participant1Id === completedMatch.id || match.participant2Id === completedMatch.id)
    );

    if (nextMatch) {
        // Update the next match with the winner
        if (nextMatch.participant1Id === completedMatch.id) {
            nextMatch.participant1Id = completedMatch.winnerId;
        } else {
            nextMatch.participant2Id = completedMatch.winnerId;
        }

        // Check if both participants are now set
        if (nextMatch.participant1Id && nextMatch.participant2Id) {
            nextMatch.status = 'ready';
        }

        // Update the tournament document with the modified bracketData
        await tournamentRef.update({
            bracketData: tournament.bracketData
        });
    }
};

/**
 * Handles progression for double elimination brackets
 */
const handleDoubleEliminationProgression = async (tournamentId, completedMatch) => {
    const db = getDb();
    const tournamentRef = db.collection('tournaments').doc(tournamentId);
    const tournamentDoc = await tournamentRef.get();
    const tournament = tournamentDoc.data();

    if (!tournament || !tournament.bracketData) {
        throw new Error('Tournament or bracket data not found');
    }

    const { matchesById } = tournament.bracketData;

    // Handle winner's bracket progression
    if (completedMatch.isUpperBracket) {
        const nextUpperMatch = Object.values(matchesById).find(match => 
            match.isUpperBracket && 
            match.status === 'pending' &&
            (match.participant1Id === completedMatch.id || match.participant2Id === completedMatch.id)
        );

        if (nextUpperMatch) {
            if (nextUpperMatch.participant1Id === completedMatch.id) {
                nextUpperMatch.participant1Id = completedMatch.winnerId;
            } else {
                nextUpperMatch.participant2Id = completedMatch.winnerId;
            }
        }

        // Handle loser's bracket progression
        const loserNextMatch = Object.values(matchesById).find(match => 
            !match.isUpperBracket && 
            match.status === 'pending' &&
            (match.participant1Id === completedMatch.id || match.participant2Id === completedMatch.id)
        );

        if (loserNextMatch) {
            if (loserNextMatch.participant1Id === completedMatch.id) {
                loserNextMatch.participant1Id = completedMatch.loserId;
            } else {
                loserNextMatch.participant2Id = completedMatch.loserId;
            }
        }
    } else {
        // Handle loser's bracket progression
        const nextLowerMatch = Object.values(matchesById).find(match => 
            !match.isUpperBracket && 
            match.status === 'pending' &&
            (match.participant1Id === completedMatch.id || match.participant2Id === completedMatch.id)
        );

        if (nextLowerMatch) {
            if (nextLowerMatch.participant1Id === completedMatch.id) {
                nextLowerMatch.participant1Id = completedMatch.winnerId;
            } else {
                nextLowerMatch.participant2Id = completedMatch.winnerId;
            }
        }
    }

    // Update match status to ready if both participants are set
    Object.values(matchesById).forEach(match => {
        if (match.status === 'pending' && match.participant1Id && match.participant2Id) {
            match.status = 'ready';
        }
    });

    // Update the tournament document with the modified bracketData
    await tournamentRef.update({
        bracketData: tournament.bracketData
    });
};

/**
 * Handles progression for round robin tournaments
 */
const handleRoundRobinProgression = async (tournamentId, completedMatch) => {
    const db = getDb();
    const tournamentRef = db.collection('tournaments').doc(tournamentId);
    const tournamentDoc = await tournamentRef.get();
    const tournament = tournamentDoc.data();

    if (!tournament || !tournament.bracketData) {
        throw new Error('Tournament or bracket data not found');
    }

    const { matchesById } = tournament.bracketData;

    // Check if all matches are completed
    const allMatchesCompleted = Object.values(matchesById).every(match => match.status === 'completed');
    
    if (allMatchesCompleted) {
        // Update tournament status to completed
        await tournamentRef.update({
            status: 'completed',
            completedAt: new Date().toISOString()
        });
    }
};

/**
 * Resolves a disputed match
 */
export const resolveDisputedMatch = async (tournamentId, matchId, resolution) => {
    try {
        const db = getDb();
        const tournamentRef = db.collection('tournaments').doc(tournamentId);
        const tournamentDoc = await tournamentRef.get();
        const tournament = tournamentDoc.data();

        if (!tournament || !tournament.bracketData) {
            throw new Error('Tournament or bracket data not found');
        }

        const match = tournament.bracketData.matchesById[matchId];
        if (!match) {
            throw new Error('Match not found in bracket data');
        }

        // Validate scores against match format requirements
        const matchFormat = match.matchFormat || tournament.bracketData?.metadata?.matchFormat || tournament.matchFormat || 'bo1';
        const formatValidation = validateMatchFormatScore(
            resolution.score,
            resolution.winnerId,
            matchFormat,
            resolution.participant1Id,
            resolution.participant2Id
        );

        if (!formatValidation.isValid) {
            throw new Error(formatValidation.error);
        }

        // Update match with resolution
        match.status = 'completed';
        match.winnerId = resolution.winnerId;
        match.loserId = resolution.winnerId === resolution.participant1Id ? resolution.participant2Id : resolution.participant1Id;
        match.score = resolution.score;
        match.resolvedBy = resolution.resolvedBy;
        match.resolvedAt = new Date().toISOString();

        // Update the tournament document with the modified bracketData
        await tournamentRef.update({
            bracketData: tournament.bracketData
        });

        // Handle bracket progression after resolution
        switch (resolution.tournamentFormat) {
            case 'single-elimination':
                await handleSingleEliminationProgression(tournamentId, match);
                break;
            case 'double-elimination':
                await handleDoubleEliminationProgression(tournamentId, match);
                break;
            case 'round-robin':
                await handleRoundRobinProgression(tournamentId, match);
                break;
        }

        return { success: true };
    } catch (error) {
        console.error('Error resolving disputed match:', error);
        throw error;
    }
}; 