{"name": "cyberfield", "version": "1.0.0", "description": "CyberField Tournament Platform", "main": "index.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "firebase": "^9.6.1"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@babel/preset-react": "^7.22.15", "babel-loader": "^9.1.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "style-loader": "^3.3.3", "css-loader": "^6.8.1"}}