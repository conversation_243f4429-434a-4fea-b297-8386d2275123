// Test Double Elimination with 4 players to verify structure
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== TESTING 4-PLAYER DOUBLE ELIMINATION ===\n');

const testParticipants = [
    { id: 'alpha', name: '<PERSON>', seed: 1 },
    { id: 'bravo', name: '<PERSON>', seed: 2 },
    { id: 'charlie', name: '<PERSON>', seed: 3 },
    { id: 'delta', name: '<PERSON>', seed: 4 }
];

try {
    console.log('Generating double elimination bracket for 4 players...');
    const bracket = generateBracket(testParticipants, 'double-elimination');
    
    console.log('\n=== BRACKET STRUCTURE ===');
    console.log('Format:', bracket.format);
    console.log('Participants:', bracket.participants.length);
    console.log('Rounds:', bracket.rounds.length);
    console.log('Total Matches:', Object.keys(bracket.matchesById).length);
    console.log('Metadata:', bracket.metadata);
    
    console.log('\n=== DETAILED ROUNDS BREAKDOWN ===');
    bracket.rounds.forEach((round, index) => {
        console.log(`\nRound ${index + 1}: ${round.name} (${round.id})`);
        console.log(`  Matches: ${round.matches.length}`);
        round.matches.forEach(match => {
            const p1Name = bracket.participants.find(p => p.id === match.participant1Id)?.name || match.participant1Id || 'TBD';
            const p2Name = bracket.participants.find(p => p.id === match.participant2Id)?.name || match.participant2Id || 'TBD';
            
            console.log(`    ${match.id}: ${p1Name} vs ${p2Name}`);
            console.log(`      Status: ${match.status}`);
            console.log(`      Upper Bracket: ${match.isUpperBracket}`);
            if (match.nextMatchId) {
                console.log(`      -> Next: ${match.nextMatchId}`);
            }
            if (match.nextLoserMatchId) {
                console.log(`      -> Loser: ${match.nextLoserMatchId}`);
            }
            if (match.identifier) {
                console.log(`      Identifier: ${match.identifier}`);
            }
        });
    });
    
    console.log('\n=== EXPECTED STRUCTURE FOR 4 PLAYERS ===');
    console.log('Upper Bracket:');
    console.log('  Round 1: 2 matches (Semi-Finals)');
    console.log('  Round 2: 1 match (Winners Final)');
    console.log('Lower Bracket:');
    console.log('  Round 1: 1 match (Losers Round 1)');
    console.log('  Round 2: 1 match (Losers Final)');
    console.log('Grand Finals:');
    console.log('  Round 1: 1 match (Grand Final)');
    console.log('  Round 2: 1 match (Grand Final Reset)');
    
    // Verify structure
    const upperRounds = bracket.rounds.filter(round => 
        round.matches.some(match => match.isUpperBracket)
    );
    const lowerRounds = bracket.rounds.filter(round => 
        round.matches.some(match => !match.isUpperBracket && !match.identifier?.includes('Grand Final'))
    );
    const grandFinalRounds = bracket.rounds.filter(round => 
        round.matches.some(match => match.identifier?.includes('Grand Final'))
    );
    
    console.log('\n=== STRUCTURE VERIFICATION ===');
    console.log(`Upper Bracket Rounds: ${upperRounds.length} (expected: 2)`);
    console.log(`Lower Bracket Rounds: ${lowerRounds.length} (expected: 2)`);
    console.log(`Grand Final Rounds: ${grandFinalRounds.length} (expected: 2)`);
    
    if (upperRounds.length === 2 && lowerRounds.length === 2 && grandFinalRounds.length === 2) {
        console.log('✅ Structure is correct!');
    } else {
        console.log('❌ Structure needs adjustment');
    }
    
} catch (error) {
    console.error('❌ Error generating bracket:', error.message);
    console.error(error.stack);
}
