# Team Sub-Bracket Architecture Design

## Overview
This document outlines the technical design for implementing sub-brackets in team tournaments, where individual team members face off in structured matches to determine the overall team winner.

## Current vs Proposed System

### Current System (Team as Black Box)
```javascript
// Current team match structure
{
  id: "R1M1",
  participant1Id: "team_alpha",  // Entire team
  participant2Id: "team_beta",   // Entire team
  winnerId: "team_alpha",        // Team wins, but HOW?
  status: "completed"
}
```

### Proposed System (Sub-Bracket Structure)
```javascript
// Enhanced team match with sub-bracket
{
  id: "R1M1",
  participant1Id: "team_alpha",
  participant2Id: "team_beta",
  winnerId: null,
  status: "pending",
  isTeamMatch: true,
  subBracket: {
    format: "best-of-majority", // or "best-of-3", "best-of-5"
    memberPairing: "random",    // or "seeded", "captain-first"
    individualMatches: [
      {
        id: "R1M1_SUB1",
        participant1Id: "0x1234...alpha_member1",
        participant2Id: "0x5678...beta_member1", 
        winnerId: null,
        status: "pending",
        parentMatchId: "R1M1",
        subMatchIndex: 0
      },
      {
        id: "R1M1_SUB2", 
        participant1Id: "0x2345...alpha_member2",
        participant2Id: "0x6789...beta_member2",
        winnerId: null,
        status: "pending", 
        parentMatchId: "R1M1",
        subMatchIndex: 1
      },
      {
        id: "R1M1_SUB3",
        participant1Id: "0x3456...alpha_member3", 
        participant2Id: "0x7890...beta_member3",
        winnerId: null,
        status: "pending",
        parentMatchId: "R1M1", 
        subMatchIndex: 2
      }
    ],
    teamResults: {
      team_alpha: { wins: 0, losses: 0 },
      team_beta: { wins: 0, losses: 0 }
    }
  }
}
```

## Implementation Components

### 1. Enhanced Bracket Generator
- Modify `bracketGenerator.js` to detect team tournaments
- Generate sub-brackets for each team vs team match
- Support different member pairing strategies

### 2. Sub-Bracket Match Creation
```javascript
function generateTeamSubBracket(teamMatch, team1, team2, options = {}) {
  const { memberPairing = 'random', format = 'best-of-majority' } = options;
  
  // Pair team members
  const memberPairs = pairTeamMembers(team1.members, team2.members, memberPairing);
  
  // Create individual matches
  const individualMatches = memberPairs.map((pair, index) => ({
    id: `${teamMatch.id}_SUB${index + 1}`,
    participant1Id: pair.team1Member,
    participant2Id: pair.team2Member,
    winnerId: null,
    status: 'pending',
    parentMatchId: teamMatch.id,
    subMatchIndex: index,
    matchFormat: teamMatch.matchFormat // Inherit from parent
  }));
  
  return {
    format,
    memberPairing,
    individualMatches,
    teamResults: {
      [team1.id]: { wins: 0, losses: 0 },
      [team2.id]: { wins: 0, losses: 0 }
    }
  };
}
```

### 3. Member Pairing Strategies
```javascript
function pairTeamMembers(team1Members, team2Members, strategy) {
  switch (strategy) {
    case 'random':
      return randomPairing(team1Members, team2Members);
    case 'seeded':
      return seededPairing(team1Members, team2Members);
    case 'captain-first':
      return captainFirstPairing(team1Members, team2Members);
    default:
      return randomPairing(team1Members, team2Members);
  }
}
```

### 4. Team Result Calculation
```javascript
function calculateTeamMatchResult(subBracket) {
  const { individualMatches, teamResults } = subBracket;
  
  // Count wins for each team
  individualMatches.forEach(match => {
    if (match.winnerId) {
      const winningTeam = getTeamForMember(match.winnerId);
      teamResults[winningTeam].wins++;
      
      const losingTeam = getTeamForMember(match.loserId);
      teamResults[losingTeam].losses++;
    }
  });
  
  // Determine team winner (majority wins)
  const teams = Object.keys(teamResults);
  const team1 = teams[0];
  const team2 = teams[1];
  
  if (teamResults[team1].wins > teamResults[team2].wins) {
    return { winnerId: team1, loserId: team2 };
  } else if (teamResults[team2].wins > teamResults[team1].wins) {
    return { winnerId: team2, loserId: team1 };
  }
  
  return null; // Tie - shouldn't happen with odd team sizes
}
```

## Database Schema Changes

### Tournament Document Structure
```javascript
// Enhanced tournament document
{
  // Existing fields...
  tournamentFormat: "2v2", // or "3v3"
  bracketFormat: "single-elim", // The actual bracket format
  teamSubBracketSettings: {
    memberPairing: "random",
    resultCalculation: "best-of-majority"
  },
  bracketData: {
    format: "single-elimination",
    participants: [...], // Teams
    rounds: [...],
    matchesById: {
      "R1M1": {
        // Enhanced team match with sub-bracket
        isTeamMatch: true,
        subBracket: { ... }
      }
    }
  }
}
```

## UI/UX Changes

### 1. Enhanced Bracket Display
- Show team matches with expandable sub-brackets
- Display individual member matchups
- Progress indicators for sub-bracket completion

### 2. Match Result Submission
- Allow submission of individual match results
- Auto-calculate team results when all sub-matches complete
- Show team progress in real-time

### 3. Tournament Creation
- Add sub-bracket configuration options
- Member pairing strategy selection
- Result calculation method selection

## Implementation Phases

### Phase 1: Core Sub-Bracket Logic
1. Enhance bracket generator for team sub-brackets
2. Implement member pairing algorithms
3. Create team result calculation logic
4. Add comprehensive tests

### Phase 2: Database Integration
1. Update Firestore schema
2. Modify tournament creation flow
3. Enhance match result processing
4. Update security rules

### Phase 3: UI Enhancement
1. Create sub-bracket display components
2. Enhance match result submission
3. Add tournament configuration options
4. Update admin controls

### Phase 4: Production Deployment
1. Feature flag implementation
2. Gradual rollout to new tournaments
3. Monitor existing tournaments
4. Performance optimization

## Backward Compatibility

### Existing Tournaments
- No changes to existing individual tournaments
- Existing team tournaments continue to work as before
- New sub-bracket feature only applies to newly created team tournaments

### Migration Strategy
- Add feature flag for sub-bracket functionality
- Default to legacy behavior for existing tournaments
- Allow tournament creators to opt-in to new system

## Testing Strategy

### Unit Tests
- Sub-bracket generation algorithms
- Member pairing strategies
- Team result calculation
- Edge cases (ties, byes, odd numbers)

### Integration Tests
- End-to-end tournament flow with sub-brackets
- Match result submission and progression
- Database consistency checks
- UI component interactions

### Production Testing
- Feature flags for controlled rollout
- A/B testing with tournament creators
- Performance monitoring
- User feedback collection

## Performance Considerations

### Database Operations
- Batch updates for sub-bracket results
- Efficient querying of nested match structures
- Indexing for sub-bracket queries

### UI Performance
- Lazy loading of sub-bracket details
- Optimized rendering for large team tournaments
- Caching of calculated results

## Security Considerations

### Access Control
- Verify team membership for sub-match participation
- Prevent unauthorized result submissions
- Maintain audit trail for all changes

### Data Validation
- Validate sub-bracket structure integrity
- Ensure consistent team result calculations
- Prevent manipulation of completed matches
