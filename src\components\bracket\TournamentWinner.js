import React from 'react';

const TournamentWinner = ({ winner }) => {
    if (!winner) return null;

    // Format the wallet address to be more readable
    const formatAddress = (address) => {
        if (!address) return '';
        return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };

    // Use the winner's name if available, otherwise format the wallet address
    const displayName = winner.name || formatAddress(winner.id || winner.walletAddress);

    return (
        <div className="tournament-winner">
            <h2>Tournament Champion</h2>
            <div className="winner-trophy">🏆</div>
            <div className="winner-name">{displayName}</div>
            {winner.walletAddress && winner.name && (
                <div className="winner-address">{formatAddress(winner.walletAddress)}</div>
            )}
        </div>
    );
};

export default TournamentWinner;