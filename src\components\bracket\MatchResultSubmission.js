import React, { useState, useEffect } from 'react';
import { updateMatchResult, resolveDisputedMatch, validateMatchParticipants, validateMatchFormatScore } from '../../services/matchManager';
import './BracketStyles.css';

const MatchResultSubmission = ({ match, tournamentId, currentUserAddress, isAdmin, bracketData }) => {
    const [selectedWinner, setSelectedWinner] = useState(null);
    const [scores, setScores] = useState({
        participant1: '',
        participant2: ''
    });
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState(null);
    const [isParticipant, setIsParticipant] = useState(false);

    useEffect(() => {
        // Verify if current user is a participant in this match using wallet address
        const checkParticipantStatus = () => {
            // Get current user's wallet address from global state
            const currentUserWalletAddress = window.currentUserAddress?.toLowerCase();

            if (!currentUserWalletAddress) {
                setIsParticipant(false);
                return;
            }

            // Check if user is directly a participant (individual tournaments)
            let isParticipantInMatch =
                currentUserWalletAddress === match.participant1Id?.toLowerCase() ||
                currentUserWalletAddress === match.participant2Id?.toLowerCase();

            // If not directly a participant, check if user is a team member (team tournaments)
            if (!isParticipantInMatch && bracketData && bracketData.participants) {
                const participant1 = bracketData.participants.find(p => p.id === match.participant1Id);
                const participant2 = bracketData.participants.find(p => p.id === match.participant2Id);

                // Check if current user is a member of either team
                if (participant1 && participant1.members && participant1.members.includes(currentUserWalletAddress)) {
                    isParticipantInMatch = true;
                }
                if (participant2 && participant2.members && participant2.members.includes(currentUserWalletAddress)) {
                    isParticipantInMatch = true;
                }
            }

            setIsParticipant(isParticipantInMatch);
        };

        checkParticipantStatus();
    }, [match.participant1Id, match.participant2Id, bracketData]);

    const handleScoreChange = (participant, value) => {
        setScores(prev => ({
            ...prev,
            [participant]: value
        }));
    };

    const handleSubmit = async () => {
        try {
            setSubmitting(true);
            setError(null);

            // Validate match participants before allowing submission
            const participantValidation = validateMatchParticipants(match, bracketData?.participants || []);
            if (!participantValidation.isValid) {
                throw new Error(participantValidation.error);
            }

            if (!isParticipant && !isAdmin) {
                throw new Error('You must be a participant in this match to submit results');
            }

            if (!selectedWinner) {
                throw new Error('Please select a winner');
            }

            if (!scores.participant1 || !scores.participant2) {
                throw new Error('Please enter scores for both participants');
            }

            const result = {
                winnerId: selectedWinner,
                score: {
                    participant1: parseInt(scores.participant1),
                    participant2: parseInt(scores.participant2)
                }
            };

            // Validate scores against match format requirements
            const matchFormat = match.matchFormat || bracketData?.metadata?.matchFormat || 'bo1';
            const formatValidation = validateMatchFormatScore(
                result.score,
                selectedWinner,
                matchFormat,
                match.participant1Id,
                match.participant2Id
            );

            if (!formatValidation.isValid) {
                throw new Error(formatValidation.error);
            }

            await updateMatchResult(tournamentId, match.id, result);
        } catch (error) {
            setError(error.message);
            console.error('Error updating match result:', error);
        } finally {
            setSubmitting(false);
        }
    };

    const handleAdminResolve = async () => {
        try {
            setSubmitting(true);
            setError(null);

            // Validate match participants before allowing admin resolution
            const participantValidation = validateMatchParticipants(match, bracketData?.participants || []);
            if (!participantValidation.isValid) {
                throw new Error(participantValidation.error);
            }

            if (!isAdmin) {
                throw new Error('Only administrators can resolve disputes');
            }

            if (!selectedWinner) {
                throw new Error('Please select a winner');
            }

            if (!scores.participant1 || !scores.participant2) {
                throw new Error('Please enter scores for both participants');
            }

            const resolution = {
                winnerId: selectedWinner,
                participant1Id: match.participant1Id,
                participant2Id: match.participant2Id,
                score: {
                    participant1: parseInt(scores.participant1),
                    participant2: parseInt(scores.participant2)
                },
                resolvedBy: window.currentUserAddress || currentUserAddress, // Use wallet address consistently
                tournamentFormat: match.tournamentFormat
            };

            // Validate scores against match format requirements
            const matchFormat = match.matchFormat || bracketData?.metadata?.matchFormat || 'bo1';
            const formatValidation = validateMatchFormatScore(
                resolution.score,
                selectedWinner,
                matchFormat,
                match.participant1Id,
                match.participant2Id
            );

            if (!formatValidation.isValid) {
                throw new Error(formatValidation.error);
            }

            await resolveDisputedMatch(tournamentId, match.id, resolution);
        } catch (error) {
            setError(error.message);
            console.error('Error resolving dispute:', error);
        } finally {
            setSubmitting(false);
        }
    };



    // Check if match is ready for result submission
    const participantValidation = validateMatchParticipants(match, bracketData?.participants || []);

    if (!participantValidation.isValid) {
        return (
            <div className="match-result-submission">
                <div className="warning-message">
                    <h3>Match Not Ready</h3>
                    <p>{participantValidation.error}</p>
                    <p>Please wait for both participants to be confirmed before submitting results.</p>
                </div>
            </div>
        );
    }

    if (!isParticipant && !isAdmin) {
        return (
            <div className="match-result-submission">
                <div className="error-message">
                    You must be a participant in this match to submit results
                </div>
            </div>
        );
    }

    return (
        <div className="match-result-submission">
            <h3>Submit Match Result</h3>

            <div className="score-inputs">
                <div className="score-input">
                    <label>Score for {match.participant1Name || 'Participant 1'}</label>
                    <input
                        type="number"
                        min="0"
                        value={scores.participant1}
                        onChange={(e) => handleScoreChange('participant1', e.target.value)}
                        disabled={submitting}
                    />
                </div>
                <div className="score-input">
                    <label>Score for {match.participant2Name || 'Participant 2'}</label>
                    <input
                        type="number"
                        min="0"
                        value={scores.participant2}
                        onChange={(e) => handleScoreChange('participant2', e.target.value)}
                        disabled={submitting}
                    />
                </div>
            </div>

            <div className="winner-selection">
                <h4>Select Winner</h4>
                <div className="winner-buttons">
                    <button
                        className={`btn ${selectedWinner === match.participant1Id ? 'btn-cyber-primary' : 'btn-cyber-secondary'}`}
                        onClick={() => setSelectedWinner(match.participant1Id)}
                        disabled={submitting}
                    >
                        {match.participant1Name || 'Participant 1'}
                    </button>
                    <button
                        className={`btn ${selectedWinner === match.participant2Id ? 'btn-cyber-primary' : 'btn-cyber-secondary'}`}
                        onClick={() => setSelectedWinner(match.participant2Id)}
                        disabled={submitting}
                    >
                        {match.participant2Name || 'Participant 2'}
                    </button>
                </div>
            </div>

            {error && (
                <div className="error-message">
                    {error}
                </div>
            )}

            {match.status === 'disputed' && isAdmin ? (
                <div className="admin-resolve">
                    <h4>Admin Resolution</h4>
                    <button
                        className="btn btn-cyber-primary"
                        onClick={handleAdminResolve}
                        disabled={submitting}
                    >
                        {submitting ? 'Resolving...' : 'Resolve Dispute'}
                    </button>
                </div>
            ) : (
                <button
                    className="btn btn-cyber-primary"
                    onClick={handleSubmit}
                    disabled={submitting}
                >
                    {submitting ? 'Submitting...' : 'Submit Result'}
                </button>
            )}
        </div>
    );
};

export default MatchResultSubmission;