// Test specifically for 3-participant single elimination bracket generation
const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== TESTING 3-PARTICIPANT SINGLE ELIMINATION FIX ===\n');

// Test with exactly 3 participants
const threeParticipants = [
    { id: '******************************************', name: '0x1234...5678', walletAddress: '******************************************', seed: 1 },
    { id: '******************************************', name: '0x2345...6789', walletAddress: '******************************************', seed: 2 },
    { id: '******************************************', name: '0x3456...78ab', walletAddress: '******************************************', seed: 3 }
];

try {
    console.log('Testing single elimination with 3 participants...');
    const bracketData = generateBracket(threeParticipants, 'single-elim', {
        matchFormat: 'bo1'
    });
    
    console.log('✅ 3-participant bracket generated successfully');
    console.log(`   Format: ${bracketData.format}`);
    console.log(`   Participants: ${bracketData.participants.length}`);
    console.log(`   Rounds: ${bracketData.rounds.length}`);
    console.log(`   Total matches: ${Object.keys(bracketData.matchesById).length}`);
    
    // Check if all participants are included
    const participantIds = bracketData.participants.map(p => p.id);
    const expectedIds = threeParticipants.map(p => p.id);
    const allIncluded = expectedIds.every(id => participantIds.includes(id));
    console.log(`   All participants included: ${allIncluded ? '✅' : '❌'}`);
    
    // Check bracket structure
    console.log('\n   Bracket Structure:');
    bracketData.rounds.forEach((round, index) => {
        console.log(`   Round ${index + 1} (${round.name}): ${round.matches.length} matches`);
        round.matches.forEach(match => {
            const p1 = match.participant1Id ? match.participant1Id.slice(0, 8) + '...' : 'TBD';
            const p2 = match.participant2Id ? match.participant2Id.slice(0, 8) + '...' : 'BYE';
            const status = match.status;
            const winner = match.winnerId ? match.winnerId.slice(0, 8) + '...' : 'TBD';
            console.log(`     ${match.id}: ${p1} vs ${p2} (${status}) Winner: ${winner}`);
        });
    });
    
    // Validate expected structure for 3 participants
    console.log('\n   Validation:');
    
    // Should have 2 rounds
    const expectedRounds = 2;
    const actualRounds = bracketData.rounds.length;
    console.log(`   Expected rounds: ${expectedRounds}, Actual: ${actualRounds} ${expectedRounds === actualRounds ? '✅' : '❌'}`);
    
    // Should have 2 total matches (1 in round 1, 1 in round 2)
    const expectedMatches = 2;
    const actualMatches = Object.keys(bracketData.matchesById).length;
    console.log(`   Expected matches: ${expectedMatches}, Actual: ${actualMatches} ${expectedMatches === actualMatches ? '✅' : '❌'}`);
    
    // Round 1 should have 1 match
    const round1Matches = bracketData.rounds[0].matches.length;
    console.log(`   Round 1 matches: Expected 1, Actual: ${round1Matches} ${round1Matches === 1 ? '✅' : '❌'}`);
    
    // Round 2 should have 1 match  
    const round2Matches = bracketData.rounds[1].matches.length;
    console.log(`   Round 2 matches: Expected 1, Actual: ${round2Matches} ${round2Matches === 1 ? '✅' : '❌'}`);
    
    // Check for bye handling
    const round1Match = bracketData.rounds[0].matches[0];
    const hasByeInRound1 = !round1Match.participant2Id || round1Match.status === 'BYE';
    console.log(`   Bye handling in Round 1: ${hasByeInRound1 ? '✅' : '❌'}`);
    
    // Summary
    const allTestsPassed = (
        allIncluded && 
        expectedRounds === actualRounds && 
        expectedMatches === actualMatches && 
        round1Matches === 1 && 
        round2Matches === 1
    );
    
    console.log(`\n🎯 Overall Result: ${allTestsPassed ? '✅ FIXED' : '❌ STILL BROKEN'}`);
    
    if (!allTestsPassed) {
        console.log('\n❌ The 3-participant bracket generation is still broken.');
        console.log('   Expected: Round 1 has 2 participants play, 1 gets bye to Round 2');
        console.log('   Expected: Round 2 has bye participant vs Round 1 winner');
    }
    
} catch (error) {
    console.log(`❌ Error generating 3-participant bracket: ${error.message}`);
    console.log('Stack:', error.stack);
}
