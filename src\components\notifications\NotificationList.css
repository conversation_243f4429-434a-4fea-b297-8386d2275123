/* Notification List Styles - Following <PERSON><PERSON><PERSON><PERSON> theme */

.notification-list {
    background: var(--bg-element-dark);
    border: 1px solid var(--border-cyber);
    border-radius: 0.5rem;
    overflow: hidden;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    font-family: 'Inter', sans-serif;
}

.notification-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-cyber);
    background: var(--bg-element-medium);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-header h3 {
    color: var(--accent-cyan);
    font-family: 'Orbitron', sans-serif;
    font-size: 1.2rem;
    margin: 0;
}

.notification-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.mark-all-read-btn,
.delete-read-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    white-space: nowrap;
}

.delete-read-btn {
    background: var(--bg-element-dark);
    border: 1px solid #dc3545;
    color: #dc3545;
    transition: all 0.3s ease;
}

.delete-read-btn:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.notification-loading,
.notification-error,
.notification-empty {
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-cyber);
    border-top: 2px solid var(--accent-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.notification-error p {
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.notification-empty {
    padding: 3rem 2rem;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    opacity: 0.7;
}

.notification-items {
    flex: 1;
    overflow-y: auto;
    max-height: calc(80vh - 80px);
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid var(--border-cyber);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:hover {
    background: var(--bg-element-medium);
    border-color: var(--accent-blue);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: rgba(var(--accent-blue-rgb), 0.05);
    border-left: 3px solid var(--accent-cyan);
}

.notification-item.unread:hover {
    background: rgba(var(--accent-blue-rgb), 0.1);
}

.notification-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.unread-dot {
    width: 6px;
    height: 6px;
    background: var(--accent-cyan);
    border-radius: 50%;
    flex-shrink: 0;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    word-wrap: break-word;
}

.notification-time {
    color: var(--text-secondary);
    font-size: 0.75rem;
    opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .notification-list {
        max-height: 70vh;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .notification-header {
        padding: 0.75rem;
    }

    .notification-header h3 {
        font-size: 1.1rem;
    }

    .mark-all-read-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .notification-item {
        padding: 0.75rem;
    }

    .notification-icon {
        font-size: 1.25rem;
        margin-right: 0.75rem;
    }

    .notification-title {
        font-size: 0.9rem;
    }

    .notification-message {
        font-size: 0.8rem;
    }

    .notification-items {
        max-height: calc(70vh - 70px);
    }
}

/* Scrollbar Styling */
.notification-items::-webkit-scrollbar {
    width: 6px;
}

.notification-items::-webkit-scrollbar-track {
    background: var(--bg-element-dark);
}

.notification-items::-webkit-scrollbar-thumb {
    background: var(--border-cyber);
    border-radius: 3px;
}

.notification-items::-webkit-scrollbar-thumb:hover {
    background: var(--accent-blue);
}

/* Animation for new notifications */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notification-item.new {
    animation: slideInFromTop 0.3s ease-out;
}
