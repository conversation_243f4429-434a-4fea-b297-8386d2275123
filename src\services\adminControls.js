import { doc, updateDoc, getDoc, collection, query, where, getDocs, deleteDoc } from 'firebase/firestore';

// Use the global Firebase instance
const db = window.db;

/**
 * Tournament Management Controls
 */

export const pauseTournament = async (tournamentId) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    await updateDoc(tournamentRef, {
        status: 'paused',
        pausedAt: new Date().toISOString()
    });
};

export const resumeTournament = async (tournamentId) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    await updateDoc(tournamentRef, {
        status: 'live',
        resumedAt: new Date().toISOString()
    });
};

export const forceStartTournament = async (tournamentId) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    await updateDoc(tournamentRef, {
        status: 'live',
        startedAt: new Date().toISOString()
    });
};

export const forceEndTournament = async (tournamentId) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    await updateDoc(tournamentRef, {
        status: 'completed',
        completedAt: new Date().toISOString()
    });
};

export const resetTournament = async (tournamentId) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    const matchesRef = collection(db, 'tournaments', tournamentId, 'matches');

    // Reset all matches
    const matchesSnapshot = await getDocs(matchesRef);
    const batch = db.batch();

    matchesSnapshot.docs.forEach(doc => {
        batch.update(doc.ref, {
            status: 'pending',
            winnerId: null,
            loserId: null,
            score: null,
            completedAt: null,
            resolvedAt: null,
            resolvedBy: null
        });
    });

    // Reset tournament status
    batch.update(tournamentRef, {
        status: 'upcoming',
        startedAt: null,
        completedAt: null,
        pausedAt: null,
        resumedAt: null
    });

    await batch.commit();
};

/**
 * Match Management Controls
 */

export const forceCompleteMatch = async (tournamentId, matchId, result) => {
    const matchRef = doc(db, 'tournaments', tournamentId, 'matches', matchId);
    await updateDoc(matchRef, {
        status: 'completed',
        winnerId: result.winnerId,
        loserId: result.loserId,
        score: result.score,
        completedAt: new Date().toISOString(),
        forceCompleted: true,
        forceCompletedBy: result.adminId
    });
};

export const resetMatch = async (tournamentId, matchId) => {
    const matchRef = doc(db, 'tournaments', tournamentId, 'matches', matchId);
    await updateDoc(matchRef, {
        status: 'pending',
        winnerId: null,
        loserId: null,
        score: null,
        completedAt: null,
        resolvedAt: null,
        resolvedBy: null,
        forceCompleted: false,
        forceCompletedBy: null
    });
};

export const overrideMatchResult = async (tournamentId, matchId, result) => {
    const matchRef = doc(db, 'tournaments', tournamentId, 'matches', matchId);
    await updateDoc(matchRef, {
        status: 'completed',
        winnerId: result.winnerId,
        loserId: result.loserId,
        score: result.score,
        completedAt: new Date().toISOString(),
        overridden: true,
        overriddenBy: result.adminId,
        originalResult: result.originalResult
    });
};

export const setMatchStatus = async (tournamentId, matchId, status) => {
    const matchRef = doc(db, 'tournaments', tournamentId, 'matches', matchId);
    await updateDoc(matchRef, {
        status,
        statusChangedAt: new Date().toISOString()
    });

    // If setting status to 'disputed', create notification for tournament creator
    if (status === 'disputed') {
        try {
            // Get tournament data to find creator
            const tournamentRef = doc(db, 'tournaments', tournamentId);
            const tournamentDoc = await getDoc(tournamentRef);

            if (tournamentDoc.exists()) {
                const tournament = tournamentDoc.data();
                const match = tournament.bracketData?.matchesById?.[matchId];

                if (match && tournament.creatorAddress && window.notificationService) {
                    // Use global notification service
                    await window.notificationService.createNotification(
                        tournament.creatorAddress,
                        'match_dispute',
                        'Match Dispute Requires Attention',
                        `A match in "${tournament.tournamentName}" has been disputed and needs admin review`,
                        {
                            tournamentId: tournamentId,
                            tournamentName: tournament.tournamentName,
                            matchId: matchId,
                            matchIdentifier: match.identifier || match.id,
                            participant1: match.participant1Name || match.participant1Id,
                            participant2: match.participant2Name || match.participant2Id
                        }
                    );
                }
            }
        } catch (error) {
            console.error('Error creating dispute notification:', error);
            // Don't throw - the main action (setting status) should still succeed
        }
    }
};

/**
 * Participant Management Controls
 */

export const addParticipant = async (tournamentId, participantWalletAddress) => {
    // Validate wallet address format
    if (!participantWalletAddress ||
        typeof participantWalletAddress !== 'string' ||
        participantWalletAddress.length !== 42 ||
        !participantWalletAddress.startsWith('0x')) {
        throw new Error('Invalid wallet address format');
    }

    const tournamentRef = doc(db, 'tournaments', tournamentId);
    const tournamentDoc = await getDoc(tournamentRef);
    const tournament = tournamentDoc.data();

    // Only allow adding participants before tournament goes live
    if (tournament.status !== 'upcoming') {
        throw new Error('Cannot add participants to a tournament that has already started or ended');
    }

    const normalizedAddress = participantWalletAddress.toLowerCase();

    // Check if participant already exists
    if (tournament.participants && tournament.participants.includes(normalizedAddress)) {
        throw new Error('Participant already exists in tournament');
    }

    // Add participant to tournament
    await updateDoc(tournamentRef, {
        participants: [...(tournament.participants || []), normalizedAddress],
        participantCount: (tournament.participantCount || 0) + 1
    });
};

/**
 * Validate and clean up tournament participants
 */
export const validateTournamentParticipants = async (tournamentId) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    const tournamentDoc = await getDoc(tournamentRef);
    const tournament = tournamentDoc.data();

    if (!tournament) {
        throw new Error('Tournament not found');
    }

    const participants = tournament.participants || [];
    const analysis = {
        total: participants.length,
        validWalletAddresses: [],
        firebaseUIDs: [],
        invalidEntries: []
    };

    participants.forEach(participant => {
        if (typeof participant === 'string') {
            if (participant.length === 42 && participant.startsWith('0x')) {
                analysis.validWalletAddresses.push(participant.toLowerCase());
            } else if (participant.length === 28) {
                // Likely Firebase UID
                analysis.firebaseUIDs.push(participant);
            } else {
                analysis.invalidEntries.push(participant);
            }
        } else {
            analysis.invalidEntries.push(participant);
        }
    });

    return analysis;
};

/**
 * Clean up tournament participants by removing non-wallet addresses
 */
export const cleanupTournamentParticipants = async (tournamentId, confirmCleanup = false) => {
    if (!confirmCleanup) {
        throw new Error('Cleanup must be explicitly confirmed');
    }

    const analysis = await validateTournamentParticipants(tournamentId);

    if (analysis.firebaseUIDs.length === 0 && analysis.invalidEntries.length === 0) {
        return { message: 'No cleanup needed', analysis };
    }

    const tournamentRef = doc(db, 'tournaments', tournamentId);
    await updateDoc(tournamentRef, {
        participants: analysis.validWalletAddresses,
        participantCount: analysis.validWalletAddresses.length
    });

    return {
        message: 'Cleanup completed',
        removed: analysis.firebaseUIDs.length + analysis.invalidEntries.length,
        remaining: analysis.validWalletAddresses.length,
        analysis
    };
};

export const removeParticipant = async (tournamentId, participantId) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    const tournamentDoc = await getDoc(tournamentRef);
    const tournament = tournamentDoc.data();

    // Remove participant from tournament
    await updateDoc(tournamentRef, {
        participants: tournament.participants.filter(p => p.id !== participantId)
    });

    // Handle participant's matches
    const matchesRef = collection(db, 'tournaments', tournamentId, 'matches');
    const matchesQuery = query(matchesRef,
        where('participant1Id', '==', participantId),
        where('participant2Id', '==', participantId)
    );
    const matchesSnapshot = await getDocs(matchesQuery);

    matchesSnapshot.docs.forEach(async doc => {
        const match = doc.data();
        if (match.status === 'pending') {
            // If match hasn't started, mark it as cancelled
            await updateDoc(doc.ref, {
                status: 'cancelled',
                cancelledAt: new Date().toISOString(),
                cancelledReason: 'participant_removed'
            });
        } else if (match.status === 'live') {
            // If match is in progress, handle based on tournament rules
            await handleInProgressMatchCancellation(doc.ref, participantId);
        }
    });
};

export const replaceParticipant = async (tournamentId, oldParticipantId, newParticipant) => {
    const tournamentRef = doc(db, 'tournaments', tournamentId);
    const tournamentDoc = await getDoc(tournamentRef);
    const tournament = tournamentDoc.data();

    // Replace participant in tournament
    await updateDoc(tournamentRef, {
        participants: tournament.participants.map(p =>
            p.id === oldParticipantId ? newParticipant : p
        )
    });

    // Update all matches involving the old participant
    const matchesRef = collection(db, 'tournaments', tournamentId, 'matches');
    const matchesQuery = query(matchesRef,
        where('participant1Id', '==', oldParticipantId),
        where('participant2Id', '==', oldParticipantId)
    );
    const matchesSnapshot = await getDocs(matchesQuery);

    matchesSnapshot.docs.forEach(async doc => {
        const match = doc.data();
        if (match.status === 'pending') {
            // Update participant in pending matches
            await updateDoc(doc.ref, {
                participant1Id: match.participant1Id === oldParticipantId ? newParticipant.id : match.participant1Id,
                participant2Id: match.participant2Id === oldParticipantId ? newParticipant.id : match.participant2Id,
                participantReplaced: true,
                replacedAt: new Date().toISOString(),
                oldParticipantId,
                newParticipantId: newParticipant.id
            });
        }
    });
};

export const handleNoShow = async (tournamentId, matchId, noShowParticipantId) => {
    const matchRef = doc(db, 'tournaments', tournamentId, 'matches', matchId);
    const matchDoc = await getDoc(matchRef);
    const match = matchDoc.data();

    const otherParticipantId = match.participant1Id === noShowParticipantId ?
        match.participant2Id : match.participant1Id;

    await updateDoc(matchRef, {
        status: 'completed',
        winnerId: otherParticipantId,
        loserId: noShowParticipantId,
        noShow: true,
        noShowParticipantId,
        completedAt: new Date().toISOString()
    });
};

/**
 * Helper function to handle in-progress match cancellation
 */
const handleInProgressMatchCancellation = async (matchRef, removedParticipantId) => {
    const matchDoc = await getDoc(matchRef);
    const match = matchDoc.data();

    const otherParticipantId = match.participant1Id === removedParticipantId ?
        match.participant2Id : match.participant1Id;

    await updateDoc(matchRef, {
        status: 'completed',
        winnerId: otherParticipantId,
        loserId: removedParticipantId,
        cancelled: true,
        cancelledAt: new Date().toISOString(),
        cancelledReason: 'participant_removed'
    });
};