.admin-control-panel {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00ff00;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    color: #fff;
}

.admin-control-panel h2 {
    color: #00ff00;
    font-family: 'Orbitron', sans-serif;
    margin-bottom: 20px;
    text-align: center;
}

.admin-control-panel h3 {
    color: #00ff00;
    font-family: 'Orbitron', sans-serif;
    margin: 15px 0;
    font-size: 1.2em;
}

.error-message {
    background: rgba(255, 0, 0, 0.2);
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.success-message {
    background: rgba(0, 255, 0, 0.2);
    border: 1px solid #00ff00;
    color: #00ff00;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.control-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin: 10px 0;
}

.btn {
    padding: 8px 16px;
    border: 1px solid #00ff00;
    background: transparent;
    color: #00ff00;
    font-family: 'Orbitron', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 4px;
}

.btn:hover {
    background: rgba(0, 255, 0, 0.2);
}

.btn-cyber-primary {
    background: rgba(0, 255, 0, 0.1);
}

.btn-cyber-secondary {
    background: transparent;
}

.btn-cyber-danger {
    border-color: #ff0000;
    color: #ff0000;
}

.btn-cyber-danger:hover {
    background: rgba(255, 0, 0, 0.2);
}

.participant-controls {
    margin: 20px 0;
}

.add-participant,
.replace-participant {
    display: flex;
    gap: 10px;
    margin: 10px 0;
    align-items: center;
}

input {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid #00ff00;
    color: #fff;
    padding: 8px;
    border-radius: 4px;
    font-family: 'Orbitron', sans-serif;
}

input:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
}

.selected-match-controls {
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid #00ff00;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.selected-match-controls h4 {
    color: #00ff00;
    margin-bottom: 10px;
    font-family: 'Orbitron', sans-serif;
}

@media (max-width: 768px) {
    .control-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    .add-participant,
    .replace-participant {
        flex-direction: column;
    }

    input {
        width: 100%;
    }
} 