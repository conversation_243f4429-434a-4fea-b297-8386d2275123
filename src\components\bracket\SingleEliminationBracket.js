import React, { useState } from 'react';
import './BracketStyles.css';
import MatchResultSubmission from './MatchResultSubmission';
import TournamentWinner from './TournamentWinner';

const SingleEliminationBracket = ({ data, tournamentId, currentUserAddress, isAdmin }) => {
    const { rounds, matchesById, participants } = data;
    const [selectedMatch, setSelectedMatch] = useState(null);

    // Find the tournament winner
    const getTournamentWinner = () => {
        if (!rounds || rounds.length === 0) return null;

        const finalRound = rounds[rounds.length - 1];
        if (!finalRound || !finalRound.matches || finalRound.matches.length === 0) return null;

        const finalMatch = finalRound.matches[0];
        if (!finalMatch || !finalMatch.winnerId) return null;

        return participants.find(p => p.id === finalMatch.winnerId);
    };

    const renderMatch = (match) => {
        const participant1 = participants.find(p => p.id === match.participant1Id);
        const participant2 = participants.find(p => p.id === match.participant2Id);
        const isParticipant = currentUserAddress &&
            (match.participant1Id === currentUserAddress || match.participant2Id === currentUserAddress);

        const canSubmitResult = isParticipant &&
            (match.status.toUpperCase() === 'PENDING' || match.status.toLowerCase() === 'ready') &&
            !match.resultSubmissions?.some(sub => sub.submittedBy === currentUserAddress);

        return (
            <div
                className={`match-container ${match.status.toLowerCase()} ${selectedMatch?.id === match.id ? 'selected' : ''}`}
                key={match.id}
                onClick={() => setSelectedMatch(match)}
            >
                <div className="match-header">
                    <span className="match-id">{match.identifier || match.id}</span>
                    {match.matchFormat && (
                        <span className="match-format">{match.matchFormat.toUpperCase()}</span>
                    )}
                    {match.status.toUpperCase() === 'DISPUTED' && (
                        <span className="dispute-badge">Disputed</span>
                    )}
                </div>
                <div className="match-content">
                    <div className={`participant ${match.winnerId === match.participant1Id ? 'winner' : ''}`}>
                        <span className="participant-name">
                            {participant1 ? participant1.name : 'TBD'}
                        </span>
                        {match.score && <span className="score">{match.score.participant1}</span>}
                    </div>
                    <div className={`participant ${match.winnerId === match.participant2Id ? 'winner' : ''}`}>
                        <span className="participant-name">
                            {participant2 ? participant2.name : 'TBD'}
                        </span>
                        {match.score && <span className="score">{match.score.participant2}</span>}
                    </div>
                </div>
                {canSubmitResult && (
                    <div className="match-actions">
                        <button
                            className="btn btn-cyber-secondary text-sm"
                            onClick={(e) => {
                                e.stopPropagation();
                                setSelectedMatch(match);
                            }}
                        >
                            Submit Result
                        </button>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="bracket-container single-elimination">
            <TournamentWinner winner={getTournamentWinner()} />
            <div className="bracket-content">
                {rounds.map((round, roundIndex) => (
                    <div className="round" key={round.id}>
                        <div className="round-header">
                            <h3>{round.name}</h3>
                        </div>
                        <div className="round-matches">
                            {round.matches.map(match => renderMatch(match))}
                        </div>
                    </div>
                ))}
            </div>

            {selectedMatch && (
                <div className="match-details-panel">
                    <button
                        className="close-panel-btn"
                        onClick={() => setSelectedMatch(null)}
                    >
                        ×
                    </button>
                    <MatchResultSubmission
                        match={selectedMatch}
                        tournamentId={tournamentId}
                        currentUserAddress={currentUserAddress}
                        isAdmin={isAdmin}
                        bracketData={data}
                    />
                </div>
            )}
        </div>
    );
};

export default SingleEliminationBracket;